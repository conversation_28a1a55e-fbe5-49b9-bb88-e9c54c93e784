(()=>{var e={};e.id=769,e.ids=[769],e.modules={2599:(e,t,r)=>{Promise.resolve().then(r.bind(r,48716))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7178:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>l});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),o=r(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let l={children:["",{children:["auth",{children:["twitter",{children:["callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39470)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\callback\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\callback\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/twitter/callback/page",pathname:"/auth/twitter/callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39470:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\auth\\\\twitter\\\\callback\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\callback\\page.tsx","default")},48716:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),n=r(43210),a=r(16189),i=r(88606),o=r(74265);function c(){let e=(0,a.useRouter)(),t=(0,a.useSearchParams)(),[r,c]=(0,n.useState)("loading"),[l,u]=(0,n.useState)(""),[d,h]=(0,n.useState)(null);return(0,n.useCallback)(async()=>{try{if(!o.y.isAuthenticated()){c("error"),u("You must be logged in to connect your Twitter account"),setTimeout(()=>e.push("/"),3e3);return}let r=t.get("oauth_token"),s=t.get("oauth_verifier");if(t.get("denied")){c("error"),u("Twitter authorization was denied"),setTimeout(()=>e.push("/settings"),3e3);return}if(!r||!s){c("error"),u("Missing OAuth parameters in callback URL"),setTimeout(()=>e.push("/settings"),3e3);return}let n=sessionStorage.getItem("twitter_oauth_token_secret");if(!n){c("error"),u("OAuth token secret not found. Please restart the authentication process."),setTimeout(()=>e.push("/settings"),3e3);return}let a=await i.b.handleTwitterCallback({oauth_token:r,oauth_verifier:s,oauth_token_secret:n});sessionStorage.removeItem("twitter_oauth_token_secret"),sessionStorage.removeItem("twitter_oauth_token"),c("success"),u(a.message),h(a.twitter_username||null),setTimeout(()=>e.push("/settings"),2e3)}catch(t){c("error"),u(t instanceof Error?t.message:"Failed to connect Twitter account"),setTimeout(()=>e.push("/settings"),3e3)}},[t,e]),(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(()=>{switch(r){case"loading":return(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"});case"success":return(0,s.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})});case"error":return(0,s.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}})()}),(0,s.jsx)("h2",{className:`text-2xl font-bold ${(()=>{switch(r){case"loading":return"text-blue-600";case"success":return"text-green-600";case"error":return"text-red-600"}})()}`,children:(()=>{switch(r){case"loading":return"Connecting Twitter Account...";case"success":return"Twitter Connected Successfully!";case"error":return"Connection Failed"}})()}),(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsx)("p",{className:"text-gray-600",children:l}),"success"===r&&d&&(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Connected as ",(0,s.jsxs)("span",{className:"font-medium text-twitter-blue",children:["@",d]})]})]}),"loading"===r&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full animate-bounce"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Please wait while we connect your account..."})]}),"success"===r&&(0,s.jsx)("div",{className:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),(0,s.jsx)("span",{className:"text-sm text-green-800",children:"You can now create and schedule tweets from Reachly!"})]})}),"error"===r&&(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)("button",{onClick:()=>e.push("/settings"),className:"w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Go to Settings"})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["loading"===r&&"This may take a few seconds...","success"===r&&"Redirecting to settings...","error"===r&&"Redirecting in a few seconds..."]})})]})})})}function l(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-blue-600",children:"Loading..."}),(0,s.jsx)("p",{className:"text-gray-600 mt-4",children:"Processing Twitter authentication..."})]})})})}function u(){return(0,s.jsx)(n.Suspense,{fallback:(0,s.jsx)(l,{}),children:(0,s.jsx)(c,{})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86364:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},88606:(e,t,r)=>{"use strict";r.d(t,{b:()=>a});var s=r(24731);class n{async initiateTwitterAuth(){try{return await s.uE.get("/auth/twitter/login")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to initiate Twitter auth: ${e}`)}}async handleTwitterCallback(e){try{return await s.uE.post("/auth/twitter/callback",e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to handle Twitter callback: ${e}`)}}async getTwitterStatus(){try{return await s.uE.get("/auth/twitter/status")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to get Twitter status: ${e}`)}}async disconnectTwitter(){try{return await s.uE.delete("/auth/twitter/disconnect")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to disconnect Twitter: ${e}`)}}async startTwitterOAuth(){let e=await this.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret);let t=window.open(e.authorization_url,"twitter_auth","width=600,height=600,scrollbars=yes,resizable=yes");return new Promise((r,s)=>{let n=setInterval(()=>{t?.closed&&(clearInterval(n),s(Error("Twitter authorization was cancelled")))},1e3),a=i=>{i.origin===window.location.origin&&("TWITTER_AUTH_SUCCESS"===i.data.type?(clearInterval(n),window.removeEventListener("message",a),t?.close(),r({oauth_token:e.oauth_token,oauth_token_secret:e.oauth_token_secret})):"TWITTER_AUTH_ERROR"===i.data.type&&(clearInterval(n),window.removeEventListener("message",a),t?.close(),s(Error(i.data.error||"Twitter authorization failed"))))};window.addEventListener("message",a)})}async completeTwitterOAuth(e){let t=sessionStorage.getItem("twitter_oauth_token_secret");if(!t)throw Error("OAuth token secret not found. Please restart the authentication process.");let r=new URLSearchParams(window.location.search).get("oauth_token");if(!r)throw Error("OAuth token not found in callback URL");let s=await this.handleTwitterCallback({oauth_token:r,oauth_verifier:e,oauth_token_secret:t});return sessionStorage.removeItem("twitter_oauth_token_secret"),s}}let a=new n},94351:(e,t,r)=>{Promise.resolve().then(r.bind(r,39470))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,786,658,70],()=>r(7178));module.exports=s})();