# 🔧 Render Environment Variables Fix - Twitter OAuth 404 Errors

## ❌ **Current Problem**

**Error in Browser Console:**
```
Failed to load resource: the server responded with a status of 404 ()
reachly-backend/auth/oauth2/twitter/init:1
```

**Root Cause**: Front<PERSON> is trying to call `reachly-backend/auth/oauth2/twitter/init` instead of the full backend URL.

## 🔍 **Diagnosis**

The issue is that `NEXT_PUBLIC_API_URL` environment variable is not properly set in your Render frontend service, so it's defaulting to `http://localhost:8000` or getting a malformed URL.

## ✅ **Step-by-Step Fix**

### **1. Check Your Current Deployment URLs**

After deploying to Render, you should have URLs like:
- **Backend**: `https://reachly-backend-xyz.onrender.com`
- **Frontend**: `https://reachly-frontend-abc.onrender.com`

### **2. Update Frontend Environment Variables**

**Go to Render Dashboard:**
1. Navigate to your **Frontend Service** (`reachly-frontend`)
2. Click **Environment** tab
3. Add/Update these variables:

```bash
# CRITICAL: Set the correct backend URL
NEXT_PUBLIC_API_URL=https://reachly-backend-xyz.onrender.com

# Optional: Set Node environment
NODE_ENV=production
```

**⚠️ IMPORTANT**: Replace `reachly-backend-xyz.onrender.com` with your actual backend URL!

### **3. Update Backend Environment Variables**

**Go to your Backend Service** (`reachly-backend`):
1. Click **Environment** tab
2. Add/Update:

```bash
# Set the correct frontend URL for CORS
FRONTEND_URL=https://reachly-frontend-abc.onrender.com

# Update Twitter OAuth redirect URI
TWITTER_OAUTH_REDIRECT_URI=https://reachly-frontend-abc.onrender.com/auth/twitter/oauth2-callback

# Your Twitter API credentials
TWITTER_CLIENT_ID=your_actual_twitter_client_id
TWITTER_CLIENT_SECRET=your_actual_twitter_client_secret
TWITTER_BEARER_TOKEN=your_actual_twitter_bearer_token
TWITTER_API_KEY=your_actual_twitter_api_key
TWITTER_API_SECRET=your_actual_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_actual_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_actual_twitter_access_token_secret

# OpenAI API key
OPENAI_API_KEY=your_actual_openai_api_key

# Database and other settings (should be auto-configured)
DATABASE_URL=<from database service>
SECRET_KEY=<auto-generated>
DEBUG=false
```

### **4. Redeploy Both Services**

**After updating environment variables:**
1. **Backend Service**: Manual Deploy → Deploy latest commit
2. **Frontend Service**: Manual Deploy → Deploy latest commit
3. **Wait for both deployments to complete**

### **5. Update Twitter App Settings**

**Go to Twitter Developer Portal:**
1. Visit [developer.twitter.com](https://developer.twitter.com)
2. Select your app
3. Go to "App settings" → "Authentication settings"
4. Update **Callback URLs**:
   ```
   https://reachly-frontend-abc.onrender.com/auth/twitter/oauth2-callback
   ```
5. **Save changes**

## 🔍 **Verification Steps**

### **1. Check Environment Variables**

**Frontend Console (after deployment):**
- Open browser console on your frontend
- Look for: `🔧 API Configuration:` log
- Should show: `BASE_URL: "https://reachly-backend-xyz.onrender.com"`

### **2. Test API Endpoints**

**Backend Health Check:**
```
https://reachly-backend-xyz.onrender.com/health
```
Should return: `{"status": "healthy"}`

**Backend API Docs:**
```
https://reachly-backend-xyz.onrender.com/docs
```
Should show FastAPI documentation

**Twitter OAuth Init Endpoint:**
```
https://reachly-backend-xyz.onrender.com/auth/oauth2/twitter/init
```
Should return JSON (not 404)

### **3. Test Twitter OAuth Flow**

1. **Go to your frontend**: `https://reachly-frontend-abc.onrender.com`
2. **Click "Continue with Twitter"**
3. **Check browser console** - should NOT see 404 errors
4. **Should redirect to Twitter** for authentication

## 🐛 **Common Issues & Fixes**

### **Issue 1: Still Getting 404 Errors**

**Check:**
- Environment variables are saved correctly
- Both services have been redeployed
- URLs don't have trailing slashes
- Backend service is actually running

### **Issue 2: CORS Errors**

**Fix:**
- Ensure `FRONTEND_URL` is set correctly in backend
- Redeploy backend service
- Check backend logs for CORS configuration

### **Issue 3: Twitter OAuth Redirect Errors**

**Fix:**
- Verify `TWITTER_OAUTH_REDIRECT_URI` matches Twitter app settings exactly
- Ensure Twitter app callback URL is updated
- Check for typos in URLs

### **Issue 4: Environment Variables Not Taking Effect**

**Fix:**
- Click "Manual Deploy" after updating variables
- Wait for deployment to complete
- Check service logs for any errors

## 📋 **Quick Checklist**

- [ ] Backend deployed and running
- [ ] Frontend deployed and running
- [ ] `NEXT_PUBLIC_API_URL` set to correct backend URL
- [ ] `FRONTEND_URL` set to correct frontend URL in backend
- [ ] `TWITTER_OAUTH_REDIRECT_URI` set correctly
- [ ] Twitter app callback URL updated
- [ ] Both services redeployed after environment variable changes
- [ ] API endpoints return 200 (not 404)
- [ ] Browser console shows correct API configuration

## 🎯 **Expected Result**

After fixing environment variables:
- ✅ **No 404 errors** in browser console
- ✅ **API calls work** to correct backend URL
- ✅ **Twitter OAuth starts** without errors
- ✅ **Redirects to Twitter** for authentication
- ✅ **Full authentication flow** works

---

## 🚨 **Critical Note**

**The most common issue is forgetting to redeploy services after updating environment variables.** 

**Always redeploy both frontend and backend services after making environment variable changes!**

**Your Twitter OAuth should work perfectly after these fixes!** 🚀
