# 🚀 Reachly Render Deployment - Simplified Guide

## 📋 Quick Deployment Steps

### **Option 1: Use Simplified Blueprint (Recommended)**

1. **Go to Render Dashboard**
   - Visit [render.com/dashboard](https://render.com/dashboard)
   - Click "New" → "Blueprint"

2. **Connect Repository**
   - Select "Connect a repository"
   - Choose your GitHub repository: `AutoReachX/MonoRepo`

3. **Use Simplified Blueprint**
   - Blueprint file: `render-simple.yaml`
   - Click "Apply" to start deployment

### **Option 2: Manual Deployment (If Blueprint Fails)**

#### **1. Deploy Database First**
- New → PostgreSQL
- Name: `reachly-db`
- Database Name: `reachly`
- User: `reachly_user`
- Plan: Free

#### **2. Deploy Backend Service**
- New → Web Service
- Connect repository: `AutoReachX/MonoRepo`
- Name: `reachly-backend`
- Runtime: Python 3
- Root Directory: `backend`
- Build Command: `pip install -r requirements.txt`
- Start Command: `python start_server.py`

**Backend Environment Variables:**
```
DATABASE_URL=<from database service>
SECRET_KEY=<auto-generate>
DEBUG=false
TWITTER_CLIENT_ID=<your-twitter-client-id>
TWITTER_CLIENT_SECRET=<your-twitter-client-secret>
TWITTER_BEARER_TOKEN=<your-twitter-bearer-token>
TWITTER_API_KEY=<your-twitter-api-key>
TWITTER_API_SECRET=<your-twitter-api-secret>
TWITTER_ACCESS_TOKEN=<your-twitter-access-token>
TWITTER_ACCESS_TOKEN_SECRET=<your-twitter-access-token-secret>
TWITTER_OAUTH_REDIRECT_URI=https://your-frontend-url.onrender.com/auth/twitter/oauth2-callback
OPENAI_API_KEY=<your-openai-api-key>
```

#### **3. Deploy Frontend Service**
- New → Web Service
- Connect repository: `AutoReachX/MonoRepo`
- Name: `reachly-frontend`
- Runtime: Node
- Root Directory: `frontend`
- Build Command: `npm ci && npm run build`
- Start Command: `npm start`

**Frontend Environment Variables:**
```
NEXT_PUBLIC_API_URL=https://your-backend-url.onrender.com
```

## ⚙️ Post-Deployment Configuration

### **1. Update Service URLs**

After deployment, you'll get URLs like:
- Backend: `https://reachly-backend.onrender.com`
- Frontend: `https://reachly-frontend.onrender.com`

**Update Backend Environment Variables:**
- `TWITTER_OAUTH_REDIRECT_URI` = `https://reachly-frontend.onrender.com/auth/twitter/oauth2-callback`

**Update Frontend Environment Variables:**
- `NEXT_PUBLIC_API_URL` = `https://reachly-backend.onrender.com`

### **2. Update Twitter OAuth Settings**

1. Go to [developer.twitter.com](https://developer.twitter.com)
2. Select your app → "App settings" → "Authentication settings"
3. Update **Callback URLs**:
   ```
   https://reachly-frontend.onrender.com/auth/twitter/oauth2-callback
   ```

### **3. Redeploy Services**

After updating environment variables:
1. Go to each service in Render dashboard
2. Click "Manual Deploy" → "Deploy latest commit"

## ✅ Testing Your Deployment

### **Health Checks**
- Backend: `https://reachly-backend.onrender.com/health`
- Frontend: `https://reachly-frontend.onrender.com`
- API Docs: `https://reachly-backend.onrender.com/docs`

### **Functionality Tests**
- [ ] Site loads without errors
- [ ] Twitter OAuth login works
- [ ] User can authenticate and access dashboard
- [ ] Content generation works (if OpenAI key is configured)

## 🔧 Optional: Add Redis Later

If you need Redis caching:

1. **Create Redis Service**
   - New → Redis
   - Name: `reachly-redis`
   - Plan: Free

2. **Update Backend Environment**
   - Add: `REDIS_URL=<from redis service>`

3. **Redeploy Backend**
   - Manual Deploy → Deploy latest commit

## 🐛 Troubleshooting

### **Common Issues**

**Build Failures:**
- Check build logs in Render dashboard
- Verify `rootDir` is set correctly
- Ensure all dependencies are in requirements.txt/package.json

**Environment Variable Issues:**
- Verify all required variables are set
- Check for typos in variable names
- Ensure URLs don't have trailing slashes

**OAuth Errors:**
- Verify redirect URI matches exactly
- Check Twitter app settings
- Ensure all Twitter credentials are correct

### **Performance Notes**

- **Free tier services sleep after 15 minutes**
- **First request after sleep takes 30+ seconds**
- **Consider upgrading to paid plans for production**

## 📞 Support

- [Render Documentation](https://render.com/docs)
- [Render Community](https://community.render.com)

---

**🎉 Your simplified deployment should work smoothly without the IP allow list issues!**
