# 🔧 Environment Variables Configuration Guide

## 📋 **Frontend Environment Variables**

### **Local Development (.env.local)**
```bash
# For local development - points to production backend
NEXT_PUBLIC_API_URL=https://reachly-backend.onrender.com
```

### **Production (.env.production)**
```bash
# For production deployment
NEXT_PUBLIC_API_URL=https://reachly-backend.onrender.com
NODE_ENV=production
```

### **Render Frontend Service Environment**
**Set these in Render Dashboard → Frontend Service → Environment:**
```bash
NEXT_PUBLIC_API_URL=https://reachly-backend.onrender.com
NODE_ENV=production
```

## 🔧 **Backend Environment Variables**

### **Render Backend Service Environment**
**Set these in Render Dashboard → Backend Service → Environment:**

#### **Database & Core**
```bash
DATABASE_URL=<auto-generated from database service>
SECRET_KEY=<auto-generated by Render>
DEBUG=false
NODE_ENV=production
```

#### **Frontend Integration**
```bash
FRONTEND_URL=https://reachly-frontend.onrender.com
```

#### **Twitter API Credentials**
```bash
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret
TWITTER_OAUTH_REDIRECT_URI=https://reachly-frontend.onrender.com/auth/twitter/oauth2-callback
```

#### **OpenAI Integration**
```bash
OPENAI_API_KEY=your_openai_api_key
```

## 🚀 **Deployment Checklist**

### **Before Deployment**
- [ ] All environment variables set in Render dashboard
- [ ] Twitter app callback URL updated
- [ ] API credentials are valid and active

### **After Deployment**
- [ ] Frontend shows correct API URL in console
- [ ] Backend health check works
- [ ] Twitter OAuth flow completes successfully

## 🔍 **Verification Steps**

### **Frontend API Configuration**
**Check browser console for:**
```javascript
🔧 API Configuration: {
  BASE_URL: 'https://reachly-backend.onrender.com',
  NEXT_PUBLIC_API_URL: 'https://reachly-backend.onrender.com',
  NODE_ENV: 'production'
}
```

### **Backend Health Check**
**Test these endpoints:**
```bash
# Health check
https://reachly-backend.onrender.com/health

# API documentation
https://reachly-backend.onrender.com/docs

# Twitter OAuth debug
https://reachly-backend.onrender.com/api/auth/oauth2/twitter/debug
```

### **Twitter OAuth Flow**
1. **Init**: `POST https://reachly-backend.onrender.com/api/auth/oauth2/twitter/init`
2. **Callback**: `POST https://reachly-backend.onrender.com/api/auth/oauth2/twitter/callback`

## 🐛 **Common Issues**

### **Issue: API calls go to wrong URL**
**Symptom**: Calls to `https://reachly-frontend.onrender.com/reachly-backend/...`
**Fix**: Update `NEXT_PUBLIC_API_URL` to full backend URL

### **Issue: CORS errors**
**Symptom**: Cross-origin request blocked
**Fix**: Ensure `FRONTEND_URL` is set correctly in backend

### **Issue: Twitter OAuth fails**
**Symptom**: "invalid_request" or redirect errors
**Fix**: Verify `TWITTER_OAUTH_REDIRECT_URI` matches Twitter app settings

## 📞 **Support**

- **Render Environment Variables**: [Render Docs](https://render.com/docs/environment-variables)
- **Next.js Environment Variables**: [Next.js Docs](https://nextjs.org/docs/basic-features/environment-variables)

---

**🎯 Remember: Always redeploy services after updating environment variables!**
