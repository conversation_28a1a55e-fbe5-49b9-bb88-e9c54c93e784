(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{966:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(5155);t(2115);var l=t(8794),a=t(7864),n=t(6979);function d(){let{user:e,isLoading:s}=(0,l.A)();return s?(0,r.jsx)(n.A,{message:"Loading dashboard..."}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,r.jsxs)("p",{className:"text-gray-600 mt-2",children:["Welcome back, ",(null==e?void 0:e.full_name)||(null==e?void 0:e.username),"!",(null==e?void 0:e.twitter_username)&&(0,r.jsxs)("span",{className:"text-purple-600 font-medium",children:[" (@",e.twitter_username,")"]})," ","Here's your Twitter growth overview."]}),(null==e?void 0:e.twitter_username)?(0,r.jsx)("div",{className:"mt-4 bg-green-50 border border-green-200 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Twitter Account Connected"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,r.jsx)("p",{children:"Your Twitter account is successfully connected and ready for automation!"})})]})]})}):(0,r.jsx)("div",{className:"mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Connect Your Twitter Account"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:(0,r.jsx)("p",{children:"To start using AutoReach features, please connect your Twitter account in Settings."})})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Followers"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"2,847"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"+12% from last month"})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-twitter-light-blue rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-twitter-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tweets"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"156"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"+8% from last month"})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Engagement"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"4.2%"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"+0.5% from last month"})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Scheduled"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"23"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Posts in queue"})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Generate Content"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Create AI-powered tweets"})]})]})}),(0,r.jsx)("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-twitter-light-blue rounded-lg flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-twitter-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Schedule Posts"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Plan your content calendar"})]})]})}),(0,r.jsx)("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"View Analytics"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Track your performance"})]})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Recent Activity"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Tweet published successfully"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"2 hours ago"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:'Content generated for "AI trends"'}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"4 hours ago"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mt-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"5 posts scheduled for tomorrow"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"6 hours ago"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Gained 15 new followers"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"1 day ago"})]})]})]})]})]})]})]})}},1125:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(5155),l=t(6874),a=t.n(l);let n=e=>{let{href:s="/dashboard",size:t="md",showText:l=!0}=e,n={sm:{icon:"w-6 h-6",text:"text-lg"},md:{icon:"w-8 h-8",text:"text-xl"},lg:{icon:"w-10 h-10",text:"text-2xl"}},d=(0,r.jsxs)("div",{className:"flex items-center space-x-2 select-none",children:[(0,r.jsx)("div",{className:"".concat(n[t].icon," bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0"),children:(0,r.jsx)("span",{className:"text-white font-bold ".concat("sm"===t?"text-sm":"text-lg"),children:"A"})}),l&&(0,r.jsx)("span",{className:"".concat(n[t].text," font-bold text-gray-900 whitespace-nowrap"),children:"AutoReach"})]});return s?(0,r.jsx)(a(),{href:s,children:d}):d}},6979:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var r=t(5155);function l(e){let{message:s="Loading...",size:t="md",className:l=""}=e;return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 ".concat(l),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full border-b-2 border-blue-500 mx-auto mb-4 ".concat({sm:"h-6 w-6",md:"h-12 w-12",lg:"h-16 w-16"}[t])}),(0,r.jsx)("p",{className:"text-gray-600",children:s})]})})}t(2115)},7864:(e,s,t)=>{"use strict";t.d(s,{default:()=>x});var r=t(5155),l=t(6874),a=t.n(l),n=t(5695),d=t(2115),i=t(4611),c=t(1125),o=t(8794);let x=e=>{var s,t;let{showNavigation:l=!0}=e,x=(0,n.usePathname)(),[m,h]=(0,d.useState)(!1),[u,g]=(0,d.useState)(!1),{user:j,logout:p,isAuthenticated:v}=(0,o.A)();return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 relative z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(c.A,{})}),l&&(0,r.jsx)("nav",{className:"hidden md:flex space-x-6",children:i.Ij.map(e=>(0,r.jsx)(a(),{href:e.path,className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(x===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"," ").concat("Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100":""),children:e.name},e.path))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[l&&(0,r.jsx)("button",{onClick:()=>h(!m),className:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle mobile menu",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:m?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),v?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>g(!u),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-purple-600",children:(null==j||null==(s=j.full_name)?void 0:s[0])||(null==j||null==(t=j.username)?void 0:t[0])||"U"})}),(0,r.jsx)("span",{className:"hidden sm:block text-sm font-medium",children:(null==j?void 0:j.full_name)||(null==j?void 0:j.username)}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),u&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,r.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 border-b border-gray-100",children:[(0,r.jsx)("p",{className:"font-medium",children:(null==j?void 0:j.full_name)||(null==j?void 0:j.username)}),(null==j?void 0:j.twitter_username)&&(0,r.jsxs)("p",{className:"text-gray-500",children:["@",j.twitter_username]})]}),(0,r.jsx)(a(),{href:"/settings",onClick:()=>g(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Settings"}),(0,r.jsx)("button",{onClick:()=>{g(!1),p()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})]}):(0,r.jsx)(a(),{href:"/login",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})]})]}),l&&m&&(0,r.jsx)("div",{className:"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,r.jsx)("nav",{className:"px-4 py-4 space-y-2",children:i.Ij.map(e=>(0,r.jsx)(a(),{href:e.path,onClick:()=>h(!1),className:"block px-4 py-3 rounded-lg text-base font-medium transition-colors ".concat(x===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"," ").concat("Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100 border border-green-200":""),children:(0,r.jsxs)("span",{className:"flex items-center gap-3",children:[(0,r.jsxs)("span",{className:"text-lg",children:["Dashboard"===e.name&&"\uD83D\uDCCA","Content"===e.name&&"✍️","Analytics"===e.name&&"\uD83D\uDCC8","Settings"===e.name&&"⚙️","Auth"===e.name&&"\uD83D\uDD10","Test API"===e.name&&"\uD83E\uDDEA"]}),e.name]})},e.path))})})]})})}},7931:(e,s,t)=>{Promise.resolve().then(t.bind(t,966))}},e=>{var s=s=>e(e.s=s);e.O(0,[464,874,794,441,684,358],()=>s(7931)),_N_E=e.O()}]);