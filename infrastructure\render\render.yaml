# Render Blueprint for AutoReach Platform
# This file defines all services needed for the AutoReach deployment
#
# SECURITY WARNING: This is a template file.
# Update all placeholder values before deploying to production.
# Never deploy directly from public repositories.

databases:
  - name: autoreach-db
    databaseName: autoreach
    user: autoreach_user
    plan: free

services:
  # Backend API Service
  - type: web
    name: autoreach-backend
    runtime: python
    plan: free
    buildCommand: |
      cd backend
      pip install -r requirements.txt
    startCommand: |
      cd backend
      uvicorn app.main:app --host 0.0.0.0 --port $PORT
    healthCheckPath: /health
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: autoreach-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: autoreach-redis
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: false
      - key: FRONTEND_URL
        fromService:
          type: web
          name: autoreach-frontend
          property: host

  # Frontend Service
  - type: web
    name: autoreach-frontend
    runtime: node
    plan: free
    buildCommand: |
      cd frontend
      npm ci
      npm run build
    startCommand: |
      cd frontend
      npm start
    envVars:
      - key: NEXT_PUBLIC_API_URL
        fromService:
          type: web
          name: autoreach-backend
          property: host

  # Redis Service
  - type: redis
    name: autoreach-redis
    plan: free
    maxmemoryPolicy: allkeys-lru
