# 🚀 Reachly Render Deployment Guide

## 📋 Pre-Deployment Checklist

### ✅ **1. Repository Preparation**
- [ ] Code is pushed to GitHub
- [ ] All recent fixes are committed (OAuth fixes, cleanup, etc.)
- [ ] Repository is public or Render has access
- [ ] All sensitive data is removed from code

### ✅ **2. API Credentials Ready**
- [ ] **Twitter API Credentials** (from developer.twitter.com):
  - [ ] Client ID (OAuth 2.0)
  - [ ] Client Secret (OAuth 2.0)
  - [ ] Bearer Token
  - [ ] API Key (v1.1)
  - [ ] API Secret (v1.1)
  - [ ] Access Token (v1.1)
  - [ ] Access Token Secret (v1.1)
- [ ] **OpenAI API Key** (from platform.openai.com)

### ✅ **3. Render Account Setup**
- [ ] Account created at [render.com](https://render.com)
- [ ] GitHub connected to Render
- [ ] Billing information added (required even for free tier)

## 🚀 Deployment Steps

### **Option 1: One-Click Blueprint Deploy (Recommended)**

1. **Go to Render Dashboard**
   - Visit [render.com/dashboard](https://render.com/dashboard)
   - Click "New" → "Blueprint"

2. **Connect Repository**
   - Select "Connect a repository"
   - Choose your GitHub repository
   - Select the repository containing Reachly

3. **Configure Blueprint**
   - Blueprint file: `infrastructure/render/render.yaml`
   - Click "Apply" to start deployment

4. **Wait for Services to Deploy**
   - Database: `reachly-db` (PostgreSQL)
   - Redis: `reachly-redis` 
   - Backend: `reachly-backend` (Python/FastAPI)
   - Frontend: `reachly-frontend` (Node.js/Next.js)

### **Option 2: Manual Deploy**

If blueprint fails, deploy services individually:

1. **Database Service**
   - New → PostgreSQL
   - Name: `reachly-db`
   - Plan: Free

2. **Redis Service**
   - New → Redis
   - Name: `reachly-redis`
   - Plan: Free

3. **Backend Service**
   - New → Web Service
   - Connect repository
   - Name: `reachly-backend`
   - Runtime: Python 3
   - Build Command: `cd backend && pip install -r requirements.txt`
   - Start Command: `cd backend && python start_server.py`

4. **Frontend Service**
   - New → Web Service
   - Connect repository
   - Name: `reachly-frontend`
   - Runtime: Node
   - Build Command: `cd frontend && npm ci && npm run build`
   - Start Command: `cd frontend && npm start`

## ⚙️ Environment Variables Configuration

### **Backend Service Variables**

Add these in the Render dashboard for your backend service:

```bash
# Database (auto-generated)
DATABASE_URL=<from database service>

# Redis (auto-generated)
REDIS_URL=<from redis service>

# Security
SECRET_KEY=<auto-generated>
DEBUG=false

# Frontend URL (auto-generated)
FRONTEND_URL=<from frontend service>

# Twitter API (OAuth 2.0) - ADD MANUALLY
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token

# Twitter API (v1.1) - ADD MANUALLY
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret

# OAuth Redirect URI - UPDATE AFTER FRONTEND DEPLOYS
TWITTER_OAUTH_REDIRECT_URI=https://your-frontend-url.onrender.com/auth/twitter/oauth2-callback

# OpenAI API - ADD MANUALLY
OPENAI_API_KEY=your_openai_api_key
```

### **Frontend Service Variables**

```bash
# Backend API URL (auto-generated)
NEXT_PUBLIC_API_URL=https://your-backend-url.onrender.com
```

## 🔧 Post-Deployment Configuration

### **1. Update Service URLs**

After deployment, you'll get URLs like:
- Backend: `https://reachly-backend.onrender.com`
- Frontend: `https://reachly-frontend.onrender.com`

**Update these environment variables:**

1. **Backend Service** → Environment → Edit:
   - `FRONTEND_URL` = `https://reachly-frontend.onrender.com`
   - `TWITTER_OAUTH_REDIRECT_URI` = `https://reachly-frontend.onrender.com/auth/twitter/oauth2-callback`

2. **Frontend Service** → Environment → Edit:
   - `NEXT_PUBLIC_API_URL` = `https://reachly-backend.onrender.com`

### **2. Update Twitter OAuth Settings**

1. Go to [developer.twitter.com](https://developer.twitter.com)
2. Select your app
3. Go to "App settings" → "Authentication settings"
4. Update **Callback URLs** to:
   ```
   https://reachly-frontend.onrender.com/auth/twitter/oauth2-callback
   ```
5. Save changes

### **3. Redeploy Services**

After updating environment variables:
1. Go to each service in Render dashboard
2. Click "Manual Deploy" → "Deploy latest commit"
3. Wait for deployment to complete

## ✅ Testing Your Deployment

### **Backend Testing**
- [ ] Health check: `https://reachly-backend.onrender.com/health`
- [ ] API docs: `https://reachly-backend.onrender.com/docs`
- [ ] Database connection working (check logs)

### **Frontend Testing**
- [ ] Site loads: `https://reachly-frontend.onrender.com`
- [ ] No console errors
- [ ] API connection working

### **Integration Testing**
- [ ] Twitter OAuth login works
- [ ] User can authenticate
- [ ] Dashboard loads after login
- [ ] Content generation works

## 🐛 Troubleshooting

### **Common Issues**

**Build Failures:**
- Check build logs in Render dashboard
- Verify all dependencies in requirements.txt/package.json
- Ensure Python/Node versions are compatible

**CORS Errors:**
- Verify `FRONTEND_URL` is set correctly in backend
- Check browser console for exact error

**OAuth Errors:**
- Verify `TWITTER_OAUTH_REDIRECT_URI` matches exactly
- Check Twitter app settings
- Ensure all Twitter credentials are correct

**Database Errors:**
- Check `DATABASE_URL` is set correctly
- Verify database service is running

### **Performance Notes**

- **Free tier services sleep after 15 minutes of inactivity**
- **First request after sleep takes 30+ seconds**
- **Consider upgrading to paid plans for production**

## 📞 Support

- [Render Documentation](https://render.com/docs)
- [Render Community](https://community.render.com)
- [GitHub Issues](https://github.com/AutoReachX/MonoRepo/issues)

---

**🎉 Once deployed, your Reachly platform will be live and ready for users!**
