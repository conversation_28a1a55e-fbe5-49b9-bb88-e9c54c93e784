(()=>{var e={};e.id=800,e.ids=[800],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11749:(e,t,r)=>{"use strict";r.d(t,{default:()=>b});var s=r(60687),a=r(43210),n=r(39046),i=r(44791),l=r(51785),o=r(37425),c=r(11806),d=r(22857);let u=(0,a.forwardRef)(({className:e,label:t,error:r,helperText:a,id:n,...i},l)=>{let o=n||t?.toLowerCase().replace(/\s+/g,"-");return(0,s.jsxs)("div",{className:"space-y-2",children:[t&&(0,s.jsx)("label",{htmlFor:o,className:"block text-sm font-medium text-gray-700",children:t}),(0,s.jsx)("input",{ref:l,id:o,className:(0,d.cn)("w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors",r?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-primary-500 focus:ring-primary-500",e),...i}),r&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:r}),a&&!r&&(0,s.jsx)("p",{className:"text-sm text-gray-500",children:a})]})});u.displayName="Input";let m=({onSubmit:e,isLoading:t,error:r,initialData:n})=>{let[i,o]=(0,a.useState)({topic:n?.topic||"",tone:n?.tone||"professional",length:n?.length||"medium",includeHashtags:n?.includeHashtags??!0,includeEmojis:n?.includeEmojis??!1}),[d,m]=(0,a.useState)({}),x=e=>e.trim()?e.length<l.gY.MIN_TOPIC_LENGTH?`Topic must be at least ${l.gY.MIN_TOPIC_LENGTH} characters`:e.length>l.gY.MAX_TOPIC_LENGTH?`Topic cannot exceed ${l.gY.MAX_TOPIC_LENGTH} characters`:null:"Topic is required",h=async t=>{t.preventDefault();let r=x(i.topic);if(r)return void m({topic:r});m({}),await e(i)},p=(e,t)=>{o(r=>({...r,[e]:t})),d[e]&&m(t=>{let r={...t};return delete r[e],r})};return(0,s.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,s.jsx)(u,{label:"Topic",value:i.topic,onChange:e=>p("topic",e.target.value),placeholder:"What would you like to tweet about?",error:d.topic,required:!0,maxLength:l.gY.MAX_TOPIC_LENGTH}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 text-right",children:[i.topic.length,"/",l.gY.MAX_TOPIC_LENGTH," characters"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tone"}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:l.ID.map(e=>(0,s.jsx)(c.$,{type:"button",variant:i.tone===e.value?"primary":"outline",size:"sm",onClick:()=>p("tone",e.value),children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Length"}),(0,s.jsx)("div",{className:"grid grid-cols-3 gap-2",children:l.m0.map(e=>(0,s.jsx)(c.$,{type:"button",variant:i.length===e.value?"primary":"outline",size:"sm",onClick:()=>p("length",e.value),children:e.label},e.value))})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"hashtags",checked:i.includeHashtags,onChange:e=>p("includeHashtags",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"hashtags",className:"ml-2 text-sm text-gray-700",children:"Include hashtags"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"emojis",checked:i.includeEmojis,onChange:e=>p("includeEmojis",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"emojis",className:"ml-2 text-sm text-gray-700",children:"Include emojis"})]})]}),(0,s.jsx)(c.$,{type:"submit",isLoading:t,disabled:!i.topic.trim(),className:"w-full",children:t?"Generating...":"Generate Content"}),r&&(0,s.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-red-600",children:r})})]})},x=(0,a.forwardRef)(({className:e,variant:t="default",...r},a)=>(0,s.jsx)("div",{ref:a,className:(0,d.cn)("rounded-lg",{default:"bg-white border border-gray-200 shadow-sm",outlined:"bg-white border border-gray-300",elevated:"bg-white border border-gray-200 shadow-md"}[t],e),...r}));x.displayName="Card";let h=(0,a.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,d.cn)("p-6 pb-4",e),...t}));h.displayName="CardHeader";let p=(0,a.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,d.cn)("p-6 pt-0",e),...t}));p.displayName="CardContent";let g=(0,a.forwardRef)(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,d.cn)("text-xl font-semibold text-gray-900",e),...t}));g.displayName="CardTitle";let y=({content:e,onSchedule:t,onSaveDraft:r,onEdit:a,onDelete:n})=>(0,s.jsxs)(x,{children:[(0,s.jsxs)(h,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsx)(g,{children:"Generated Content"}),(0,s.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(e=>{switch(e){case"published":return"bg-green-100 text-green-800";case"scheduled":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})]}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Created ",new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]}),(0,s.jsx)(p,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"p-4 bg-gray-50 border border-gray-200 rounded-md",children:(0,s.jsx)("p",{className:"text-gray-900 whitespace-pre-wrap",children:e.content})}),e.hashtags&&e.hashtags.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Hashtags:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.hashtags.map((e,t)=>(0,s.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["#",e]},t))})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[t&&"draft"===e.status&&(0,s.jsx)(c.$,{size:"sm",onClick:()=>t(e),children:"Schedule"}),r&&"draft"!==e.status&&(0,s.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>r(e),children:"Save Draft"}),a&&(0,s.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>a(e),children:"Edit"}),n&&(0,s.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>n(e.id),children:"Delete"})]})]})})]}),b=()=>{let{generateContent:e,isLoading:t,errorState:r}=function(e={}){let{validateBeforeGeneration:t=!0,onSuccess:r,onError:s}=e,[c,d]=(0,a.useState)({isLoading:!1,content:null,isValidating:!1,validationErrors:{}}),{errorState:u,handleContentError:m,handleValidationError:x,clearError:h}=function(){let{handleError:e,...t}=function(e=l.i3.RETRY_ATTEMPTS){let[t,r]=(0,a.useState)({error:null,isError:!1,errorId:null}),[s,n]=(0,a.useState)({isRetrying:!1,retryCount:0,maxRetries:e,canRetry:!0}),o=(0,a.useRef)(0),c=(0,a.useCallback)((t,a)=>{let l=i.zc.handleApiError(t,{logError:!0,fallbackMessage:`Error in ${a||"operation"}`});r({error:l,isError:!0,errorId:`error_${++o.current}_${Date.now()}`});let c=i.Z$.isRetryableError(t)&&s.retryCount<e;return n(e=>({...e,canRetry:c,isRetrying:!1})),l},[e,s.retryCount]),d=(0,a.useCallback)(()=>{r({error:null,isError:!1,errorId:null}),n(e=>({...e,isRetrying:!1,retryCount:0,canRetry:!0}))},[]),u=(0,a.useCallback)(async t=>{if(s.canRetry&&!s.isRetrying){n(e=>({...e,isRetrying:!0,retryCount:e.retryCount+1}));try{let r=await i.Z$.retryOperation(t,e-s.retryCount,l.i3.RETRY_DELAY);return d(),r}catch(e){throw c(e,"retry operation"),e}}},[s,e,c,d]),m=(0,a.useCallback)(async(e,t)=>{try{return d(),await e()}catch(e){c(e,t);return}},[c,d]);return{errorState:t,retryState:s,handleError:c,clearError:d,retry:u,executeWithErrorHandling:m}}(),r=(0,a.useCallback)((t,r)=>e(i.Iz.handleGenerationError(t),`content ${r||"operation"}`),[e]),s=(0,a.useCallback)(t=>e(i.Iz.handleContentValidationError(t),"content validation"),[e]);return{...t,handleContentError:r,handleValidationError:s}}(),{validateTopic:p,getContentStats:g,clearValidation:y}=function(){let[e,t]=(0,a.useState)({}),r=(0,a.useCallback)(e=>{let r=o.$D.validateTopic(e);return t(e=>({...e,topic:r})),r},[]),s=(0,a.useCallback)(e=>{let r=o.$D.validateContent(e);return t(e=>({...e,content:r})),r},[]);return{validationResults:e,validateTopic:r,validateContent:s,getContentStats:(0,a.useCallback)(e=>o.$D.getContentStats(e),[]),clearValidation:(0,a.useCallback)(e=>{e?t(t=>{let r={...t};return delete r[e],r}):t({})},[])}}(),b=(0,a.useCallback)(e=>{d(e=>({...e,isValidating:!0,validationErrors:{}}));let t={},r=p(e.topic);!r.isValid&&r.error&&(t.topic=r.error),e.tone&&!["professional","casual","humorous","inspirational"].includes(e.tone)&&(t.tone="Invalid content tone"),e.length&&!["short","medium","long"].includes(e.length)&&(t.length="Invalid content length");let s=Object.keys(t).length>0;return d(e=>({...e,isValidating:!1,validationErrors:t})),s&&x(t),!s},[p,x]),f=(0,a.useCallback)(async e=>{if(h(),y(),!t||b(e)){d(e=>({...e,isLoading:!0}));try{let t=await n.l.generateContent(e);return d(e=>({...e,content:t,isLoading:!1})),r?.(t),t}catch(t){d(e=>({...e,isLoading:!1}));let e=m(t,"generation");s?.(e);return}}},[t,b,m,h,y,r,s]),j=(0,a.useCallback)(async(e,t,r)=>f({topic:e,tone:t||"professional",length:r||"medium",includeHashtags:!0,includeEmojis:!1}),[f]),v=(0,a.useCallback)(async(e,t)=>f({topic:e,tone:t||"professional",length:"long",includeHashtags:!0,includeEmojis:!1}),[f]),N=(0,a.useCallback)(async(e,t)=>f({topic:e,tone:t||"professional",length:"short",includeHashtags:!1,includeEmojis:!1}),[f]),w=(0,a.useCallback)(()=>{d({isLoading:!1,content:null,isValidating:!1,validationErrors:{}}),h(),y()},[h,y]),C=(0,a.useCallback)(e=>g(e),[g]);return{...c,errorState:u,generateContent:f,generateTweet:j,generateThread:v,generateReply:N,validateRequest:b,clearAll:w,clearError:h,getContentStatistics:C}}(),[c,d]=(0,a.useState)(null),u=async t=>{let r=await e(t);r&&d(r)};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(x,{children:[(0,s.jsx)(h,{children:(0,s.jsx)(g,{children:"Generate Content"})}),(0,s.jsx)(p,{children:(0,s.jsx)(m,{onSubmit:u,isLoading:t,error:r.error?.message})})]}),c&&(0,s.jsx)(y,{content:c,onSchedule:e=>{console.log("Schedule content:",e)},onSaveDraft:e=>{console.log("Save draft:",e)}})]})}},11806:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var s=r(60687),a=r(43210),n=r(22857);let i=(0,a.forwardRef)(({className:e,variant:t="primary",size:r="md",isLoading:a,children:i,disabled:l,...o},c)=>(0,s.jsxs)("button",{ref:c,className:(0,n.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[r],e),disabled:l||a,...o,children:[a&&(0,s.jsxs)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]}));i.displayName="Button"},12412:e=>{"use strict";e.exports=require("assert")},14890:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\Header.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22857:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(49384),a=r(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51159:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(60687),a=r(85814),n=r.n(a);let i=({href:e="/dashboard",size:t="md",showText:r=!0})=>{let a={sm:{icon:"w-6 h-6",text:"text-lg"},md:{icon:"w-8 h-8",text:"text-xl"},lg:{icon:"w-10 h-10",text:"text-2xl"}},i=(0,s.jsxs)("div",{className:"flex items-center space-x-2 select-none",children:[(0,s.jsx)("div",{className:`${a[t].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`,children:(0,s.jsx)("span",{className:`text-white font-bold ${"sm"===t?"text-sm":"text-lg"}`,children:"A"})}),r&&(0,s.jsx)("span",{className:`${a[t].text} font-bold text-gray-900 whitespace-nowrap`,children:"AutoReach"})]});return e?(0,s.jsx)(n(),{href:e,children:i}):i}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64867:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),a=r(14890),n=r(86808);function i(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(a.default,{}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Content"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Generate and manage your Twitter content with AI."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsx)("div",{children:(0,s.jsx)(n.default,{})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Recent Content"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Published"}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"2 hours ago"})]}),(0,s.jsx)("p",{className:"text-gray-900 mb-2",children:"Just discovered an amazing new AI tool that's revolutionizing content creation! \uD83D\uDE80 The future of marketing is here. #AI #Marketing #Innovation"}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-500 space-x-4",children:[(0,s.jsx)("span",{children:"❤️ 24"}),(0,s.jsx)("span",{children:"\uD83D\uDD04 8"}),(0,s.jsx)("span",{children:"\uD83D\uDCAC 3"})]})]}),(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"Scheduled"}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"Tomorrow 9:00 AM"})]}),(0,s.jsx)("p",{className:"text-gray-900 mb-2",children:"Monday motivation: Success isn't just about what you accomplish in your life, it's about what you inspire others to do. \uD83D\uDCAA #MondayMotivation #Success"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{className:"text-sm text-primary-600 hover:text-primary-700",children:"Edit"}),(0,s.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700",children:"Delete"})]})]}),(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"Draft"}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"1 day ago"})]}),(0,s.jsx)("p",{className:"text-gray-900 mb-2",children:"The key to effective social media marketing is consistency and authenticity. Here are 5 tips to improve your engagement... \uD83E\uDDF5"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{className:"text-sm text-primary-600 hover:text-primary-700",children:"Edit"}),(0,s.jsx)("button",{className:"text-sm text-green-600 hover:text-green-700",children:"Schedule"}),(0,s.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700",children:"Delete"})]})]}),(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Published"}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:"2 days ago"})]}),(0,s.jsx)("p",{className:"text-gray-900 mb-2",children:"Building a personal brand takes time, but the results are worth it. Focus on providing value and being genuine. #PersonalBrand #Growth"}),(0,s.jsxs)("div",{className:"flex items-center text-sm text-gray-500 space-x-4",children:[(0,s.jsx)("span",{children:"❤️ 42"}),(0,s.jsx)("span",{children:"\uD83D\uDD04 15"}),(0,s.jsx)("span",{children:"\uD83D\uDCAC 7"})]})]})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsx)("button",{className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"View All Content"})})]})]})]})]})}},72597:(e,t,r)=>{Promise.resolve().then(r.bind(r,11749)),Promise.resolve().then(r.bind(r,95188))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81454:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["content",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,64867)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\content\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\content\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/content/page",pathname:"/content",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81630:e=>{"use strict";e.exports=require("http")},82325:(e,t,r)=>{Promise.resolve().then(r.bind(r,86808)),Promise.resolve().then(r.bind(r,14890))},83997:e=>{"use strict";e.exports=require("tty")},86808:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\components\\\\ContentGeneration.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ContentGeneration.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},95188:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(60687),a=r(85814),n=r.n(a),i=r(16189),l=r(43210),o=r(51785),c=r(51159),d=r(63772);let u=({showNavigation:e=!0})=>{let t=(0,i.usePathname)(),[r,a]=(0,l.useState)(!1),[u,m]=(0,l.useState)(!1),{user:x,logout:h,isAuthenticated:p}=(0,d.A)();return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 relative z-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(c.A,{})}),e&&(0,s.jsx)("nav",{className:"hidden md:flex space-x-6",children:o.Ij.map(e=>(0,s.jsx)(n(),{href:e.path,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${t===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"} ${"Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100":""}`,children:e.name},e.path))}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e&&(0,s.jsx)("button",{onClick:()=>a(!r),className:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle mobile menu",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r?(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),p?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>m(!u),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-medium text-purple-600",children:x?.full_name?.[0]||x?.username?.[0]||"U"})}),(0,s.jsx)("span",{className:"hidden sm:block text-sm font-medium",children:x?.full_name||x?.username}),(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),u&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,s.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 border-b border-gray-100",children:[(0,s.jsx)("p",{className:"font-medium",children:x?.full_name||x?.username}),x?.twitter_username&&(0,s.jsxs)("p",{className:"text-gray-500",children:["@",x.twitter_username]})]}),(0,s.jsx)(n(),{href:"/settings",onClick:()=>m(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Settings"}),(0,s.jsx)("button",{onClick:()=>{m(!1),h()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})]}):(0,s.jsx)(n(),{href:"/login",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})]})]}),e&&r&&(0,s.jsx)("div",{className:"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,s.jsx)("nav",{className:"px-4 py-4 space-y-2",children:o.Ij.map(e=>(0,s.jsx)(n(),{href:e.path,onClick:()=>a(!1),className:`block px-4 py-3 rounded-lg text-base font-medium transition-colors ${t===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"} ${"Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100 border border-green-200":""}`,children:(0,s.jsxs)("span",{className:"flex items-center gap-3",children:[(0,s.jsxs)("span",{className:"text-lg",children:["Dashboard"===e.name&&"\uD83D\uDCCA","Content"===e.name&&"✍️","Analytics"===e.name&&"\uD83D\uDCC8","Settings"===e.name&&"⚙️","Auth"===e.name&&"\uD83D\uDD10","Test API"===e.name&&"\uD83E\uDDEA"]}),e.name]})},e.path))})})]})})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,786,658,814,69,70,337],()=>r(81454));module.exports=s})();