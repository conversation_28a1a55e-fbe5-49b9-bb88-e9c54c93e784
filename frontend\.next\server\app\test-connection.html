<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/96c5a8811fcdd0f0.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-d3d0a601b14eaefe.js"/><script src="/_next/static/chunks/4bd1b696-dcfcdc570c79c271.js" async=""></script><script src="/_next/static/chunks/684-53733ca48c812d60.js" async=""></script><script src="/_next/static/chunks/main-app-bf250d8ffdf20ddb.js" async=""></script><script src="/_next/static/chunks/464-d428eda3d36d5b11.js" async=""></script><script src="/_next/static/chunks/794-28dd786855561576.js" async=""></script><script src="/_next/static/chunks/app/layout-1c48f3a60e9fb700.js" async=""></script><script src="/_next/static/chunks/874-0451bdd8dbf73b46.js" async=""></script><script src="/_next/static/chunks/926-2ff8eea8389d574a.js" async=""></script><script src="/_next/static/chunks/app/test-connection/page-e9f8ad4f947621e6.js" async=""></script><title>Reachly</title><meta name="description" content="Automate your Twitter growth with AI-powered content creation and engagement"/><link rel="shortcut icon" href="/favicon.svg"/><link rel="icon" href="/favicon.svg"/><link rel="apple-touch-icon" href="/favicon.svg"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_e8ce0c font-sans antialiased"><div class="min-h-screen bg-gray-100 py-8"><div class="container mx-auto px-4"><div class="text-center mb-8"><h1 class="text-4xl font-bold text-gray-800 mb-4">AutoReach Connection Test</h1><p class="text-lg text-gray-600 max-w-2xl mx-auto">This page tests the connection between the Next.js frontend and FastAPI backend. Use this to verify that your setup is working correctly.</p></div><div class="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg"><h2 class="text-2xl font-bold mb-6 text-gray-800">Frontend-Backend Connection Test</h2><div class="space-y-4 mb-6"><div class="flex items-center space-x-3 p-3 border rounded-lg"><span class="text-2xl">⏳</span><div class="flex-1"><div class="font-medium text-yellow-600">Backend Health Check</div></div></div><div class="flex items-center space-x-3 p-3 border rounded-lg"><span class="text-2xl">⏳</span><div class="flex-1"><div class="font-medium text-yellow-600">API Configuration</div></div></div><div class="flex items-center space-x-3 p-3 border rounded-lg"><span class="text-2xl">⏳</span><div class="flex-1"><div class="font-medium text-yellow-600">Authentication Test</div></div></div><div class="flex items-center space-x-3 p-3 border rounded-lg"><span class="text-2xl">⏳</span><div class="flex-1"><div class="font-medium text-yellow-600">Content Service Test</div></div></div></div><div class="flex space-x-4"><button class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">Run Connection Tests</button><button disabled="" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed">Test Content Generation</button></div><div class="mt-6 p-4 bg-gray-50 rounded-lg"><h3 class="font-medium text-gray-800 mb-2">Connection Status</h3><div class="text-sm text-gray-600 space-y-1"><div>Backend URL: <!-- -->http://localhost:8000/api</div><div>Authentication: <!-- -->❌ Not authenticated</div></div></div><div class="mt-4 text-xs text-gray-500"><p>This component tests the connection between the Next.js frontend and FastAPI backend.</p><p>Make sure the backend is running on port 8000 before testing.</p></div></div><div class="mt-12 max-w-4xl mx-auto"><div class="bg-white rounded-lg shadow-lg p-6"><h2 class="text-2xl font-bold mb-4 text-gray-800">Setup Instructions</h2><div class="grid md:grid-cols-2 gap-6"><div><h3 class="text-lg font-semibold mb-3 text-gray-700">🔧 Backend Setup</h3><div class="space-y-2 text-sm text-gray-600"><div class="bg-gray-50 p-3 rounded font-mono">cd backend<br/>.\venv\Scripts\Activate.ps1<br/>uvicorn app.main:app --reload</div><p>Backend should be running on <strong>http://localhost:8000</strong></p></div></div><div><h3 class="text-lg font-semibold mb-3 text-gray-700">⚛️ Frontend Setup</h3><div class="space-y-2 text-sm text-gray-600"><div class="bg-gray-50 p-3 rounded font-mono">cd frontend<br/>npm run dev</div><p>Frontend should be running on <strong>http://localhost:3000</strong></p></div></div></div><div class="mt-6 text-center"><a class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" href="/">← Back to Home</a></div></div></div></div></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-d3d0a601b14eaefe.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[8794,[\"464\",\"static/chunks/464-d428eda3d36d5b11.js\",\"794\",\"static/chunks/794-28dd786855561576.js\",\"177\",\"static/chunks/app/layout-1c48f3a60e9fb700.js\"],\"AuthProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[8715,[\"464\",\"static/chunks/464-d428eda3d36d5b11.js\",\"874\",\"static/chunks/874-0451bdd8dbf73b46.js\",\"926\",\"static/chunks/926-2ff8eea8389d574a.js\",\"894\",\"static/chunks/app/test-connection/page-e9f8ad4f947621e6.js\"],\"default\"]\n6:I[6874,[\"464\",\"static/chunks/464-d428eda3d36d5b11.js\",\"874\",\"static/chunks/874-0451bdd8dbf73b46.js\",\"926\",\"static/chunks/926-2ff8eea8389d574a.js\",\"894\",\"static/chunks/app/test-connection/page-e9f8ad4f947621e6.js\"],\"\"]\n7:I[9665,[],\"MetadataBoundary\"]\n9:I[9665,[],\"OutletBoundary\"]\nc:I[4911,[],\"AsyncMetadataOutlet\"]\ne:I[9665,[],\"ViewportBoundary\"]\n10:I[6614,[],\"\"]\n:HL[\"/_next/static/css/96c5a8811fcdd0f0.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"o4ILAc_RI2SHu5NpYzW5F\",\"p\":\"\",\"c\":[\"\",\"test-connection\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"test-connection\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/96c5a8811fcdd0f0.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_e8ce0c font-sans antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"test-connection\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-100 py-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"container mx-auto px-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-center mb-8\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-4xl font-bold text-gray-800 mb-4\",\"children\":\"AutoReach Connection Test\"}],[\"$\",\"p\",null,{\"className\":\"text-lg text-gray-600 max-w-2xl mx-auto\",\"children\":\"This page tests the connection between the Next.js frontend and FastAPI backend. Use this to verify that your setup is working correctly.\"}]]}],[\"$\",\"$L5\",null,{}],[\"$\",\"div\",null,{\"className\":\"mt-12 max-w-4xl mx-auto\",\"children\":[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow-lg p-6\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-2xl font-bold mb-4 text-gray-800\",\"children\":\"Setup Instructions\"}],[\"$\",\"div\",null,{\"className\":\"grid md:grid-cols-2 gap-6\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-3 text-gray-700\",\"children\":\"🔧 Backend Setup\"}],[\"$\",\"div\",null,{\"className\":\"space-y-2 text-sm text-gray-600\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-gray-50 p-3 rounded font-mono\",\"children\":[\"cd backend\",[\"$\",\"br\",null,{}],\".\\\\venv\\\\Scripts\\\\Activate.ps1\",[\"$\",\"br\",null,{}],\"uvicorn app.main:app --reload\"]}],[\"$\",\"p\",null,{\"children\":[\"Backend should be running on \",[\"$\",\"strong\",null,{\"children\":\"http://localhost:8000\"}]]}]]}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-semibold mb-3 text-gray-700\",\"children\":\"⚛️ Frontend Setup\"}],[\"$\",\"div\",null,{\"className\":\"space-y-2 text-sm text-gray-600\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-gray-50 p-3 rounded font-mono\",\"children\":[\"cd frontend\",[\"$\",\"br\",null,{}],\"npm run dev\"]}],[\"$\",\"p\",null,{\"children\":[\"Frontend should be running on \",[\"$\",\"strong\",null,{\"children\":\"http://localhost:3000\"}]]}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-6 text-center\",\"children\":[\"$\",\"$L6\",null,{\"href\":\"/\",\"className\":\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\"children\":\"← Back to Home\"}]}]]}]}]]}]}],[\"$\",\"$L7\",null,{\"children\":\"$L8\"}],null,[\"$\",\"$L9\",null,{\"children\":[\"$La\",\"$Lb\",[\"$\",\"$Lc\",null,{\"promise\":\"$@d\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"u0ALGqkIU--68iWpB86Dr\",{\"children\":[[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$10\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"11:\"$Sreact.suspense\"\n12:I[4911,[],\"AsyncMetadata\"]\n8:[\"$\",\"$11\",null,{\"fallback\":null,\"children\":[\"$\",\"$L12\",null,{\"promise\":\"$@13\"}]}]\n"])</script><script>self.__next_f.push([1,"b:null\n"])</script><script>self.__next_f.push([1,"f:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\na:null\n"])</script><script>self.__next_f.push([1,"13:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Reachly\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Automate your Twitter growth with AI-powered content creation and engagement\"}],[\"$\",\"link\",\"2\",{\"rel\":\"shortcut icon\",\"href\":\"/favicon.svg\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/favicon.svg\"}],[\"$\",\"link\",\"4\",{\"rel\":\"apple-touch-icon\",\"href\":\"/favicon.svg\"}]],\"error\":null,\"digest\":\"$undefined\"}\nd:{\"metadata\":\"$13:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>