@echo off
REM Reachly Render Deployment Helper Script
REM This script helps prepare your repository for Render deployment

echo 🚀 Reachly Render Deployment Helper
echo ==================================

REM Check if we're in the right directory
if not exist "infrastructure\render\render.yaml" (
    echo ❌ Error: render.yaml not found. Please run this script from the project root.
    pause
    exit /b 1
)

echo ✅ Found render.yaml configuration

REM Check if git is initialized
if not exist ".git" (
    echo ❌ Error: This is not a git repository. Please initialize git first:
    echo    git init
    echo    git add .
    echo    git commit -m "Initial commit"
    echo    git remote add origin ^<your-github-repo-url^>
    echo    git push -u origin main
    pause
    exit /b 1
)

echo ✅ Git repository detected

REM Check for uncommitted changes
git status --porcelain > nul 2>&1
if %errorlevel% equ 0 (
    for /f %%i in ('git status --porcelain') do (
        echo ⚠️  Warning: You have uncommitted changes.
        echo    It's recommended to commit all changes before deployment.
        echo.
        set /p commit="Do you want to commit all changes now? (y/n): "
        if /i "!commit!"=="y" (
            git add .
            git commit -m "Pre-deployment commit: %date% %time%"
            echo ✅ Changes committed
        )
        goto :continue
    )
)

:continue

REM Check if we have a remote origin
git remote get-url origin > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: No git remote 'origin' found.
    echo    Please add your GitHub repository as origin:
    echo    git remote add origin ^<your-github-repo-url^>
    pause
    exit /b 1
)

echo ✅ Git remote origin configured

REM Push to GitHub
echo 📤 Pushing to GitHub...
git push origin main

if %errorlevel% equ 0 (
    echo ✅ Successfully pushed to GitHub
) else (
    echo ❌ Failed to push to GitHub. Please check your git configuration.
    pause
    exit /b 1
)

echo.
echo 🎉 Repository is ready for Render deployment!
echo.
echo Next steps:
echo 1. Go to https://render.com/dashboard
echo 2. Click 'New' → 'Blueprint'
echo 3. Connect your GitHub repository
echo 4. Select 'infrastructure/render/render.yaml'
echo 5. Click 'Apply' to deploy
echo.
echo 📖 For detailed instructions, see: RENDER_DEPLOYMENT_GUIDE.md
echo.
echo 🔑 Don't forget to add your API keys in the Render dashboard:
echo    - Twitter API credentials
echo    - OpenAI API key
echo.
echo 🌐 After deployment, update these URLs:
echo    - Twitter OAuth redirect URI
echo    - Frontend/Backend environment variables
echo.
echo Happy deploying! 🚀
pause
