{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/validation.ts"], "sourcesContent": ["/**\n * Frontend validation utilities following DRY and KISS principles.\n * Centralizes validation logic to eliminate code duplication.\n */\n\nimport {\n  VALIDATION_RULES,\n  REGEX_PATTERNS,\n  CONTENT_CONSTANTS,\n  TWITTER_CONSTANTS\n} from './constants';\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n}\n\nexport interface FieldValidationResult {\n  isValid: boolean;\n  error?: string;\n  warnings?: string[];\n}\n\n/**\n * Base validation utilities\n */\nexport class ValidationUtils {\n  /**\n   * Validate required field\n   */\n  static validateRequired(value: unknown, fieldName: string = 'This field'): FieldValidationResult {\n    if (value === null || value === undefined) {\n      // If fieldName contains \"is required\" or similar, use it as is\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    if (typeof value === 'string' && !value.trim()) {\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    if (Array.isArray(value) && value.length === 0) {\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate string length\n   */\n  static validateLength(\n    value: string,\n    minLength?: number,\n    maxLength?: number\n  ): FieldValidationResult {\n    if (typeof value !== 'string') {\n      return { isValid: false, error: 'Value must be a string' };\n    }\n\n    const length = value.length;\n\n    if (minLength !== undefined && length < minLength) {\n      return {\n        isValid: false,\n        error: `Must be at least ${minLength} characters long`\n      };\n    }\n\n    if (maxLength !== undefined && length > maxLength) {\n      return {\n        isValid: false,\n        error: `Must be no more than ${maxLength} characters long`\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate email format\n   */\n  static validateEmail(email: string): FieldValidationResult {\n    if (!email) {\n      return { isValid: false, error: 'Email is required' };\n    }\n\n    if (!REGEX_PATTERNS.EMAIL.test(email)) {\n      return { isValid: false, error: 'Please enter a valid email address' };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate pattern match\n   */\n  static validatePattern(value: string, pattern: RegExp, errorMessage: string = 'Invalid format'): FieldValidationResult {\n    if (!pattern.test(value)) {\n      return { isValid: false, error: errorMessage };\n    }\n    return { isValid: true };\n  }\n\n  /**\n   * Validate numeric range\n   */\n  static validateRange(value: number, min: number, max: number): FieldValidationResult {\n    if (value < min || value > max) {\n      return { isValid: false, error: `Must be between ${min} and ${max}` };\n    }\n    return { isValid: true };\n  }\n\n  /**\n   * Validate choice from options\n   */\n  static validateChoice(value: string, choices: readonly string[] | string[], errorMessage: string = 'Please select a valid option'): FieldValidationResult {\n    if (!choices.includes(value)) {\n      return { isValid: false, error: errorMessage };\n    }\n    return { isValid: true };\n  }\n\n\n\n  /**\n   * Validate username\n   */\n  static validateUsername(username: string): FieldValidationResult {\n    const requiredResult = this.validateRequired(username, 'Username');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const lengthResult = this.validateLength(username, 3, 20);\n    if (!lengthResult.isValid) return lengthResult;\n\n    if (!REGEX_PATTERNS.USERNAME.test(username)) {\n      return { isValid: false, error: 'Username can only contain letters, numbers, and underscores' };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate password\n   */\n  static validatePassword(password: string): FieldValidationResult {\n    const requiredResult = this.validateRequired(password, 'Password');\n    if (!requiredResult.isValid) return requiredResult;\n\n    if (password.length < VALIDATION_RULES.MIN_PASSWORD_LENGTH) {\n      return {\n        isValid: false,\n        error: `Password must be at least ${VALIDATION_RULES.MIN_PASSWORD_LENGTH} characters long`\n      };\n    }\n\n    if (!REGEX_PATTERNS.PASSWORD.test(password)) {\n      return {\n        isValid: false,\n        error: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'\n      };\n    }\n\n    return { isValid: true };\n  }\n}\n\n/**\n * Content-specific validation utilities\n */\nexport class ContentValidation {\n  /**\n   * Validate topic for content generation\n   */\n  static validateTopic(topic: string): FieldValidationResult {\n    const requiredResult = ValidationUtils.validateRequired(topic, 'Topic');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const length = topic.length;\n    if (length < VALIDATION_RULES.MIN_TOPIC_LENGTH) {\n      return {\n        isValid: false,\n        error: `Topic must be at least ${VALIDATION_RULES.MIN_TOPIC_LENGTH} characters long`\n      };\n    }\n\n    if (length > VALIDATION_RULES.MAX_TOPIC_LENGTH) {\n      return {\n        isValid: false,\n        error: `Topic must be no more than ${VALIDATION_RULES.MAX_TOPIC_LENGTH} characters long`\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate content for tweets\n   */\n  static validateContent(content: string): FieldValidationResult {\n    const requiredResult = ValidationUtils.validateRequired(content, 'Content');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const length = content.length;\n    if (length > TWITTER_CONSTANTS.MAX_TWEET_LENGTH) {\n      return {\n        isValid: false,\n        error: `Content must be no more than ${TWITTER_CONSTANTS.MAX_TWEET_LENGTH} characters long`\n      };\n    }\n\n    const warnings: string[] = [];\n\n    // Check for too many hashtags\n    const hashtagCount = ContentValidation.countHashtags(content);\n    if (hashtagCount > 3) {\n      warnings.push('Consider using fewer hashtags for better engagement');\n    }\n\n    // Check for too many mentions\n    const mentionCount = ContentValidation.countMentions(content);\n    if (mentionCount > 5) {\n      warnings.push('Too many mentions may reduce visibility');\n    }\n\n    return {\n      isValid: true,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n  }\n\n  /**\n   * Validate content style\n   */\n  static validateStyle(style: string): FieldValidationResult {\n    return ValidationUtils.validateChoice(\n      style,\n      CONTENT_CONSTANTS.SUPPORTED_STYLES,\n      'Please select a valid style'\n    );\n  }\n\n  /**\n   * Validate language\n   */\n  static validateLanguage(language: string): FieldValidationResult {\n    const supportedLanguages = CONTENT_CONSTANTS.SUPPORTED_LANGUAGES.map(lang => lang.code);\n    return ValidationUtils.validateChoice(\n      language,\n      supportedLanguages,\n      'Please select a valid language'\n    );\n  }\n\n  /**\n   * Validate thread size\n   */\n  static validateThreadSize(size: number): FieldValidationResult {\n    if (size < 2) {\n      return { isValid: false, error: 'Thread must have at least 2 tweets' };\n    }\n    if (size > TWITTER_CONSTANTS.MAX_THREAD_TWEETS) {\n      return { isValid: false, error: `Thread cannot have more than ${TWITTER_CONSTANTS.MAX_THREAD_TWEETS} tweets` };\n    }\n    return { isValid: true };\n  }\n\n\n\n  /**\n   * Count hashtags in content\n   */\n  static countHashtags(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.HASHTAG);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Count mentions in content\n   */\n  static countMentions(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.MENTION);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Count URLs in content\n   */\n  static countUrls(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.URL);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Get content statistics\n   */\n  static getContentStats(content: string) {\n    const characterCount = content.length;\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n\n    return {\n      characterCount,\n      wordCount,\n      hashtagCount: ContentValidation.countHashtags(content),\n      mentionCount: ContentValidation.countMentions(content),\n      urlCount: ContentValidation.countUrls(content),\n      // Add properties expected by tests\n      length: characterCount,\n      remaining: TWITTER_CONSTANTS.MAX_TWEET_LENGTH - characterCount,\n    };\n  }\n}\n\n/**\n * Form validation utilities\n */\nexport class FormValidation {\n  /**\n   * Validate multiple fields and return combined result\n   */\n  static validateFields(\n    validations: Array<() => FieldValidationResult>\n  ): ValidationResult {\n    const errors: string[] = [];\n\n    for (const validation of validations) {\n      const result = validation();\n      if (!result.isValid && result.error) {\n        errors.push(result.error);\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n    };\n  }\n\n  /**\n   * Validate form data against schema\n   */\n  static validateFormData<T extends Record<string, unknown>>(\n    data: T,\n    schema: Record<keyof T, (value: unknown) => FieldValidationResult>\n  ): Record<keyof T, string | undefined> {\n    const errors: Record<keyof T, string | undefined> = {} as Record<keyof T, string | undefined>;\n\n    for (const [field, validator] of Object.entries(schema)) {\n      const result = validator(data[field as keyof T]);\n      if (!result.isValid && result.error) {\n        errors[field as keyof T] = result.error;\n      }\n    }\n\n    return errors;\n  }\n\n  /**\n   * Validate single field\n   */\n  static validateField(\n    value: unknown,\n    validator: (value: unknown) => FieldValidationResult\n  ): FieldValidationResult {\n    return validator(value);\n  }\n}\n\n/**\n * Real-time validation hook utilities\n */\nexport class ValidationHooks {\n  /**\n   * Debounced validation for real-time feedback\n   */\n  static createDebouncedValidator(\n    validator: (value: unknown) => FieldValidationResult,\n    delay: number = 300\n  ) {\n    let timeoutId: NodeJS.Timeout;\n\n    return (value: unknown, callback: (result: FieldValidationResult) => void) => {\n      clearTimeout(timeoutId);\n      timeoutId = setTimeout(() => {\n        const result = validator(value);\n        callback(result);\n      }, delay);\n    };\n  }\n}\n\n/**\n * Common validation schemas for forms\n */\nexport const ValidationSchemas = {\n  contentGeneration: {\n    topic: (value: unknown) => ContentValidation.validateTopic(value as string),\n    style: (value: unknown) => ContentValidation.validateStyle(value as string),\n    language: (value: unknown) => ContentValidation.validateLanguage(value as string),\n  },\n\n  userRegistration: {\n    username: (value: unknown) => ValidationUtils.validateUsername(value as string),\n    email: (value: unknown) => ValidationUtils.validateEmail(value as string),\n    password: (value: unknown) => ValidationUtils.validatePassword(value as string),\n  },\n\n  userLogin: {\n    email: (value: unknown) => ValidationUtils.validateEmail(value as string),\n    password: (value: unknown) => ValidationUtils.validateRequired(value, 'Password'),\n  },\n};\n\n/**\n * Utility functions for common validation patterns\n */\nexport const validateContentGenerationForm = (data: {\n  topic: string;\n  style?: string;\n  language?: string;\n}) => {\n  const errors: Record<string, string | undefined> = {};\n\n  const topicValidation = ValidationSchemas.contentGeneration.topic(data.topic);\n  if (!topicValidation.isValid && topicValidation.error) {\n    errors.topic = topicValidation.error;\n  }\n\n  return errors;\n};\n\nexport const validateUserRegistrationForm = (data: {\n  username: string;\n  email: string;\n  password: string;\n}) => {\n  return FormValidation.validateFormData(data, ValidationSchemas.userRegistration);\n};\n\nexport const validateUserLoginForm = (data: {\n  email: string;\n  password: string;\n}) => {\n  return FormValidation.validateFormData(data, ValidationSchemas.userLogin);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;;AAqBO,MAAM;IACX;;GAEC,GACD,OAAO,iBAAiB,KAAc,EAAE,YAAoB,YAAY,EAAyB;QAC/F,IAAI,UAAU,QAAQ,UAAU,WAAW;YACzC,+DAA+D;YAC/D,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,IAAI;YAC9C,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;YAC9C,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,eACL,KAAa,EACb,SAAkB,EAClB,SAAkB,EACK;QACvB,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QAEA,MAAM,SAAS,MAAM,MAAM;QAE3B,IAAI,cAAc,aAAa,SAAS,WAAW;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,iBAAiB,EAAE,UAAU,gBAAgB,CAAC;YACxD;QACF;QAEA,IAAI,cAAc,aAAa,SAAS,WAAW;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,qBAAqB,EAAE,UAAU,gBAAgB,CAAC;YAC5D;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,IAAI,CAAC,OAAO;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoB;QACtD;QAEA,IAAI,CAAC,0HAAA,CAAA,iBAAc,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,gBAAgB,KAAa,EAAE,OAAe,EAAE,eAAuB,gBAAgB,EAAyB;QACrH,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;YACxB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAE,GAAW,EAAE,GAAW,EAAyB;QACnF,IAAI,QAAQ,OAAO,QAAQ,KAAK;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,gBAAgB,EAAE,IAAI,KAAK,EAAE,KAAK;YAAC;QACtE;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,eAAe,KAAa,EAAE,OAAqC,EAAE,eAAuB,8BAA8B,EAAyB;QACxJ,IAAI,CAAC,QAAQ,QAAQ,CAAC,QAAQ;YAC5B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAIA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,iBAAiB,IAAI,CAAC,gBAAgB,CAAC,UAAU;QACvD,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,eAAe,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QACtD,IAAI,CAAC,aAAa,OAAO,EAAE,OAAO;QAElC,IAAI,CAAC,0HAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;YAC3C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA8D;QAChG;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,iBAAiB,IAAI,CAAC,gBAAgB,CAAC,UAAU;QACvD,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,IAAI,SAAS,MAAM,GAAG,0HAAA,CAAA,mBAAgB,CAAC,mBAAmB,EAAE;YAC1D,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,0BAA0B,EAAE,0HAAA,CAAA,mBAAgB,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;YAC5F;QACF;QAEA,IAAI,CAAC,0HAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,MAAM,iBAAiB,gBAAgB,gBAAgB,CAAC,OAAO;QAC/D,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,SAAS,MAAM,MAAM;QAC3B,IAAI,SAAS,0HAAA,CAAA,mBAAgB,CAAC,gBAAgB,EAAE;YAC9C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,uBAAuB,EAAE,0HAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YACtF;QACF;QAEA,IAAI,SAAS,0HAAA,CAAA,mBAAgB,CAAC,gBAAgB,EAAE;YAC9C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,2BAA2B,EAAE,0HAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YAC1F;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAe,EAAyB;QAC7D,MAAM,iBAAiB,gBAAgB,gBAAgB,CAAC,SAAS;QACjE,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,SAAS,QAAQ,MAAM;QAC7B,IAAI,SAAS,0HAAA,CAAA,oBAAiB,CAAC,gBAAgB,EAAE;YAC/C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,6BAA6B,EAAE,0HAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YAC7F;QACF;QAEA,MAAM,WAAqB,EAAE;QAE7B,8BAA8B;QAC9B,MAAM,eAAe,kBAAkB,aAAa,CAAC;QACrD,IAAI,eAAe,GAAG;YACpB,SAAS,IAAI,CAAC;QAChB;QAEA,8BAA8B;QAC9B,MAAM,eAAe,kBAAkB,aAAa,CAAC;QACrD,IAAI,eAAe,GAAG;YACpB,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO;YACL,SAAS;YACT,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;QAC7C;IACF;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,OAAO,gBAAgB,cAAc,CACnC,OACA,0HAAA,CAAA,oBAAiB,CAAC,gBAAgB,EAClC;IAEJ;IAEA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,qBAAqB,0HAAA,CAAA,oBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QACtF,OAAO,gBAAgB,cAAc,CACnC,UACA,oBACA;IAEJ;IAEA;;GAEC,GACD,OAAO,mBAAmB,IAAY,EAAyB;QAC7D,IAAI,OAAO,GAAG;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QACA,IAAI,OAAO,0HAAA,CAAA,oBAAiB,CAAC,iBAAiB,EAAE;YAC9C,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,6BAA6B,EAAE,0HAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAAC;QAC/G;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAIA;;GAEC,GACD,OAAO,cAAc,OAAe,EAAU;QAC5C,MAAM,UAAU,QAAQ,KAAK,CAAC,0HAAA,CAAA,iBAAc,CAAC,OAAO;QACpD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,cAAc,OAAe,EAAU;QAC5C,MAAM,UAAU,QAAQ,KAAK,CAAC,0HAAA,CAAA,iBAAc,CAAC,OAAO;QACpD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,UAAU,OAAe,EAAU;QACxC,MAAM,UAAU,QAAQ,KAAK,CAAC,0HAAA,CAAA,iBAAc,CAAC,GAAG;QAChD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAe,EAAE;QACtC,MAAM,iBAAiB,QAAQ,MAAM;QACrC,MAAM,YAAY,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,GAAG;QAExE,OAAO;YACL;YACA;YACA,cAAc,kBAAkB,aAAa,CAAC;YAC9C,cAAc,kBAAkB,aAAa,CAAC;YAC9C,UAAU,kBAAkB,SAAS,CAAC;YACtC,mCAAmC;YACnC,QAAQ;YACR,WAAW,0HAAA,CAAA,oBAAiB,CAAC,gBAAgB,GAAG;QAClD;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,eACL,WAA+C,EAC7B;QAClB,MAAM,SAAmB,EAAE;QAE3B,KAAK,MAAM,cAAc,YAAa;YACpC,MAAM,SAAS;YACf,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBACnC,OAAO,IAAI,CAAC,OAAO,KAAK;YAC1B;QACF;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;QACF;IACF;IAEA;;GAEC,GACD,OAAO,iBACL,IAAO,EACP,MAAkE,EAC7B;QACrC,MAAM,SAA8C,CAAC;QAErD,KAAK,MAAM,CAAC,OAAO,UAAU,IAAI,OAAO,OAAO,CAAC,QAAS;YACvD,MAAM,SAAS,UAAU,IAAI,CAAC,MAAiB;YAC/C,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBACnC,MAAM,CAAC,MAAiB,GAAG,OAAO,KAAK;YACzC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,cACL,KAAc,EACd,SAAoD,EAC7B;QACvB,OAAO,UAAU;IACnB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,yBACL,SAAoD,EACpD,QAAgB,GAAG,EACnB;QACA,IAAI;QAEJ,OAAO,CAAC,OAAgB;YACtB,aAAa;YACb,YAAY,WAAW;gBACrB,MAAM,SAAS,UAAU;gBACzB,SAAS;YACX,GAAG;QACL;IACF;AACF;AAKO,MAAM,oBAAoB;IAC/B,mBAAmB;QACjB,OAAO,CAAC,QAAmB,kBAAkB,aAAa,CAAC;QAC3D,OAAO,CAAC,QAAmB,kBAAkB,aAAa,CAAC;QAC3D,UAAU,CAAC,QAAmB,kBAAkB,gBAAgB,CAAC;IACnE;IAEA,kBAAkB;QAChB,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC;QAC/D,OAAO,CAAC,QAAmB,gBAAgB,aAAa,CAAC;QACzD,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC;IACjE;IAEA,WAAW;QACT,OAAO,CAAC,QAAmB,gBAAgB,aAAa,CAAC;QACzD,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC,OAAO;IACxE;AACF;AAKO,MAAM,gCAAgC,CAAC;IAK5C,MAAM,SAA6C,CAAC;IAEpD,MAAM,kBAAkB,kBAAkB,iBAAiB,CAAC,KAAK,CAAC,KAAK,KAAK;IAC5E,IAAI,CAAC,gBAAgB,OAAO,IAAI,gBAAgB,KAAK,EAAE;QACrD,OAAO,KAAK,GAAG,gBAAgB,KAAK;IACtC;IAEA,OAAO;AACT;AAEO,MAAM,+BAA+B,CAAC;IAK3C,OAAO,eAAe,gBAAgB,CAAC,MAAM,kBAAkB,gBAAgB;AACjF;AAEO,MAAM,wBAAwB,CAAC;IAIpC,OAAO,eAAe,gBAAgB,CAAC,MAAM,kBAAkB,SAAS;AAC1E", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/errorHandling.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Frontend error handling utilities following DRY and KISS principles.\n * Centralizes error handling logic to eliminate code duplication.\n */\n\nimport { HTTP_STATUS, ERROR_MESSAGES, getErrorMessageByStatus } from './constants';\n\nexport interface ApiError {\n  message: string;\n  status?: number;\n  code?: string;\n  details?: Record<string, unknown>;\n}\n\nexport interface ErrorHandlerOptions {\n  showToast?: boolean;\n  logError?: boolean;\n  fallbackMessage?: string;\n}\n\nexport interface ErrorResult {\n  type: string;\n  message: string;\n  userMessage: string;\n  context?: any;\n  timestamp: number;\n}\n\n// Error type constants\nexport const ErrorTypes = {\n  NETWORK: 'NETWORK',\n  VALIDATION: 'VALIDATION',\n  AUTHENTICATION: 'AUTHENTICATION',\n  AUTHORIZATION: 'AUTHORIZATION',\n  RATE_LIMIT: 'RATE_LIMIT',\n  SERVER: 'SERVER',\n  CLIENT: 'CLIENT',\n  UNKNOWN: 'UNKNOWN'\n} as const;\n\n/**\n * Base error handling utilities\n */\nexport class ErrorHandler {\n  /**\n   * Main error handler that processes errors and returns structured result\n   */\n  static handleError(error: unknown, context?: any): ErrorResult {\n    const message = this.getErrorMessage(error);\n    const type = this.getErrorType(error);\n    const userMessage = this.getUserFriendlyMessage(type);\n\n    const result: ErrorResult = {\n      type,\n      message,\n      userMessage,\n      context,\n      timestamp: Date.now()\n    };\n\n    this.logError(error, context);\n\n    return result;\n  }\n\n  /**\n   * Get error message from various error types\n   */\n  static getErrorMessage(error: unknown): string {\n    if (error instanceof Error) {\n      return error.message;\n    }\n    if (typeof error === 'string') {\n      return error;\n    }\n    if (error && typeof error === 'object' && 'message' in error) {\n      return String((error as any).message);\n    }\n    return 'An unknown error occurred';\n  }\n\n  /**\n   * Determine error type based on error content\n   */\n  static getErrorType(error: unknown): string {\n    const message = this.getErrorMessage(error).toLowerCase();\n\n    if (message.includes('network') || message.includes('fetch')) {\n      return ErrorTypes.NETWORK;\n    }\n    if (message.includes('validation') || message.includes('invalid')) {\n      return ErrorTypes.VALIDATION;\n    }\n    if (message.includes('unauthorized') || message.includes('auth')) {\n      return ErrorTypes.AUTHENTICATION;\n    }\n    if (message.includes('forbidden')) {\n      return ErrorTypes.AUTHORIZATION;\n    }\n    if (message.includes('too many requests') || message.includes('rate limit')) {\n      return ErrorTypes.RATE_LIMIT;\n    }\n    if (message.includes('server error') || message.includes('internal')) {\n      return ErrorTypes.SERVER;\n    }\n    if (message.includes('bad request') || message.includes('client')) {\n      return ErrorTypes.CLIENT;\n    }\n\n    return ErrorTypes.UNKNOWN;\n  }\n\n  /**\n   * Get user-friendly message for error type\n   */\n  static getUserFriendlyMessage(errorType: string): string {\n    switch (errorType) {\n      case ErrorTypes.NETWORK:\n        return 'Network error. Please check your connection.';\n      case ErrorTypes.VALIDATION:\n        return 'Please check your input and try again.';\n      case ErrorTypes.AUTHENTICATION:\n        return 'Please log in to continue.';\n      case ErrorTypes.AUTHORIZATION:\n        return 'You do not have permission to perform this action.';\n      case ErrorTypes.RATE_LIMIT:\n        return 'Too many requests. Please wait a moment and try again.';\n      case ErrorTypes.SERVER:\n        return 'Server error. Please try again later.';\n      case ErrorTypes.CLIENT:\n        return 'Invalid request. Please check your input.';\n      default:\n        return 'An unexpected error occurred. Please try again.';\n    }\n  }\n\n  /**\n   * Check if error is retryable\n   */\n  static isRetryableError(error: unknown): boolean {\n    const type = this.getErrorType(error);\n    const message = this.getErrorMessage(error).toLowerCase();\n\n    // Network errors are retryable\n    if (type === ErrorTypes.NETWORK) {\n      return true;\n    }\n\n    // Timeout errors are retryable\n    if (message.includes('timeout')) {\n      return true;\n    }\n\n    // Server errors (5xx) are retryable\n    if (type === ErrorTypes.SERVER || message.includes('server error')) {\n      return true;\n    }\n\n    // Rate limit errors are retryable (after delay)\n    if (type === ErrorTypes.RATE_LIMIT) {\n      return true;\n    }\n\n    // Authentication, validation, and client errors are not retryable\n    return false;\n  }\n\n  /**\n   * Log error with context\n   */\n  static logError(error: unknown, context?: any): void {\n    const message = this.getErrorMessage(error);\n    const logData = {\n      message,\n      context,\n      timestamp: new Date().toISOString(),\n      stack: error instanceof Error ? error.stack : undefined\n    };\n\n    console.error('Error occurred:', logData);\n  }\n\n  /**\n   * Handle API errors with consistent formatting\n   */\n  static handleApiError(\n    error: unknown,\n    options: ErrorHandlerOptions = {}\n  ): ApiError {\n    const {\n      showToast = false,\n      logError = true,\n      fallbackMessage = ERROR_MESSAGES.GENERIC_ERROR\n    } = options;\n\n    let apiError: ApiError;\n\n    // Type guard for error objects\n    const isErrorWithResponse = (err: unknown): err is { response: { status: number; data?: { message?: string; code?: string; details?: Record<string, unknown> } } } => {\n      return typeof err === 'object' && err !== null && 'response' in err;\n    };\n\n    const isErrorWithRequest = (err: unknown): err is { request: unknown } => {\n      return typeof err === 'object' && err !== null && 'request' in err;\n    };\n\n    const isErrorWithMessage = (err: unknown): err is { message: string } => {\n      return typeof err === 'object' && err !== null && 'message' in err;\n    };\n\n    if (isErrorWithResponse(error)) {\n      // HTTP error response\n      const status = error.response.status;\n      const data = error.response.data;\n\n      apiError = {\n        message: data?.message || getErrorMessageByStatus(status),\n        status,\n        code: data?.code,\n        details: data?.details\n      };\n    } else if (isErrorWithRequest(error)) {\n      // Network error\n      apiError = {\n        message: ERROR_MESSAGES.NETWORK_ERROR,\n        status: 0,\n        code: 'NETWORK_ERROR'\n      };\n    } else {\n      // Other error\n      apiError = {\n        message: isErrorWithMessage(error) ? error.message : fallbackMessage,\n        code: 'UNKNOWN_ERROR'\n      };\n    }\n\n    if (logError) {\n      console.error('API Error:', apiError, error);\n    }\n\n    if (showToast) {\n      // This would integrate with your toast system\n      // toast.error(apiError.message);\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle validation errors\n   */\n  static handleValidationError(\n    errors: Record<string, string> | string[] | unknown,\n    options: ErrorHandlerOptions = {}\n  ): ApiError {\n    const { logError = true } = options;\n\n    let message: string;\n\n    if (Array.isArray(errors)) {\n      message = errors.join(', ');\n    } else if (typeof errors === 'object' && errors !== null) {\n      message = Object.values(errors).join(', ');\n    } else {\n      message = ERROR_MESSAGES.VALIDATION_ERROR;\n    }\n\n    const apiError: ApiError = {\n      message,\n      status: HTTP_STATUS.UNPROCESSABLE_ENTITY,\n      code: 'VALIDATION_ERROR',\n      details: typeof errors === 'object' && errors !== null ? errors as Record<string, unknown> : undefined\n    };\n\n    if (logError) {\n      console.warn('Validation Error:', apiError);\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle async operation errors\n   */\n  static async handleAsyncError<T>(\n    operation: () => Promise<T>,\n    options: ErrorHandlerOptions = {}\n  ): Promise<{ data?: T; error?: ApiError }> {\n    try {\n      const data = await operation();\n      return { data };\n    } catch (error) {\n      const apiError = this.handleApiError(error, options);\n      return { error: apiError };\n    }\n  }\n\n  /**\n   * Create error boundary handler\n   */\n  static createErrorBoundaryHandler(\n    fallbackComponent?: React.ComponentType<{ error: Error }>\n  ) {\n    return (error: Error, errorInfo: any) => {\n      console.error('Error Boundary caught an error:', error, errorInfo);\n\n      // Log to error reporting service\n      // errorReportingService.captureException(error, errorInfo);\n\n      return fallbackComponent;\n    };\n  }\n}\n\n/**\n * Specific error handlers for different scenarios\n */\nexport class ContentErrorHandler {\n  /**\n   * Handle content generation errors\n   */\n  static handleGenerationError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.GENERATION_FAILED,\n      logError: true\n    });\n  }\n\n  /**\n   * Handle content validation errors\n   */\n  static handleContentValidationError(errors: any): ApiError {\n    return ErrorHandler.handleValidationError(errors, {\n      logError: true\n    });\n  }\n}\n\n/**\n * Authentication error handlers\n */\nexport class AuthErrorHandler {\n  /**\n   * Handle authentication errors\n   */\n  static handleAuthError(error: any): ApiError {\n    const apiError = ErrorHandler.handleApiError(error, {\n      logError: true\n    });\n\n    // Handle specific auth scenarios\n    if (apiError.status === HTTP_STATUS.UNAUTHORIZED) {\n      // Clear auth tokens\n      localStorage.removeItem('authToken');\n\n      // Redirect to login if needed\n      // router.push('/login');\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle token expiration\n   */\n  static handleTokenExpiration(): void {\n    localStorage.removeItem('authToken');\n    // Show token expired message\n    // toast.error(ERROR_MESSAGES.UNAUTHORIZED);\n    // Redirect to login\n    // router.push('/login');\n  }\n}\n\n/**\n * Network error handlers\n */\nexport class NetworkErrorHandler {\n  /**\n   * Handle network connectivity issues\n   */\n  static handleNetworkError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.NETWORK_ERROR,\n      logError: true\n    });\n  }\n\n  /**\n   * Handle rate limiting\n   */\n  static handleRateLimitError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,\n      logError: true\n    });\n  }\n}\n\n/**\n * Error retry utilities\n */\nexport class RetryHandler {\n  /**\n   * Retry failed operations with exponential backoff\n   */\n  static async retryOperation<T>(\n    operation: () => Promise<T>,\n    maxRetries: number = 3,\n    baseDelay: number = 1000\n  ): Promise<T> {\n    let lastError: any;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        return await operation();\n      } catch (error) {\n        lastError = error;\n\n        if (attempt === maxRetries) {\n          throw error;\n        }\n\n        // Exponential backoff\n        const delay = baseDelay * Math.pow(2, attempt);\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n    }\n\n    throw lastError;\n  }\n\n  /**\n   * Check if error is retryable\n   */\n  static isRetryableError(error: any): boolean {\n    if (!error.response) {\n      return true; // Network errors are retryable\n    }\n\n    const status = error.response.status;\n    return status >= 500 || status === HTTP_STATUS.TOO_MANY_REQUESTS;\n  }\n}\n\n/**\n * Error logging utilities\n */\nexport class ErrorLogger {\n  /**\n   * Log error with timestamp and context\n   */\n  static log(error: unknown, context?: any): void {\n    const timestamp = new Date().toISOString();\n    const message = error instanceof Error ? error.message : String(error);\n\n    if (context) {\n      console.error(`[ERROR] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.error(`[ERROR] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log warning with timestamp and context\n   */\n  static warn(message: string, context?: any): void {\n    const timestamp = new Date().toISOString();\n\n    if (context) {\n      console.warn(`[WARN] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.warn(`[WARN] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log info message\n   */\n  static info(message: string, context?: any): void {\n    const timestamp = new Date().toISOString();\n\n    if (context) {\n      console.log(`[INFO] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.log(`[INFO] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log debug message (only in development)\n   */\n  static debug(message: string, context?: any): void {\n    if (process.env.NODE_ENV === 'development') {\n      const timestamp = new Date().toISOString();\n\n      if (context) {\n        console.log(`[DEBUG] ${timestamp}`, message, 'Context:', context);\n      } else {\n        console.log(`[DEBUG] ${timestamp}`, message);\n      }\n    }\n  }\n\n  /**\n   * Log error to console with context (legacy method)\n   */\n  static logError(\n    error: any,\n    context: string,\n    additionalData?: any\n  ): void {\n    console.group(`🚨 Error in ${context}`);\n    console.error('Error:', error);\n    if (additionalData) {\n      console.error('Additional Data:', additionalData);\n    }\n    console.error('Stack:', error.stack);\n    console.groupEnd();\n  }\n\n  /**\n   * Log warning with context (legacy method)\n   */\n  static logWarning(\n    message: string,\n    context: string,\n    additionalData?: any\n  ): void {\n    console.group(`⚠️ Warning in ${context}`);\n    console.warn('Message:', message);\n    if (additionalData) {\n      console.warn('Additional Data:', additionalData);\n    }\n    console.groupEnd();\n  }\n}\n\n/**\n * Utility functions for common error handling patterns\n */\nexport const withErrorHandling = <T extends any[], R>(\n  fn: (...args: T) => Promise<R>,\n  errorHandler?: (error: any) => void\n) => {\n  return async (...args: T): Promise<R | undefined> => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      if (errorHandler) {\n        errorHandler(error);\n      } else {\n        ErrorHandler.handleApiError(error, { logError: true });\n      }\n      return undefined;\n    }\n  };\n};\n\nexport const createAsyncErrorHandler = (\n  defaultErrorMessage: string = ERROR_MESSAGES.GENERIC_ERROR\n) => {\n  return (error: any) => {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: defaultErrorMessage,\n      logError: true\n    });\n  };\n};\n\n// Export commonly used error handlers\nexport const handleContentError = ContentErrorHandler.handleGenerationError;\nexport const handleAuthError = AuthErrorHandler.handleAuthError;\nexport const handleNetworkError = NetworkErrorHandler.handleNetworkError;\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD;;;CAGC;;;;;;;;;;;;;;AA0eO;AAxeR;;AAwBO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,gBAAgB;IAChB,eAAe;IACf,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,YAAY,KAAc,EAAE,OAAa,EAAe;QAC7D,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC;QACrC,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,MAAM,cAAc,IAAI,CAAC,sBAAsB,CAAC;QAEhD,MAAM,SAAsB;YAC1B;YACA;YACA;YACA;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,IAAI,CAAC,QAAQ,CAAC,OAAO;QAErB,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,gBAAgB,KAAc,EAAU;QAC7C,IAAI,iBAAiB,OAAO;YAC1B,OAAO,MAAM,OAAO;QACtB;QACA,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;QACT;QACA,IAAI,SAAS,OAAO,UAAU,YAAY,aAAa,OAAO;YAC5D,OAAO,OAAO,AAAC,MAAc,OAAO;QACtC;QACA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,aAAa,KAAc,EAAU;QAC1C,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,OAAO,WAAW;QAEvD,IAAI,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,UAAU;YAC5D,OAAO,WAAW,OAAO;QAC3B;QACA,IAAI,QAAQ,QAAQ,CAAC,iBAAiB,QAAQ,QAAQ,CAAC,YAAY;YACjE,OAAO,WAAW,UAAU;QAC9B;QACA,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,SAAS;YAChE,OAAO,WAAW,cAAc;QAClC;QACA,IAAI,QAAQ,QAAQ,CAAC,cAAc;YACjC,OAAO,WAAW,aAAa;QACjC;QACA,IAAI,QAAQ,QAAQ,CAAC,wBAAwB,QAAQ,QAAQ,CAAC,eAAe;YAC3E,OAAO,WAAW,UAAU;QAC9B;QACA,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,aAAa;YACpE,OAAO,WAAW,MAAM;QAC1B;QACA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,WAAW;YACjE,OAAO,WAAW,MAAM;QAC1B;QAEA,OAAO,WAAW,OAAO;IAC3B;IAEA;;GAEC,GACD,OAAO,uBAAuB,SAAiB,EAAU;QACvD,OAAQ;YACN,KAAK,WAAW,OAAO;gBACrB,OAAO;YACT,KAAK,WAAW,UAAU;gBACxB,OAAO;YACT,KAAK,WAAW,cAAc;gBAC5B,OAAO;YACT,KAAK,WAAW,aAAa;gBAC3B,OAAO;YACT,KAAK,WAAW,UAAU;gBACxB,OAAO;YACT,KAAK,WAAW,MAAM;gBACpB,OAAO;YACT,KAAK,WAAW,MAAM;gBACpB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,OAAO,iBAAiB,KAAc,EAAW;QAC/C,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,OAAO,WAAW;QAEvD,+BAA+B;QAC/B,IAAI,SAAS,WAAW,OAAO,EAAE;YAC/B,OAAO;QACT;QAEA,+BAA+B;QAC/B,IAAI,QAAQ,QAAQ,CAAC,YAAY;YAC/B,OAAO;QACT;QAEA,oCAAoC;QACpC,IAAI,SAAS,WAAW,MAAM,IAAI,QAAQ,QAAQ,CAAC,iBAAiB;YAClE,OAAO;QACT;QAEA,gDAAgD;QAChD,IAAI,SAAS,WAAW,UAAU,EAAE;YAClC,OAAO;QACT;QAEA,kEAAkE;QAClE,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,SAAS,KAAc,EAAE,OAAa,EAAQ;QACnD,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC;QACrC,MAAM,UAAU;YACd;YACA;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO,iBAAiB,QAAQ,MAAM,KAAK,GAAG;QAChD;QAEA,QAAQ,KAAK,CAAC,mBAAmB;IACnC;IAEA;;GAEC,GACD,OAAO,eACL,KAAc,EACd,UAA+B,CAAC,CAAC,EACvB;QACV,MAAM,EACJ,YAAY,KAAK,EACjB,WAAW,IAAI,EACf,kBAAkB,0HAAA,CAAA,iBAAc,CAAC,aAAa,EAC/C,GAAG;QAEJ,IAAI;QAEJ,+BAA+B;QAC/B,MAAM,sBAAsB,CAAC;YAC3B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,cAAc;QAClE;QAEA,MAAM,qBAAqB,CAAC;YAC1B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,aAAa;QACjE;QAEA,MAAM,qBAAqB,CAAC;YAC1B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,aAAa;QACjE;QAEA,IAAI,oBAAoB,QAAQ;YAC9B,sBAAsB;YACtB,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;YACpC,MAAM,OAAO,MAAM,QAAQ,CAAC,IAAI;YAEhC,WAAW;gBACT,SAAS,MAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD,EAAE;gBAClD;gBACA,MAAM,MAAM;gBACZ,SAAS,MAAM;YACjB;QACF,OAAO,IAAI,mBAAmB,QAAQ;YACpC,gBAAgB;YAChB,WAAW;gBACT,SAAS,0HAAA,CAAA,iBAAc,CAAC,aAAa;gBACrC,QAAQ;gBACR,MAAM;YACR;QACF,OAAO;YACL,cAAc;YACd,WAAW;gBACT,SAAS,mBAAmB,SAAS,MAAM,OAAO,GAAG;gBACrD,MAAM;YACR;QACF;QAEA,IAAI,UAAU;YACZ,QAAQ,KAAK,CAAC,cAAc,UAAU;QACxC;QAEA,IAAI,WAAW;QACb,8CAA8C;QAC9C,iCAAiC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,sBACL,MAAmD,EACnD,UAA+B,CAAC,CAAC,EACvB;QACV,MAAM,EAAE,WAAW,IAAI,EAAE,GAAG;QAE5B,IAAI;QAEJ,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,UAAU,OAAO,IAAI,CAAC;QACxB,OAAO,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;YACxD,UAAU,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC;QACvC,OAAO;YACL,UAAU,0HAAA,CAAA,iBAAc,CAAC,gBAAgB;QAC3C;QAEA,MAAM,WAAqB;YACzB;YACA,QAAQ,0HAAA,CAAA,cAAW,CAAC,oBAAoB;YACxC,MAAM;YACN,SAAS,OAAO,WAAW,YAAY,WAAW,OAAO,SAAoC;QAC/F;QAEA,IAAI,UAAU;YACZ,QAAQ,IAAI,CAAC,qBAAqB;QACpC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,iBACX,SAA2B,EAC3B,UAA+B,CAAC,CAAC,EACQ;QACzC,IAAI;YACF,MAAM,OAAO,MAAM;YACnB,OAAO;gBAAE;YAAK;QAChB,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,OAAO;YAC5C,OAAO;gBAAE,OAAO;YAAS;QAC3B;IACF;IAEA;;GAEC,GACD,OAAO,2BACL,iBAAyD,EACzD;QACA,OAAO,CAAC,OAAc;YACpB,QAAQ,KAAK,CAAC,mCAAmC,OAAO;YAExD,iCAAiC;YACjC,4DAA4D;YAE5D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,sBAAsB,KAAU,EAAY;QACjD,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,0HAAA,CAAA,iBAAc,CAAC,iBAAiB;YACjD,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,6BAA6B,MAAW,EAAY;QACzD,OAAO,aAAa,qBAAqB,CAAC,QAAQ;YAChD,UAAU;QACZ;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,gBAAgB,KAAU,EAAY;QAC3C,MAAM,WAAW,aAAa,cAAc,CAAC,OAAO;YAClD,UAAU;QACZ;QAEA,iCAAiC;QACjC,IAAI,SAAS,MAAM,KAAK,0HAAA,CAAA,cAAW,CAAC,YAAY,EAAE;YAChD,oBAAoB;YACpB,aAAa,UAAU,CAAC;QAExB,8BAA8B;QAC9B,yBAAyB;QAC3B;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,wBAA8B;QACnC,aAAa,UAAU,CAAC;IACxB,6BAA6B;IAC7B,4CAA4C;IAC5C,oBAAoB;IACpB,yBAAyB;IAC3B;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,mBAAmB,KAAU,EAAY;QAC9C,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,0HAAA,CAAA,iBAAc,CAAC,aAAa;YAC7C,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,qBAAqB,KAAU,EAAY;QAChD,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,0HAAA,CAAA,iBAAc,CAAC,mBAAmB;YACnD,UAAU;QACZ;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,aAAa,eACX,SAA2B,EAC3B,aAAqB,CAAC,EACtB,YAAoB,IAAI,EACZ;QACZ,IAAI;QAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,OAAO,MAAM;YACf,EAAE,OAAO,OAAO;gBACd,YAAY;gBAEZ,IAAI,YAAY,YAAY;oBAC1B,MAAM;gBACR;gBAEA,sBAAsB;gBACtB,MAAM,QAAQ,YAAY,KAAK,GAAG,CAAC,GAAG;gBACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,MAAM;IACR;IAEA;;GAEC,GACD,OAAO,iBAAiB,KAAU,EAAW;QAC3C,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO,MAAM,+BAA+B;QAC9C;QAEA,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;QACpC,OAAO,UAAU,OAAO,WAAW,0HAAA,CAAA,cAAW,CAAC,iBAAiB;IAClE;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,IAAI,KAAc,EAAE,OAAa,EAAQ;QAC9C,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QAEhE,IAAI,SAAS;YACX,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,YAAY;QAC7D,OAAO;YACL,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE;QACxC;IACF;IAEA;;GAEC,GACD,OAAO,KAAK,OAAe,EAAE,OAAa,EAAQ;QAChD,MAAM,YAAY,IAAI,OAAO,WAAW;QAExC,IAAI,SAAS;YACX,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,YAAY;QAC3D,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE;QACtC;IACF;IAEA;;GAEC,GACD,OAAO,KAAK,OAAe,EAAE,OAAa,EAAQ;QAChD,MAAM,YAAY,IAAI,OAAO,WAAW;QAExC,IAAI,SAAS;YACX,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,YAAY;QAC1D,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE;QACrC;IACF;IAEA;;GAEC,GACD,OAAO,MAAM,OAAe,EAAE,OAAa,EAAQ;QACjD,wCAA4C;YAC1C,MAAM,YAAY,IAAI,OAAO,WAAW;YAExC,IAAI,SAAS;gBACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,YAAY;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE;YACtC;QACF;IACF;IAEA;;GAEC,GACD,OAAO,SACL,KAAU,EACV,OAAe,EACf,cAAoB,EACd;QACN,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,SAAS;QACtC,QAAQ,KAAK,CAAC,UAAU;QACxB,IAAI,gBAAgB;YAClB,QAAQ,KAAK,CAAC,oBAAoB;QACpC;QACA,QAAQ,KAAK,CAAC,UAAU,MAAM,KAAK;QACnC,QAAQ,QAAQ;IAClB;IAEA;;GAEC,GACD,OAAO,WACL,OAAe,EACf,OAAe,EACf,cAAoB,EACd;QACN,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS;QACxC,QAAQ,IAAI,CAAC,YAAY;QACzB,IAAI,gBAAgB;YAClB,QAAQ,IAAI,CAAC,oBAAoB;QACnC;QACA,QAAQ,QAAQ;IAClB;AACF;AAKO,MAAM,oBAAoB,CAC/B,IACA;IAEA,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,IAAI,cAAc;gBAChB,aAAa;YACf,OAAO;gBACL,aAAa,cAAc,CAAC,OAAO;oBAAE,UAAU;gBAAK;YACtD;YACA,OAAO;QACT;IACF;AACF;AAEO,MAAM,0BAA0B,CACrC,sBAA8B,0HAAA,CAAA,iBAAc,CAAC,aAAa;IAE1D,OAAO,CAAC;QACN,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB;YACjB,UAAU;QACZ;IACF;AACF;AAGO,MAAM,qBAAqB,oBAAoB,qBAAqB;AACpE,MAAM,kBAAkB,iBAAiB,eAAe;AACxD,MAAM,qBAAqB,oBAAoB,kBAAkB", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/contentService.ts"], "sourcesContent": ["import { ApiClient } from './apiClient';\nimport { ContentValidation, ValidationUtils } from './validation';\nimport { ContentErrorHandler } from './errorHandling';\nimport { TWITTER_CONSTANTS } from './constants';\nimport {\n  ContentGenerationRequest,\n  ThreadGenerationRequest,\n  ReplyGenerationRequest,\n  ContentResponse,\n  ContentHistoryResponse,\n} from '../types/api';\n\n// Legacy types for backward compatibility\nexport interface ContentRequest {\n  topic: string;\n  tone: 'professional' | 'casual' | 'humorous' | 'inspirational';\n  length: 'short' | 'medium' | 'long';\n  includeHashtags: boolean;\n  includeEmojis: boolean;\n}\n\nexport interface GeneratedContent {\n  id: string;\n  content: string;\n  hashtags: string[];\n  createdAt: string;\n  status: 'draft' | 'scheduled' | 'published';\n}\n\nexport interface ScheduleRequest {\n  contentId: string;\n  scheduledTime: string;\n}\n\nexport interface ContentFilters {\n  status?: 'draft' | 'scheduled' | 'published';\n  dateFrom?: string;\n  dateTo?: string;\n  limit?: number;\n  offset?: number;\n}\n\n// Content Service following Single Responsibility Principle\nexport class ContentService {\n  constructor(private apiClient: ApiClient) {}\n\n  // New backend API methods with error handling\n  async generateTweet(request: ContentGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateTweetRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-tweet', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async generateThread(request: ThreadGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateThreadRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-thread', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async generateReply(request: ReplyGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateReplyRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-reply', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async getContentHistory(skip: number = 0, limit: number = 50): Promise<ContentHistoryResponse> {\n    const params = new URLSearchParams({\n      skip: skip.toString(),\n      limit: limit.toString()\n    });\n    return this.apiClient.get<ContentHistoryResponse>(`/content/history?${params}`);\n  }\n\n  // Legacy methods for backward compatibility\n  async generateContent(request: ContentRequest): Promise<GeneratedContent> {\n    this.validateContentRequest(request);\n    // Convert legacy request to new format\n    const newRequest: ContentGenerationRequest = {\n      topic: request.topic,\n      style: request.tone,\n      user_context: `Length: ${request.length}, Include hashtags: ${request.includeHashtags}, Include emojis: ${request.includeEmojis}`\n    };\n\n    const response = await this.generateTweet(newRequest);\n\n    // Convert response to legacy format\n    return {\n      id: Date.now().toString(),\n      content: response.content || '',\n      hashtags: [],\n      createdAt: new Date().toISOString(),\n      status: 'draft'\n    };\n  }\n\n  // TODO: Implement these methods when backend endpoints are ready\n  // For now, these are removed to eliminate dead code\n  //\n  // Future implementations:\n  // - getContent(): Use history endpoint with filtering\n  // - getContentById(): Implement backend endpoint for single content retrieval\n  // - updateContent(): Implement backend endpoint for content updates\n  // - scheduleContent(): Use scheduled posts API\n  // - deleteContent(): Implement backend endpoint for content deletion\n  // - publishContent(): Use Twitter posting API\n\n  // Private validation methods using centralized validation utilities\n  private validateTweetRequest(request: ContentGenerationRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n  }\n\n  private validateThreadRequest(request: ThreadGenerationRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n\n    if (request.num_tweets) {\n      if (request.num_tweets < 2) {\n        throw new Error('Thread must contain at least 2 tweets');\n      }\n      if (request.num_tweets > TWITTER_CONSTANTS.MAX_THREAD_TWEETS) {\n        throw new Error(`Thread cannot exceed ${TWITTER_CONSTANTS.MAX_THREAD_TWEETS} tweets`);\n      }\n    }\n  }\n\n  private validateReplyRequest(request: ReplyGenerationRequest): void {\n    const requiredValidation = ValidationUtils.validateRequired(request.original_tweet, 'Original tweet');\n    if (!requiredValidation.isValid) {\n      throw new Error(requiredValidation.error);\n    }\n\n    const lengthValidation = ValidationUtils.validateLength(\n      request.original_tweet,\n      1,\n      TWITTER_CONSTANTS.MAX_TWEET_LENGTH\n    );\n    if (!lengthValidation.isValid) {\n      throw new Error(lengthValidation.error);\n    }\n  }\n\n  private validateContentRequest(request: ContentRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n  }\n\n  // Removed unused validation methods to eliminate dead code\n\n  private buildQueryParams(filters?: ContentFilters): string {\n    if (!filters) return '';\n\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, String(value));\n      }\n    });\n\n    const queryString = params.toString();\n    return queryString ? `?${queryString}` : '';\n  }\n}\n\n// Export singleton instance\nimport { apiClient } from './apiClient';\nexport const contentService = new ContentService(apiClient);\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAgLA,4BAA4B;AAC5B;;;;AAzIO,MAAM;;IACX,YAAY,AAAQ,SAAoB,CAAE;aAAtB,YAAA;IAAuB;IAE3C,8CAA8C;IAC9C,MAAM,cAAc,OAAiC,EAA4B;QAC/E,IAAI;YACF,IAAI,CAAC,oBAAoB,CAAC;YAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,2BAA2B;QAC/E,EAAE,OAAO,OAAO;YACd,MAAM,8HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,eAAe,OAAgC,EAA4B;QAC/E,IAAI;YACF,IAAI,CAAC,qBAAqB,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,4BAA4B;QAChF,EAAE,OAAO,OAAO;YACd,MAAM,8HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,cAAc,OAA+B,EAA4B;QAC7E,IAAI;YACF,IAAI,CAAC,oBAAoB,CAAC;YAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,2BAA2B;QAC/E,EAAE,OAAO,OAAO;YACd,MAAM,8HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,kBAAkB,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAmC;QAC7F,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAyB,CAAC,iBAAiB,EAAE,QAAQ;IAChF;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB,OAAuB,EAA6B;QACxE,IAAI,CAAC,sBAAsB,CAAC;QAC5B,uCAAuC;QACvC,MAAM,aAAuC;YAC3C,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,IAAI;YACnB,cAAc,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,oBAAoB,EAAE,QAAQ,eAAe,CAAC,kBAAkB,EAAE,QAAQ,aAAa,EAAE;QACnI;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;QAE1C,oCAAoC;QACpC,OAAO;YACL,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS,SAAS,OAAO,IAAI;YAC7B,UAAU,EAAE;YACZ,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;QACV;IACF;IAEA,iEAAiE;IACjE,oDAAoD;IACpD,EAAE;IACF,0BAA0B;IAC1B,sDAAsD;IACtD,8EAA8E;IAC9E,oEAAoE;IACpE,+CAA+C;IAC/C,qEAAqE;IACrE,8CAA8C;IAE9C,oEAAoE;IAC5D,qBAAqB,OAAiC,EAAQ;QACpE,MAAM,kBAAkB,2HAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;IACF;IAEQ,sBAAsB,OAAgC,EAAQ;QACpE,MAAM,kBAAkB,2HAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,IAAI,QAAQ,UAAU,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,QAAQ,UAAU,GAAG,0HAAA,CAAA,oBAAiB,CAAC,iBAAiB,EAAE;gBAC5D,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,0HAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACtF;QACF;IACF;IAEQ,qBAAqB,OAA+B,EAAQ;QAClE,MAAM,qBAAqB,2HAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,QAAQ,cAAc,EAAE;QACpF,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC/B,MAAM,IAAI,MAAM,mBAAmB,KAAK;QAC1C;QAEA,MAAM,mBAAmB,2HAAA,CAAA,kBAAe,CAAC,cAAc,CACrD,QAAQ,cAAc,EACtB,GACA,0HAAA,CAAA,oBAAiB,CAAC,gBAAgB;QAEpC,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B,MAAM,IAAI,MAAM,iBAAiB,KAAK;QACxC;IACF;IAEQ,uBAAuB,OAAuB,EAAQ;QAC5D,MAAM,kBAAkB,2HAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;IACF;IAEA,2DAA2D;IAEnD,iBAAiB,OAAwB,EAAU;QACzD,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,OAAO;YAC5B;QACF;QAEA,MAAM,cAAc,OAAO,QAAQ;QACnC,OAAO,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG;IAC3C;AACF;;AAIO,MAAM,iBAAiB,IAAI,eAAe,0HAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ConnectionTest.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { apiClient } from '../lib/apiClient';\nimport { authService, LoginResponse } from '../lib/authService';\nimport { contentService } from '../lib/contentService';\nimport { HealthCheckResponse, ContentHistoryResponse } from '../types/api';\n\ninterface TestResult {\n  name: string;\n  status: 'pending' | 'success' | 'error';\n  message?: string;\n}\n\nexport default function ConnectionTest() {\n  const [tests, setTests] = useState<TestResult[]>([\n    { name: 'Backend Health Check', status: 'pending' },\n    { name: 'API Configuration', status: 'pending' },\n    { name: 'Authentication Test', status: 'pending' },\n    { name: 'Content Service Test', status: 'pending' },\n  ]);\n\n  const [isRunning, setIsRunning] = useState(false);\n  const [authToken, setAuthToken] = useState<string | null>(null);\n\n  const updateTest = (index: number, status: TestResult['status'], message?: string) => {\n    setTests(prev => prev.map((test, i) =>\n      i === index ? { ...test, status, message } : test\n    ));\n  };\n\n  const runTests = async () => {\n    setIsRunning(true);\n\n    // Reset all tests\n    setTests(prev => prev.map(test => ({ ...test, status: 'pending' })));\n\n    try {\n      // Test 1: Backend Health Check\n      try {\n        // Use the API health endpoint\n        const health = await apiClient.get<HealthCheckResponse>('/health');\n        if (health && health.status === 'healthy') {\n          updateTest(0, 'success', `Backend is healthy (API: ${health.api || 'ready'})`);\n        } else {\n          updateTest(0, 'error', 'Backend health check failed');\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        updateTest(0, 'error', `Backend not accessible: ${errorMessage}`);\n      }\n\n      // Test 2: API Configuration\n      try {\n        const baseURL = apiClient.instance.defaults.baseURL;\n        if (baseURL?.includes('8000')) {\n          updateTest(1, 'success', `API URL: ${baseURL}`);\n        } else {\n          updateTest(1, 'error', `Incorrect API URL: ${baseURL}`);\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        updateTest(1, 'error', `API configuration error: ${errorMessage}`);\n      }\n\n      // Test 3: Authentication Test\n      try {\n        const testUser = {\n          username: `test_${Date.now()}`,\n          email: `test_${Date.now()}@example.com`,\n          password: 'testpass123',\n          full_name: 'Test User'\n        };\n\n        // Try to register (might fail if user exists)\n        try {\n          await authService.register(testUser);\n        } catch (error: unknown) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n          if (!errorMessage.includes('already registered')) {\n            throw error;\n          }\n        }\n\n        // Try to login\n        const loginResult: LoginResponse = await authService.login({\n          username: testUser.username,\n          password: testUser.password\n        });\n\n        if (loginResult.access_token) {\n          setAuthToken(loginResult.access_token);\n          updateTest(2, 'success', 'Authentication successful');\n        } else {\n          updateTest(2, 'error', 'Login failed');\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        updateTest(2, 'error', `Authentication failed: ${errorMessage}`);\n      }\n\n      // Test 4: Content Service Test\n      try {\n        if (authService.isAuthenticated()) {\n          const history: ContentHistoryResponse = await contentService.getContentHistory(0, 5);\n          updateTest(3, 'success', `Content history retrieved (${history.total || 0} items)`);\n        } else {\n          updateTest(3, 'error', 'Not authenticated for content test');\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        updateTest(3, 'error', `Content service failed: ${errorMessage}`);\n      }\n\n    } catch (error: unknown) {\n      console.error('Test suite error:', error);\n    } finally {\n      setIsRunning(false);\n    }\n  };\n\n  const testContentGeneration = async () => {\n    if (!authService.isAuthenticated()) {\n      alert('Please run authentication test first');\n      return;\n    }\n\n    try {\n      const result = await contentService.generateTweet({\n        topic: 'Testing AutoReach connection',\n        style: 'professional',\n        language: 'en'\n      });\n\n      if (result.success && result.content) {\n        alert(`Content generated successfully:\\n\\n${result.content}`);\n      } else {\n        alert(`Content generation failed: ${result.error || 'Unknown error'}`);\n      }\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      alert(`Content generation error: ${errorMessage}`);\n    }\n  };\n\n  const getStatusIcon = (status: TestResult['status']) => {\n    switch (status) {\n      case 'pending': return '⏳';\n      case 'success': return '✅';\n      case 'error': return '❌';\n    }\n  };\n\n  const getStatusColor = (status: TestResult['status']) => {\n    switch (status) {\n      case 'pending': return 'text-yellow-600';\n      case 'success': return 'text-green-600';\n      case 'error': return 'text-red-600';\n    }\n  };\n\n  return (\n    <div className=\"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg\">\n      <h2 className=\"text-2xl font-bold mb-6 text-gray-800\">\n        Frontend-Backend Connection Test\n      </h2>\n\n      <div className=\"space-y-4 mb-6\">\n        {tests.map((test, index) => (\n          <div key={index} className=\"flex items-center space-x-3 p-3 border rounded-lg\">\n            <span className=\"text-2xl\">{getStatusIcon(test.status)}</span>\n            <div className=\"flex-1\">\n              <div className={`font-medium ${getStatusColor(test.status)}`}>\n                {test.name}\n              </div>\n              {test.message && (\n                <div className=\"text-sm text-gray-600 mt-1\">\n                  {test.message}\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"flex space-x-4\">\n        <button\n          onClick={runTests}\n          disabled={isRunning}\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isRunning ? 'Running Tests...' : 'Run Connection Tests'}\n        </button>\n\n        <button\n          onClick={testContentGeneration}\n          disabled={isRunning || !authService.isAuthenticated()}\n          className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          Test Content Generation\n        </button>\n      </div>\n\n      <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n        <h3 className=\"font-medium text-gray-800 mb-2\">Connection Status</h3>\n        <div className=\"text-sm text-gray-600 space-y-1\">\n          <div>Backend URL: {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'}</div>\n          <div>Authentication: {authService.isAuthenticated() ? '✅ Authenticated' : '❌ Not authenticated'}</div>\n          {authToken && (\n            <div className=\"break-all\">Token: {authToken.substring(0, 20)}...</div>\n          )}\n        </div>\n      </div>\n\n      <div className=\"mt-4 text-xs text-gray-500\">\n        <p>This component tests the connection between the Next.js frontend and FastAPI backend.</p>\n        <p>Make sure the backend is running on port 8000 before testing.</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AA8M6B;;AA5M7B;AACA;AACA;AACA;;;AALA;;;;;AAce,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC/C;YAAE,MAAM;YAAwB,QAAQ;QAAU;QAClD;YAAE,MAAM;YAAqB,QAAQ;QAAU;QAC/C;YAAE,MAAM;YAAuB,QAAQ;QAAU;QACjD;YAAE,MAAM;YAAwB,QAAQ;QAAU;KACnD;IAED,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,aAAa,CAAC,OAAe,QAA8B;QAC/D,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAC,MAAM,IAC/B,MAAM,QAAQ;oBAAE,GAAG,IAAI;oBAAE;oBAAQ;gBAAQ,IAAI;IAEjD;IAEA,MAAM,WAAW;QACf,aAAa;QAEb,kBAAkB;QAClB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,CAAC;QAEjE,IAAI;YACF,+BAA+B;YAC/B,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,SAAS,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAsB;gBACxD,IAAI,UAAU,OAAO,MAAM,KAAK,WAAW;oBACzC,WAAW,GAAG,WAAW,CAAC,yBAAyB,EAAE,OAAO,GAAG,IAAI,QAAQ,CAAC,CAAC;gBAC/E,OAAO;oBACL,WAAW,GAAG,SAAS;gBACzB;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAAE,cAAc;YAClE;YAEA,4BAA4B;YAC5B,IAAI;gBACF,MAAM,UAAU,0HAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO;gBACnD,IAAI,SAAS,SAAS,SAAS;oBAC7B,WAAW,GAAG,WAAW,CAAC,SAAS,EAAE,SAAS;gBAChD,OAAO;oBACL,WAAW,GAAG,SAAS,CAAC,mBAAmB,EAAE,SAAS;gBACxD;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,WAAW,GAAG,SAAS,CAAC,yBAAyB,EAAE,cAAc;YACnE;YAEA,8BAA8B;YAC9B,IAAI;gBACF,MAAM,WAAW;oBACf,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;oBAC9B,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,YAAY,CAAC;oBACvC,UAAU;oBACV,WAAW;gBACb;gBAEA,8CAA8C;gBAC9C,IAAI;oBACF,MAAM,4HAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBAC7B,EAAE,OAAO,OAAgB;oBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAC9D,IAAI,CAAC,aAAa,QAAQ,CAAC,uBAAuB;wBAChD,MAAM;oBACR;gBACF;gBAEA,eAAe;gBACf,MAAM,cAA6B,MAAM,4HAAA,CAAA,cAAW,CAAC,KAAK,CAAC;oBACzD,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;gBAC7B;gBAEA,IAAI,YAAY,YAAY,EAAE;oBAC5B,aAAa,YAAY,YAAY;oBACrC,WAAW,GAAG,WAAW;gBAC3B,OAAO;oBACL,WAAW,GAAG,SAAS;gBACzB;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,WAAW,GAAG,SAAS,CAAC,uBAAuB,EAAE,cAAc;YACjE;YAEA,+BAA+B;YAC/B,IAAI;gBACF,IAAI,4HAAA,CAAA,cAAW,CAAC,eAAe,IAAI;oBACjC,MAAM,UAAkC,MAAM,+HAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC,GAAG;oBAClF,WAAW,GAAG,WAAW,CAAC,2BAA2B,EAAE,QAAQ,KAAK,IAAI,EAAE,OAAO,CAAC;gBACpF,OAAO;oBACL,WAAW,GAAG,SAAS;gBACzB;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAAE,cAAc;YAClE;QAEF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,4HAAA,CAAA,cAAW,CAAC,eAAe,IAAI;YAClC,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,+HAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;gBAChD,OAAO;gBACP,OAAO;gBACP,UAAU;YACZ;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,EAAE;gBACpC,MAAM,CAAC,mCAAmC,EAAE,OAAO,OAAO,EAAE;YAC9D,OAAO;gBACL,MAAM,CAAC,2BAA2B,EAAE,OAAO,KAAK,IAAI,iBAAiB;YACvE;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,CAAC,0BAA0B,EAAE,cAAc;QACnD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;QACvB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;QACvB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAItD,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;wBAAgB,WAAU;;0CACzB,6LAAC;gCAAK,WAAU;0CAAY,cAAc,KAAK,MAAM;;;;;;0CACrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,YAAY,EAAE,eAAe,KAAK,MAAM,GAAG;kDACzD,KAAK,IAAI;;;;;;oCAEX,KAAK,OAAO,kBACX,6LAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO;;;;;;;;;;;;;uBARX;;;;;;;;;;0BAgBd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,YAAY,qBAAqB;;;;;;kCAGpC,6LAAC;wBACC,SAAS;wBACT,UAAU,aAAa,CAAC,4HAAA,CAAA,cAAW,CAAC,eAAe;wBACnD,WAAU;kCACX;;;;;;;;;;;;0BAKH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAI;oCAAc,iEAAmC;;;;;;;0CACtD,6LAAC;;oCAAI;oCAAiB,4HAAA,CAAA,cAAW,CAAC,eAAe,KAAK,oBAAoB;;;;;;;4BACzE,2BACC,6LAAC;gCAAI,WAAU;;oCAAY;oCAAQ,UAAU,SAAS,CAAC,GAAG;oCAAI;;;;;;;;;;;;;;;;;;;0BAKpE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAE;;;;;;kCACH,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;AAIX;GA9MwB;KAAA", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/src/client/use-merged-ref.ts"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup"], "mappings": ";;;;+BASgBA,gBAAAA;;;eAAAA;;;uBAT8B;AASvC,SAASA,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAC7C,MAAMC,WAAWD,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAE7C,mFAAmF;IACnF,yEAAyE;IACzE,iGAAiG;IACjG,8FAA8F;IAC9F,gDAAgD;IAChD,mGAAmG;IACnG,wFAAwF;IACxF,OAAOE,CAAAA,GAAAA,OAAAA,WAAW,EAChB,CAACC;QACC,IAAIA,YAAY,MAAM;YACpB,MAAMC,aAAaL,SAASI,OAAO;YACnC,IAAIC,YAAY;gBACdL,SAASI,OAAO,GAAG;gBACnBC;YACF;YACA,MAAMC,aAAaJ,SAASE,OAAO;YACnC,IAAIE,YAAY;gBACdJ,SAASE,OAAO,GAAG;gBACnBE;YACF;QACF,OAAO;YACL,IAAIR,MAAM;gBACRE,SAASI,OAAO,GAAGG,SAAST,MAAMM;YACpC;YACA,IAAIL,MAAM;gBACRG,SAASE,OAAO,GAAGG,SAASR,MAAMK;YACpC;QACF;IACF,GACA;QAACN;QAAMC;KAAK;AAEhB;AAEA,SAASQ,SACPT,IAAgC,EAChCM,OAAiB;IAEjB,IAAI,OAAON,SAAS,YAAY;QAC9B,MAAMU,UAAUV,KAAKM;QACrB,IAAI,OAAOI,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMV,KAAK;QACpB;IACF,OAAO;QACLA,KAAKM,OAAO,GAAGA;QACf,OAAO;YACLN,KAAKM,OAAO,GAAG;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1843, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMuE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAmTA;;;;;;;;;CASC,GACD,OAyZC,EAAA;eAzZuBzE;;IA+ZXC,aAAa,EAAA;eAAbA;;;;;iEA1tB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,MAAME,WAAW;QACf,IAAIR,YAAY;YACd,IAAIS,qBAAqB;YAEzBT,WAAW;gBACTM,gBAAgB;oBACdG,qBAAqB;gBACvB;YACF;YAEA,IAAIA,oBAAoB;gBACtB;YACF;QACF;QAEAC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBd,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBc,OAAO;IAE3B;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAACL;AACxB;AAEA,SAASM,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASpC,cACtBsC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMzB,kBAAkB0B,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ5B,MAAM6B,QAAQ,EACd5B,IAAI6B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR/B,OAAO,EACPgC,OAAO,EACP/B,MAAM,EACNgC,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBpC,UAAU,EACVqC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAS9B,OAAAA,OAAK,CAAC+B,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,OAAOmB,oBAAAA,YAAY,CAACC,IAAI,GAAGD,oBAAAA,YAAY,CAACE,IAAI;IAE/D,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DjE,MAAM;QACR;QACA,MAAMkE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DtE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR+B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBpC,YAAY;QACd;QACA,MAAMmE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI9B;YACJ,IAAI,OAAO6B,aAAa,UAAU;gBAChC7B,OAAO6B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA5E,OAAO6B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI5E,MAAM;gBACR,MAAM6E,oBAAoB7E,KACvB8E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB5D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGgB,OAAAA,OAAK,CAACkE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL7B,MAAMoF;gBACNnF,IAAI6B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQpE,OAAAA,OAAK,CAACuE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;QAEP;IACF,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B5E,OAAAA,OAAK,CAAC6E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB7C,gBAAgBc,OAAO,GAAGgF,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA/F,MACA+C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAItB,gBAAgBc,OAAO,EAAE;wBAC3BiF,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC/F,gBAAgBc,OAAO;wBACvDd,gBAAgBc,OAAO,GAAG;oBAC5B;oBACAkF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBlD;QAAM+C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQrC,CAAC;YACP,IAAIwD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAAC1D,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI6D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQrC;YACV;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACrC;YACtB;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAIhD,EAAEuG,gBAAgB,EAAE;gBACtB;YACF;YAEAxG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACAgC,cAAatC,CAAC;YACZ,IAAI,CAAC0C,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBvC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACtC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,GAChDC,oCACA,SAASnE,aAAaxC,CAAC;YACrB,IAAI,CAAC0C,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBzC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACxC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBzG,EAAEV,aAAa,EACfkH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAAC1G,KAAK;QACrBoG,WAAWrG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACwC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWrG,IAAI,GAAG4G,CAAAA,GAAAA,aAAAA,WAAW,EAAC3G;IAChC;IAEA,IAAI4G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO5F,OAAAA,OAAK,CAAC8F,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMzC,gBAAgB;IAC3B,OAAO+D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}]}