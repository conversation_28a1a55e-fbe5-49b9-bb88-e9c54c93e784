import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Remove static export for full-stack functionality
  // output: 'export', // Commented out to enable server-side features

  // Ensure proper trailing slash handling
  trailingSlash: false,

  // Image optimization for production
  images: {
    unoptimized: false // Enable optimization for better performance
  },

  // Environment variables
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'https://reachly-backend.onrender.com',
  },

  // Enable React strict mode for better development
  reactStrictMode: true,

  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: false, // Enable ESLint during builds
  },
};

export default nextConfig;
