(()=>{var e={};e.id=352,e.ids=[352],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},18835:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413);function i(){return(0,s.jsxs)("div",{style:{padding:"20px",fontFamily:"Arial, sans-serif"},children:[(0,s.jsx)("h1",{children:"\uD83C\uDF89 Frontend is Working!"}),(0,s.jsx)("p",{children:"If you can see this page, the Next.js app is running correctly."}),(0,s.jsxs)("p",{children:["Current time: ",new Date().toISOString()]}),(0,s.jsxs)("p",{children:["Environment: ","production"]}),(0,s.jsxs)("p",{children:["API URL: ","http://localhost:8000/api"]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43802:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>l,tree:()=>u});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),p={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>a[e]);r.d(t,p);let u={children:["",{children:["test-simple",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,18835)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-simple\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-simple\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-simple/page",pathname:"/test-simple",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86364:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,786,658,70],()=>r(43802));module.exports=s})();