{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  href?: string;\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n}\n\nconst Logo = ({ href = '/dashboard', size = 'md', showText = true }: LogoProps) => {\n  const sizes = {\n    sm: { icon: 'w-6 h-6', text: 'text-lg' },\n    md: { icon: 'w-8 h-8', text: 'text-xl' },\n    lg: { icon: 'w-10 h-10', text: 'text-2xl' },\n  };\n\n  const logoContent = (\n    <div className=\"flex items-center space-x-2 select-none\">\n      <div className={`${sizes[size].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`}>\n        <span className={`text-white font-bold ${size === 'sm' ? 'text-sm' : 'text-lg'}`}>A</span>\n      </div>\n      {showText && (\n        <span className={`${sizes[size].text} font-bold text-gray-900 whitespace-nowrap`}>AutoReach</span>\n      )}\n    </div>\n  );\n\n  if (href) {\n    return (\n      <Link href={href}>\n        {logoContent}\n      </Link>\n    );\n  }\n\n  return logoContent;\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,OAAO,CAAC,EAAE,OAAO,YAAY,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI,EAAa;IAC5E,MAAM,QAAQ;QACZ,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAa,MAAM;QAAW;IAC5C;IAEA,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,yEAAyE,CAAC;0BAC5G,cAAA,8OAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,SAAS,OAAO,YAAY,WAAW;8BAAE;;;;;;;;;;;YAEnF,0BACC,8OAAC;gBAAK,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC;0BAAE;;;;;;;;;;;;IAKxF,IAAI,MAAM;QACR,qBACE,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;sBACT;;;;;;IAGP;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useState } from 'react';\nimport { NAVIGATION_ITEMS } from '@/lib/constants';\nimport Logo from '@/components/ui/Logo';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface HeaderProps {\n  showNavigation?: boolean;\n}\n\nconst Header = ({ showNavigation = true }: HeaderProps) => {\n  const pathname = usePathname();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const { user, logout, isAuthenticated } = useAuth();\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 relative z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n          </div>\n\n          {/* Desktop Navigation */}\n          {showNavigation && (\n            <nav className=\"hidden md:flex space-x-6\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.path}\n                  href={item.path}\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    pathname === item.path\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  } ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100' : ''}`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          )}\n\n          {/* Mobile menu button and User menu */}\n          <div className=\"flex items-center space-x-2\">\n            {/* Mobile menu button */}\n            {showNavigation && (\n              <button\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                aria-label=\"Toggle mobile menu\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  {isMobileMenuOpen ? (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  ) : (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  )}\n                </svg>\n              </button>\n            )}\n\n            {/* User menu */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md\"\n                >\n                  <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-purple-600\">\n                      {user?.full_name?.[0] || user?.username?.[0] || 'U'}\n                    </span>\n                  </div>\n                  <span className=\"hidden sm:block text-sm font-medium\">\n                    {user?.full_name || user?.username}\n                  </span>\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* User dropdown menu */}\n                {isUserMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200\">\n                    <div className=\"px-4 py-2 text-sm text-gray-700 border-b border-gray-100\">\n                      <p className=\"font-medium\">{user?.full_name || user?.username}</p>\n                      {user?.twitter_username && (\n                        <p className=\"text-gray-500\">@{user.twitter_username}</p>\n                      )}\n                    </div>\n                    <Link\n                      href=\"/settings\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Settings\n                    </Link>\n                    <button\n                      onClick={() => {\n                        setIsUserMenuOpen(false);\n                        logout();\n                      }}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <Link\n                href=\"/login\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        </div>\n\n        {/* Mobile Navigation Menu */}\n        {showNavigation && isMobileMenuOpen && (\n          <div className=\"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg\">\n            <nav className=\"px-4 py-4 space-y-2\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.path}\n                  href={item.path}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`block px-4 py-3 rounded-lg text-base font-medium transition-colors ${\n                    pathname === item.path\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  } ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100 border border-green-200' : ''}`}\n                >\n                  <span className=\"flex items-center gap-3\">\n                    <span className=\"text-lg\">\n                      {item.name === 'Dashboard' && '📊'}\n                      {item.name === 'Content' && '✍️'}\n                      {item.name === 'Analytics' && '📈'}\n                      {item.name === 'Settings' && '⚙️'}\n                      {item.name === 'Auth' && '🔐'}\n                      {item.name === 'Test API' && '🧪'}\n                    </span>\n                    {item.name}\n                  </span>\n                </Link>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaA,MAAM,SAAS,CAAC,EAAE,iBAAiB,IAAI,EAAe;IACpD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;;;;;;;;;;wBAIN,gCACC,8OAAC;4BAAI,WAAU;sCACZ,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,IAAI,GAClB,mCACA,qDACL,CAAC,EAAE,KAAK,IAAI,KAAK,aAAa,kDAAkD,IAAI;8CAEpF,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAetB,8OAAC;4BAAI,WAAU;;gCAEZ,gCACC,8OAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChE,iCACC,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;iEAErE,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;gCAO5E,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,MAAM,WAAW,CAAC,EAAE,IAAI,MAAM,UAAU,CAAC,EAAE,IAAI;;;;;;;;;;;8DAGpD,8OAAC;oDAAK,WAAU;8DACb,MAAM,aAAa,MAAM;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAKxE,gCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAe,MAAM,aAAa,MAAM;;;;;;wDACpD,MAAM,kCACL,8OAAC;4DAAE,WAAU;;gEAAgB;gEAAE,KAAK,gBAAgB;;;;;;;;;;;;;8DAGxD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;wDACP,kBAAkB;wDAClB;oDACF;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;yDAOP,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;gBAQN,kBAAkB,kCACjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,oBAAoB;gCACnC,WAAW,CAAC,mEAAmE,EAC7E,aAAa,KAAK,IAAI,GAClB,mCACA,qDACL,CAAC,EAAE,KAAK,IAAI,KAAK,aAAa,0EAA0E,IAAI;0CAE7G,cAAA,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAK,WAAU;;gDACb,KAAK,IAAI,KAAK,eAAe;gDAC7B,KAAK,IAAI,KAAK,aAAa;gDAC3B,KAAK,IAAI,KAAK,eAAe;gDAC7B,KAAK,IAAI,KAAK,cAAc;gDAC5B,KAAK,IAAI,KAAK,UAAU;gDACxB,KAAK,IAAI,KAAK,cAAc;;;;;;;wCAE9B,KAAK,IAAI;;;;;;;+BAlBP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BhC;uCAEe", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/twitterAuthService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\n\nexport interface TwitterAuthResponse {\n  authorization_url: string;\n  oauth_token: string;\n  oauth_token_secret: string;\n}\n\nexport interface TwitterStatus {\n  connected: boolean;\n  twitter_username?: string;\n  twitter_user_id?: string;\n}\n\nexport interface TwitterCallbackData {\n  oauth_token: string;\n  oauth_verifier: string;\n  oauth_token_secret: string;\n  [key: string]: unknown;\n}\n\nclass TwitterAuthService {\n  /**\n   * Initiate Twitter OAuth flow\n   * Returns authorization URL and OAuth tokens\n   */\n  async initiateTwitterAuth(): Promise<TwitterAuthResponse> {\n    try {\n      const response = await apiClient.get<TwitterAuthResponse>('/auth/twitter/login');\n      return response;\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      throw new Error(`Failed to initiate Twitter auth: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * Handle Twitter OAuth callback\n   * Exchange OAuth verifier for access tokens\n   */\n  async handleTwitterCallback(callbackData: TwitterCallbackData): Promise<{\n    message: string;\n    twitter_username?: string;\n    twitter_user_id?: string;\n  }> {\n    try {\n      const response = await apiClient.post<{\n        message: string;\n        twitter_username?: string;\n        twitter_user_id?: string;\n      }>('/auth/twitter/callback', callbackData);\n      return response;\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      throw new Error(`Failed to handle Twitter callback: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * Get Twitter connection status\n   */\n  async getTwitterStatus(): Promise<TwitterStatus> {\n    try {\n      const response = await apiClient.get<TwitterStatus>('/auth/twitter/status');\n      return response;\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      throw new Error(`Failed to get Twitter status: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * Disconnect Twitter account\n   */\n  async disconnectTwitter(): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete<{ message: string }>('/auth/twitter/disconnect');\n      return response;\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      throw new Error(`Failed to disconnect Twitter: ${errorMessage}`);\n    }\n  }\n\n  /**\n   * Start Twitter OAuth flow\n   * Opens Twitter authorization in a new window\n   */\n  async startTwitterOAuth(): Promise<{\n    oauth_token: string;\n    oauth_token_secret: string;\n  }> {\n    const authData = await this.initiateTwitterAuth();\n\n    // Store oauth_token_secret for later use\n    sessionStorage.setItem('twitter_oauth_token_secret', authData.oauth_token_secret);\n\n    // Open Twitter authorization in a new window\n    const authWindow = window.open(\n      authData.authorization_url,\n      'twitter_auth',\n      'width=600,height=600,scrollbars=yes,resizable=yes'\n    );\n\n    return new Promise((resolve, reject) => {\n      // Listen for the callback\n      const checkClosed = setInterval(() => {\n        if (authWindow?.closed) {\n          clearInterval(checkClosed);\n          reject(new Error('Twitter authorization was cancelled'));\n        }\n      }, 1000);\n\n      // Listen for messages from the popup\n      const messageListener = (event: MessageEvent) => {\n        if (event.origin !== window.location.origin) return;\n\n        if (event.data.type === 'TWITTER_AUTH_SUCCESS') {\n          clearInterval(checkClosed);\n          window.removeEventListener('message', messageListener);\n          authWindow?.close();\n          resolve({\n            oauth_token: authData.oauth_token,\n            oauth_token_secret: authData.oauth_token_secret\n          });\n        } else if (event.data.type === 'TWITTER_AUTH_ERROR') {\n          clearInterval(checkClosed);\n          window.removeEventListener('message', messageListener);\n          authWindow?.close();\n          reject(new Error(event.data.error || 'Twitter authorization failed'));\n        }\n      };\n\n      window.addEventListener('message', messageListener);\n    });\n  }\n\n  /**\n   * Complete Twitter OAuth flow with verifier\n   */\n  async completeTwitterOAuth(oauth_verifier: string): Promise<{\n    message: string;\n    twitter_username?: string;\n    twitter_user_id?: string;\n  }> {\n    const oauth_token_secret = sessionStorage.getItem('twitter_oauth_token_secret');\n    if (!oauth_token_secret) {\n      throw new Error('OAuth token secret not found. Please restart the authentication process.');\n    }\n\n    // Extract oauth_token from URL if needed\n    const urlParams = new URLSearchParams(window.location.search);\n    const oauth_token = urlParams.get('oauth_token');\n\n    if (!oauth_token) {\n      throw new Error('OAuth token not found in callback URL');\n    }\n\n    const result = await this.handleTwitterCallback({\n      oauth_token,\n      oauth_verifier,\n      oauth_token_secret\n    });\n\n    // Clean up stored token\n    sessionStorage.removeItem('twitter_oauth_token_secret');\n\n    return result;\n  }\n}\n\nexport const twitterAuthService = new TwitterAuthService();\nexport default twitterAuthService;\n"], "names": [], "mappings": ";;;;AAAA;;AAqBA,MAAM;IACJ;;;GAGC,GACD,MAAM,sBAAoD;QACxD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAsB;YAC1D,OAAO;QACT,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,cAAc;QACpE;IACF;IAEA;;;GAGC,GACD,MAAM,sBAAsB,YAAiC,EAI1D;QACD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAIlC,0BAA0B;YAC7B,OAAO;QACT,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,cAAc;QACtE;IACF;IAEA;;GAEC,GACD,MAAM,mBAA2C;QAC/C,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAgB;YACpD,OAAO;QACT,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,cAAc;QACjE;IACF;IAEA;;GAEC,GACD,MAAM,oBAAkD;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAsB;YAC7D,OAAO;QACT,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,cAAc;QACjE;IACF;IAEA;;;GAGC,GACD,MAAM,oBAGH;QACD,MAAM,WAAW,MAAM,IAAI,CAAC,mBAAmB;QAE/C,yCAAyC;QACzC,eAAe,OAAO,CAAC,8BAA8B,SAAS,kBAAkB;QAEhF,6CAA6C;QAC7C,MAAM,aAAa,OAAO,IAAI,CAC5B,SAAS,iBAAiB,EAC1B,gBACA;QAGF,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,0BAA0B;YAC1B,MAAM,cAAc,YAAY;gBAC9B,IAAI,YAAY,QAAQ;oBACtB,cAAc;oBACd,OAAO,IAAI,MAAM;gBACnB;YACF,GAAG;YAEH,qCAAqC;YACrC,MAAM,kBAAkB,CAAC;gBACvB,IAAI,MAAM,MAAM,KAAK,OAAO,QAAQ,CAAC,MAAM,EAAE;gBAE7C,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,wBAAwB;oBAC9C,cAAc;oBACd,OAAO,mBAAmB,CAAC,WAAW;oBACtC,YAAY;oBACZ,QAAQ;wBACN,aAAa,SAAS,WAAW;wBACjC,oBAAoB,SAAS,kBAAkB;oBACjD;gBACF,OAAO,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,sBAAsB;oBACnD,cAAc;oBACd,OAAO,mBAAmB,CAAC,WAAW;oBACtC,YAAY;oBACZ,OAAO,IAAI,MAAM,MAAM,IAAI,CAAC,KAAK,IAAI;gBACvC;YACF;YAEA,OAAO,gBAAgB,CAAC,WAAW;QACrC;IACF;IAEA;;GAEC,GACD,MAAM,qBAAqB,cAAsB,EAI9C;QACD,MAAM,qBAAqB,eAAe,OAAO,CAAC;QAClD,IAAI,CAAC,oBAAoB;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,yCAAyC;QACzC,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC5D,MAAM,cAAc,UAAU,GAAG,CAAC;QAElC,IAAI,CAAC,aAAa;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,MAAM,IAAI,CAAC,qBAAqB,CAAC;YAC9C;YACA;YACA;QACF;QAEA,wBAAwB;QACxB,eAAe,UAAU,CAAC;QAE1B,OAAO;IACT;AACF;AAEO,MAAM,qBAAqB,IAAI;uCACvB", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/TwitterAuth.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { twitterAuthService, TwitterStatus } from '../lib/twitterAuthService';\nimport { authService } from '../lib/authService';\n\ninterface TwitterAuthProps {\n  onStatusChange?: (status: TwitterStatus) => void;\n}\n\nexport default function TwitterAuth({ onStatusChange }: TwitterAuthProps) {\n  const [twitterStatus, setTwitterStatus] = useState<TwitterStatus>({ connected: false });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  const loadTwitterStatus = useCallback(async () => {\n    try {\n      setError(null);\n      const status = await twitterAuthService.getTwitterStatus();\n      setTwitterStatus(status);\n      onStatusChange?.(status);\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      setError(`Failed to load Twitter status: ${errorMessage}`);\n    }\n  }, [onStatusChange]);\n\n  useEffect(() => {\n    setIsAuthenticated(authService.isAuthenticated());\n    if (authService.isAuthenticated()) {\n      loadTwitterStatus();\n    }\n  }, [loadTwitterStatus]);\n\n  const handleConnectTwitter = async () => {\n    if (!isAuthenticated) {\n      setError('Please log in first to connect your Twitter account');\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const authData = await twitterAuthService.initiateTwitterAuth();\n\n      // Store oauth_token_secret for the callback\n      sessionStorage.setItem('twitter_oauth_token_secret', authData.oauth_token_secret);\n      sessionStorage.setItem('twitter_oauth_token', authData.oauth_token);\n\n      // Redirect to Twitter authorization\n      window.location.href = authData.authorization_url;\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      setError(`Failed to connect Twitter: ${errorMessage}`);\n      setIsLoading(false);\n    }\n  };\n\n  const handleDisconnectTwitter = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      await twitterAuthService.disconnectTwitter();\n      const newStatus = { connected: false };\n      setTwitterStatus(newStatus);\n      onStatusChange?.(newStatus);\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      setError(`Failed to disconnect Twitter: ${errorMessage}`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n        <div className=\"flex items-center space-x-2\">\n          <svg className=\"w-5 h-5 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n          </svg>\n          <span className=\"text-yellow-800\">Please log in to connect your Twitter account</span>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-twitter-blue rounded-lg flex items-center justify-center\">\n            <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n            </svg>\n          </div>\n          <div>\n            <p className=\"font-medium text-gray-900\">Twitter</p>\n            {twitterStatus.connected ? (\n              <p className=\"text-sm text-gray-500\">@{twitterStatus.twitter_username}</p>\n            ) : (\n              <p className=\"text-sm text-gray-500\">Not connected</p>\n            )}\n          </div>\n        </div>\n\n        <div className=\"flex items-center space-x-3\">\n          {twitterStatus.connected ? (\n            <>\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                Connected\n              </span>\n              <button\n                onClick={handleDisconnectTwitter}\n                disabled={isLoading}\n                className=\"px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50 disabled:opacity-50\"\n              >\n                {isLoading ? 'Disconnecting...' : 'Disconnect'}\n              </button>\n            </>\n          ) : (\n            <>\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                Not connected\n              </span>\n              <button\n                onClick={handleConnectTwitter}\n                disabled={isLoading}\n                className=\"px-3 py-1 text-sm text-white bg-twitter-blue rounded hover:bg-blue-600 disabled:opacity-50\"\n              >\n                {isLoading ? 'Connecting...' : 'Connect'}\n              </button>\n            </>\n          )}\n        </div>\n      </div>\n\n      {error && (\n        <div className=\"p-3 bg-red-50 border border-red-200 rounded-lg\">\n          <div className=\"flex items-center space-x-2\">\n            <svg className=\"w-4 h-4 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span className=\"text-sm text-red-800\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {twitterStatus.connected && (\n        <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg\">\n          <div className=\"flex items-center space-x-2\">\n            <svg className=\"w-4 h-4 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n            </svg>\n            <span className=\"text-sm text-green-800\">\n              Twitter account connected successfully! You can now create and schedule tweets.\n            </span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,YAAY,EAAE,cAAc,EAAoB;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,WAAW;IAAM;IACrF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI;YACF,SAAS;YACT,MAAM,SAAS,MAAM,gIAAA,CAAA,qBAAkB,CAAC,gBAAgB;YACxD,iBAAiB;YACjB,iBAAiB;QACnB,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAC,+BAA+B,EAAE,cAAc;QAC3D;IACF,GAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB,yHAAA,CAAA,cAAW,CAAC,eAAe;QAC9C,IAAI,yHAAA,CAAA,cAAW,CAAC,eAAe,IAAI;YACjC;QACF;IACF,GAAG;QAAC;KAAkB;IAEtB,MAAM,uBAAuB;QAC3B,IAAI,CAAC,iBAAiB;YACpB,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,gIAAA,CAAA,qBAAkB,CAAC,mBAAmB;YAE7D,4CAA4C;YAC5C,eAAe,OAAO,CAAC,8BAA8B,SAAS,kBAAkB;YAChF,eAAe,OAAO,CAAC,uBAAuB,SAAS,WAAW;YAElE,oCAAoC;YACpC,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAS,iBAAiB;QACnD,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAC,2BAA2B,EAAE,cAAc;YACrD,aAAa;QACf;IACF;IAEA,MAAM,0BAA0B;QAC9B,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,gIAAA,CAAA,qBAAkB,CAAC,iBAAiB;YAC1C,MAAM,YAAY;gBAAE,WAAW;YAAM;YACrC,iBAAiB;YACjB,iBAAiB;QACnB,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,SAAS,CAAC,8BAA8B,EAAE,cAAc;QAC1D,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA0B,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;kCAEvE,8OAAC;wBAAK,WAAU;kCAAkB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAe,SAAQ;8CAC9D,cAAA,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAGZ,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;oCACxC,cAAc,SAAS,iBACtB,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAE,cAAc,gBAAgB;;;;;;6DAErE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;kCACZ,cAAc,SAAS,iBACtB;;8CACE,8OAAC;oCAAK,WAAU;8CAAsG;;;;;;8CAGtH,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,YAAY,qBAAqB;;;;;;;yDAItC;;8CACE,8OAAC;oCAAK,WAAU;8CAAoG;;;;;;8CAGpH,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;YAOxC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAuB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC9E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;YAK7C,cAAc,SAAS,kBACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAyB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAChF,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,8OAAC;4BAAK,WAAU;sCAAyB;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}]}