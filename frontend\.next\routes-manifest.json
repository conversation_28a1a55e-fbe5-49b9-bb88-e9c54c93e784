{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/analytics", "regex": "^/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/analytics(?:/)?$"}, {"page": "/auth/twitter/callback", "regex": "^/auth/twitter/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/twitter/callback(?:/)?$"}, {"page": "/auth/twitter/oauth2-callback", "regex": "^/auth/twitter/oauth2\\-callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/twitter/oauth2\\-callback(?:/)?$"}, {"page": "/content", "regex": "^/content(?:/)?$", "routeKeys": {}, "namedRegex": "^/content(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/debug-oauth", "regex": "^/debug\\-oauth(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-oauth(?:/)?$"}, {"page": "/debug-twitter", "regex": "^/debug\\-twitter(?:/)?$", "routeKeys": {}, "namedRegex": "^/debug\\-twitter(?:/)?$"}, {"page": "/icon.svg", "regex": "^/icon\\.svg(?:/)?$", "routeKeys": {}, "namedRegex": "^/icon\\.svg(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/test/twitter-oauth", "regex": "^/test/twitter\\-oauth(?:/)?$", "routeKeys": {}, "namedRegex": "^/test/twitter\\-oauth(?:/)?$"}, {"page": "/test-connection", "regex": "^/test\\-connection(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-connection(?:/)?$"}, {"page": "/test-simple", "regex": "^/test\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-simple(?:/)?$"}, {"page": "/test-twitter-oauth2", "regex": "^/test\\-twitter\\-oauth2(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-twitter\\-oauth2(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}