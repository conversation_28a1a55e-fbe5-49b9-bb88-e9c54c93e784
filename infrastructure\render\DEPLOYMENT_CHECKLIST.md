# AutoReach Deployment Checklist

## Pre-Deployment Setup

### 1. Render Account Setup
- [ ] Create account at [render.com](https://render.com)
- [ ] Connect GitHub repository
- [ ] Verify billing information (even for free tier)

### 2. API Keys and Credentials
- [ ] Twitter API credentials ready
  - [ ] Client ID
  - [ ] Client Secret  
  - [ ] Bearer <PERSON>ken
- [ ] OpenAI API key ready
- [ ] Generate secure SECRET_KEY (use: `openssl rand -hex 32`)

### 3. Repository Preparation
- [ ] Code pushed to GitHub
- [ ] All dependencies listed in requirements.txt/package.json
- [ ] Environment variables documented

## Deployment Steps

### Option A: One-Click Deploy (Recommended)
1. [ ] Go to Render dashboard
2. [ ] Click "New" → "Blueprint"
3. [ ] Connect your GitHub repository
4. [ ] Select `infrastructure/render/render.yaml`
5. [ ] Click "Apply" to deploy all services
6. [ ] Add environment variables (see below)

### Option B: Manual Deploy
Follow the detailed steps in `README.md`

## Environment Variables to Configure

### Backend Service
```
DATABASE_URL=<auto-generated from database service>
REDIS_URL=<auto-generated from redis service>
SECRET_KEY=<generate secure key>
DEBUG=false
FRONTEND_URL=<will be frontend service URL>
TWITTER_CLIENT_ID=<your twitter client id>
TWITTER_CLIENT_SECRET=<your twitter client secret>
TWITTER_BEARER_TOKEN=<your twitter bearer token>
TWITTER_OAUTH_REDIRECT_URI=<frontend-url>/auth/twitter/oauth2-callback
OPENAI_API_KEY=<your openai api key>
```

### Frontend Service
```
NEXT_PUBLIC_API_URL=<backend service URL>
```

## Post-Deployment Configuration

### 1. Update Service URLs
- [ ] Copy backend service URL
- [ ] Update frontend `NEXT_PUBLIC_API_URL` environment variable
- [ ] Copy frontend service URL
- [ ] Update backend `FRONTEND_URL` environment variable

### 2. Update Twitter OAuth Settings
- [ ] Go to [developer.twitter.com](https://developer.twitter.com)
- [ ] Update OAuth redirect URI to: `https://your-frontend-url.onrender.com/auth/twitter/oauth2-callback`

### 3. Test Deployment
- [ ] Backend health check: `https://your-backend-url.onrender.com/health`
- [ ] Frontend loads: `https://your-frontend-url.onrender.com`
- [ ] API docs accessible: `https://your-backend-url.onrender.com/docs`

## Testing Checklist

### Backend Testing
- [ ] Health endpoint responds: `/health`
- [ ] API docs accessible: `/docs`
- [ ] Database connection working
- [ ] Redis connection working
- [ ] CORS headers present

### Frontend Testing
- [ ] Site loads correctly
- [ ] API connection working (check browser console)
- [ ] Twitter OAuth flow working
- [ ] All pages accessible
- [ ] Mobile responsive

### Integration Testing
- [ ] User can initiate Twitter OAuth
- [ ] Authentication flow completes
- [ ] Content creation features work
- [ ] API calls succeed

## Troubleshooting

### Build Failures
- Check build logs in Render dashboard
- Verify all dependencies are in requirements.txt/package.json
- Ensure Python/Node versions are compatible

### Runtime Issues
- **Database connection errors**: Verify DATABASE_URL is correct
- **CORS errors**: Check ALLOWED_HOSTS includes frontend URL
- **OAuth errors**: Verify redirect URI matches exactly
- **API connection errors**: Check NEXT_PUBLIC_API_URL is correct

### Performance Issues
- Monitor service metrics in Render dashboard
- Consider upgrading to paid plans for better performance
- Check for memory/CPU usage spikes

## Service URLs (Fill in after deployment)

- **Frontend**: `https://autoreach-frontend.onrender.com`
- **Backend**: `https://autoreach-backend.onrender.com`
- **API Docs**: `https://autoreach-backend.onrender.com/docs`
- **Database**: `<internal render URL>`
- **Redis**: `<internal render URL>`

## Support Resources

- [Render Documentation](https://render.com/docs)
- [Render Community](https://community.render.com)
- [AutoReach GitHub Issues](https://github.com/your-username/autoreach/issues)

## Notes

- Free tier services spin down after 15 minutes of inactivity
- First request after spin-down may take 30+ seconds
- Consider upgrading to paid plans for production use
- Monitor usage to avoid hitting free tier limits
