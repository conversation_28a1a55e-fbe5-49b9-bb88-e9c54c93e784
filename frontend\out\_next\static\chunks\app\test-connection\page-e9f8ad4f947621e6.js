(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[894],{2703:(e,t,a)=>{"use strict";a.d(t,{I:()=>n});class r{getItem(e){try{return localStorage.getItem(e)}catch(e){return null}}setItem(e,t){try{localStorage.setItem(e,t)}catch(e){}}removeItem(e){try{localStorage.removeItem(e)}catch(e){}}clear(){try{localStorage.clear()}catch(e){}}}let n=new r},3851:(e,t,a)=>{"use strict";a.d(t,{y:()=>o});var r=a(4611),n=a(2703),s=a(4205);class i{async login(e){this.validateLoginRequest(e);let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let a=await this.apiClient.instance.post("/auth/token",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return n.I.setItem(r.d5.AUTH_TOKEN,a.data.access_token),a.data}async register(e){return this.validateRegisterRequest(e),this.apiClient.post("/users/",e)}async getCurrentUser(){return this.apiClient.get("/auth/me")}async logout(){n.I.removeItem(r.d5.AUTH_TOKEN),window.location.href="/login"}isAuthenticated(){return!!n.I.getItem(r.d5.AUTH_TOKEN)}getToken(){return n.I.getItem(r.d5.AUTH_TOKEN)}async initiateTwitterOAuth2(){return(await this.apiClient.instance.post("/auth/oauth2/twitter/init")).data}async handleTwitterOAuth2Callback(e){let t=await this.apiClient.instance.post("/auth/oauth2/twitter/callback",e);return n.I.setItem(r.d5.AUTH_TOKEN,t.data.access_token),t.data}validateLoginRequest(e){var t,a;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(a=e.password)?void 0:a.trim()))throw Error("Password is required")}validateRegisterRequest(e){var t,a,r;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(a=e.email)?void 0:a.trim()))throw Error("Email is required");if(!(null==(r=e.password)?void 0:r.trim()))throw Error("Password is required");if(e.password.length<6)throw Error("Password must be at least 6 characters");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email))throw Error("Please enter a valid email address")}constructor(e){this.apiClient=e}}let o=new i(s.uE)},4205:(e,t,a)=>{"use strict";a.d(t,{uE:()=>o});var r=a(3464),n=a(4611),s=a(2703);class i{createClient(){return r.A.create({baseURL:n.i3.BASE_URL,timeout:n.i3.TIMEOUT,headers:{"Content-Type":"application/json"}})}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.storage.getItem(n.d5.AUTH_TOKEN);return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(this.handleError(e))),this.client.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(this.handleError(e))})}handleUnauthorized(){this.storage.removeItem(n.d5.AUTH_TOKEN),window.location.href="/login"}handleError(e){if(!e.response)return Error(n.UU.NETWORK_ERROR);switch(e.response.status){case 401:return Error(n.UU.UNAUTHORIZED);case 400:return Error(n.UU.VALIDATION_ERROR);default:let t=e.response.data;return Error((null==t?void 0:t.message)||n.UU.GENERIC_ERROR)}}get instance(){return this.client}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(e=s.I){this.storage=e,this.client=this.createClient(),this.setupInterceptors()}}let o=new i},4611:(e,t,a)=>{"use strict";a.d(t,{ID:()=>n,Ij:()=>r,KA:()=>c,Ot:()=>o,RW:()=>m,UU:()=>d,WF:()=>h,d5:()=>u,gY:()=>l,gx:()=>E,i3:()=>i,m0:()=>s});let r=[{name:"Dashboard",path:"/dashboard"},{name:"Content",path:"/content"},{name:"Analytics",path:"/analytics"},{name:"Settings",path:"/settings"},{name:"Auth",path:"/auth"},{name:"Test API",path:"/test-connection"}],n=[{value:"engaging",label:"Engaging"},{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"educational",label:"Educational"},{value:"humorous",label:"Humorous"},{value:"informative",label:"Informative"},{value:"helpful",label:"Helpful"}],s=[{value:"short",label:"Short (1-2 sentences)"},{value:"medium",label:"Medium (3-5 sentences)"},{value:"long",label:"Long (6+ sentences)"}],i={BASE_URL:"http://localhost:8000/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},o={MAX_TWEET_LENGTH:280,MAX_THREAD_TWEETS:25,DEFAULT_THREAD_SIZE:3,MAX_HASHTAGS_RECOMMENDED:3,MAX_MENTIONS_RECOMMENDED:5},c={DEFAULT_STYLE:"engaging",DEFAULT_LANGUAGE:"en",SUPPORTED_STYLES:["engaging","professional","casual","educational","humorous","informative","helpful"],SUPPORTED_LANGUAGES:[{code:"en",name:"English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"}]},l={MIN_TOPIC_LENGTH:3,MAX_TOPIC_LENGTH:200,MIN_CONTENT_LENGTH:1,MAX_CONTENT_LENGTH:2e3,MIN_USERNAME_LENGTH:3,MAX_USERNAME_LENGTH:50,MIN_PASSWORD_LENGTH:8,MAX_PASSWORD_LENGTH:128},u={AUTH_TOKEN:"authToken",USER_PREFERENCES:"userPreferences",DRAFT_CONTENT:"draftContent",THEME:"theme"},d={NETWORK_ERROR:"Network error. Please check your connection.",UNAUTHORIZED:"Your session has expired. Please log in again.",FORBIDDEN:"Access denied.",NOT_FOUND:"The requested resource was not found.",VALIDATION_ERROR:"Please check your input and try again.",GENERATION_FAILED:"Content generation failed. Please try again.",RATE_LIMIT_EXCEEDED:"Rate limit exceeded. Please try again later.",GENERIC_ERROR:"Something went wrong. Please try again."},E={OK:200,CREATED:201,NO_CONTENT:204,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,CONFLICT:409,UNPROCESSABLE_ENTITY:422,TOO_MANY_REQUESTS:429,INTERNAL_SERVER_ERROR:500,SERVICE_UNAVAILABLE:503},h={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,USERNAME:/^[a-zA-Z0-9_]+$/,PASSWORD:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,HASHTAG:/#\w+/g,MENTION:/@\w+/g,URL:/https?:\/\/[^\s]+/g},m=e=>{switch(e){case E.UNAUTHORIZED:return d.UNAUTHORIZED;case E.FORBIDDEN:return d.FORBIDDEN;case E.NOT_FOUND:return d.NOT_FOUND;case E.UNPROCESSABLE_ENTITY:return d.VALIDATION_ERROR;case E.TOO_MANY_REQUESTS:return d.RATE_LIMIT_EXCEEDED;case E.INTERNAL_SERVER_ERROR:case E.SERVICE_UNAVAILABLE:return d.GENERATION_FAILED;default:return d.GENERIC_ERROR}}},5367:(e,t,a)=>{Promise.resolve().then(a.bind(a,8715)),Promise.resolve().then(a.t.bind(a,6874,23))},8715:(e,t,a)=>{"use strict";a.d(t,{default:()=>c});var r=a(5155),n=a(2115),s=a(4205),i=a(3851),o=a(7926);function c(){let[e,t]=(0,n.useState)([{name:"Backend Health Check",status:"pending"},{name:"API Configuration",status:"pending"},{name:"Authentication Test",status:"pending"},{name:"Content Service Test",status:"pending"}]),[a,c]=(0,n.useState)(!1),[l,u]=(0,n.useState)(null),d=(e,a,r)=>{t(t=>t.map((t,n)=>n===e?{...t,status:a,message:r}:t))},E=async()=>{c(!0),t(e=>e.map(e=>({...e,status:"pending"})));try{try{let e=await s.uE.get("/health");e&&"healthy"===e.status?d(0,"success","Backend is healthy (API: ".concat(e.api||"ready",")")):d(0,"error","Backend health check failed")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";d(0,"error","Backend not accessible: ".concat(e))}try{let e=s.uE.instance.defaults.baseURL;(null==e?void 0:e.includes("8000"))?d(1,"success","API URL: ".concat(e)):d(1,"error","Incorrect API URL: ".concat(e))}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";d(1,"error","API configuration error: ".concat(e))}try{let e={username:"test_".concat(Date.now()),email:"test_".concat(Date.now(),"@example.com"),password:"testpass123",full_name:"Test User"};try{await i.y.register(e)}catch(e){if(!(e instanceof Error?e.message:"Unknown error occurred").includes("already registered"))throw e}let t=await i.y.login({username:e.username,password:e.password});t.access_token?(u(t.access_token),d(2,"success","Authentication successful")):d(2,"error","Login failed")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";d(2,"error","Authentication failed: ".concat(e))}try{if(i.y.isAuthenticated()){let e=await o.l.getContentHistory(0,5);d(3,"success","Content history retrieved (".concat(e.total||0," items)"))}else d(3,"error","Not authenticated for content test")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";d(3,"error","Content service failed: ".concat(e))}}catch(e){console.error("Test suite error:",e)}finally{c(!1)}},h=async()=>{if(!i.y.isAuthenticated())return void alert("Please run authentication test first");try{let e=await o.l.generateTweet({topic:"Testing AutoReach connection",style:"professional",language:"en"});e.success&&e.content?alert("Content generated successfully:\n\n".concat(e.content)):alert("Content generation failed: ".concat(e.error||"Unknown error"))}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";alert("Content generation error: ".concat(e))}},m=e=>{switch(e){case"pending":return"⏳";case"success":return"✅";case"error":return"❌"}},g=e=>{switch(e){case"pending":return"text-yellow-600";case"success":return"text-green-600";case"error":return"text-red-600"}};return(0,r.jsxs)("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6 text-gray-800",children:"Frontend-Backend Connection Test"}),(0,r.jsx)("div",{className:"space-y-4 mb-6",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 border rounded-lg",children:[(0,r.jsx)("span",{className:"text-2xl",children:m(e.status)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium ".concat(g(e.status)),children:e.name}),e.message&&(0,r.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:e.message})]})]},t))}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{onClick:E,disabled:a,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:a?"Running Tests...":"Run Connection Tests"}),(0,r.jsx)("button",{onClick:h,disabled:a||!i.y.isAuthenticated(),className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Test Content Generation"})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-800 mb-2",children:"Connection Status"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsxs)("div",{children:["Backend URL: ","http://localhost:8000/api"]}),(0,r.jsxs)("div",{children:["Authentication: ",i.y.isAuthenticated()?"✅ Authenticated":"❌ Not authenticated"]}),l&&(0,r.jsxs)("div",{className:"break-all",children:["Token: ",l.substring(0,20),"..."]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:[(0,r.jsx)("p",{children:"This component tests the connection between the Next.js frontend and FastAPI backend."}),(0,r.jsx)("p",{children:"Make sure the backend is running on port 8000 before testing."})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,926,441,684,358],()=>t(5367)),_N_E=e.O()}]);