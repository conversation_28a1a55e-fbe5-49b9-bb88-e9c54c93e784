(()=>{var e={};e.id=35,e.ids=[35],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52343:(e,t,s)=>{Promise.resolve().then(s.bind(s,88310))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57919:(e,t,s)=>{Promise.resolve().then(s.bind(s,62148))},62148:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\test-twitter-oauth2\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-twitter-oauth2\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72976:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(60687),a=s(43210),i=s(74265);function n({onError:e,className:t=""}){let[s,n]=(0,a.useState)(!1),[o,l]=(0,a.useState)(null),d=async()=>{n(!0),l(null);try{console.log("Starting Twitter OAuth...");let e=await i.y.initiateTwitterOAuth2();console.log("Auth data received:",{hasAuthUrl:!!e.authorization_url,state:e.state}),sessionStorage.setItem("twitter_oauth2_state",e.state),sessionStorage.setItem("twitter_oauth2_code_verifier",e.code_verifier),window.location.href=e.authorization_url}catch(s){let t=s instanceof Error?`Failed to start Twitter login: ${s.message}`:"Failed to start Twitter login: Unknown error occurred";l(t),e?.(t),n(!1)}};return(0,r.jsxs)("div",{className:`space-y-4 ${t}`,children:[o&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:(0,r.jsx)("p",{className:"text-sm",children:o})}),(0,r.jsx)("button",{onClick:d,disabled:s,className:`
          w-full flex items-center justify-center px-4 py-3 border border-transparent
          text-base font-medium rounded-md text-white bg-blue-500 hover:bg-blue-600
          focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
          disabled:opacity-50 disabled:cursor-not-allowed
          transition-colors duration-200
        `,children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Connecting to Twitter..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})}),"Continue with Twitter"]})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"By continuing, you agree to our Terms of Service and Privacy Policy"})})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82840:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["test-twitter-oauth2",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,62148)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-twitter-oauth2\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-twitter-oauth2\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-twitter-oauth2/page",pathname:"/test-twitter-oauth2",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83997:e=>{"use strict";e.exports=require("tty")},86364:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},88310:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(60687),a=s(43210),i=s(74265),n=s(72976);function o(){let[e,t]=(0,a.useState)(null),[s,o]=(0,a.useState)(null),[l,d]=(0,a.useState)(!1),c=async()=>{d(!0),o(null),t(null);try{let e=await i.y.initiateTwitterOAuth2();t(e)}catch(e){o(e instanceof Error?e.message:"An unknown error occurred")}finally{d(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Twitter OAuth 2.0 Test"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Test the Twitter OAuth 2.0 implementation for user authentication"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Manual API Test"}),(0,r.jsx)("button",{onClick:c,disabled:l,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50 mb-4",children:l?"Testing...":"Test OAuth 2.0 Init"}),s&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4",children:[(0,r.jsx)("h3",{className:"font-medium",children:"Error:"}),(0,r.jsx)("p",{className:"text-sm",children:s})]}),e&&(0,r.jsxs)("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium mb-2",children:"Success:"}),(0,r.jsx)("pre",{className:"text-xs overflow-auto bg-white p-2 rounded border",children:JSON.stringify(e,null,2)})]})]}),(0,r.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Component Test"}),(0,r.jsx)(n.A,{onError:e=>{o(e)}}),e&&"success"in e&&e.success&&(0,r.jsxs)("div",{className:"mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md",children:[(0,r.jsx)("h3",{className:"font-medium mb-2",children:"Login Success:"}),(0,r.jsx)("pre",{className:"text-xs overflow-auto bg-white p-2 rounded border",children:JSON.stringify(e.user,null,2)})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white shadow-md rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"API Endpoints"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"POST /api/auth/oauth2/twitter/init"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Initialize Twitter OAuth 2.0 flow"}),(0,r.jsx)("code",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:'{ "redirect_uri": "http://localhost:3000/auth/twitter/oauth2-callback" }'})]}),(0,r.jsxs)("div",{className:"border-l-4 border-green-500 pl-4",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:"POST /api/auth/oauth2/twitter/callback"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Handle Twitter OAuth 2.0 callback"}),(0,r.jsx)("code",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:'{ "code": "...", "state": "...", "code_verifier": "..." }'})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white shadow-md rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Environment Check"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Backend URL:"}),(0,r.jsx)("span",{className:"font-mono",children:"http://127.0.0.1:8000"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Frontend URL:"}),(0,r.jsx)("span",{className:"font-mono",children:"http://localhost:3000"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"OAuth Callback:"}),(0,r.jsx)("span",{className:"font-mono",children:"http://localhost:3000/auth/twitter/oauth2-callback"})]})]})]})]})})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,786,658,70],()=>s(82840));module.exports=r})();