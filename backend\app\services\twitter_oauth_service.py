"""
Twitter OAuth 2.0 service for user authentication.
Handles the complete OAuth flow including PKCE, token exchange, and user management.
"""

import secrets
import hashlib
import base64
import urllib.parse
from typing import Dict, Any, Optional, Tuple
from datetime import timedelta
import httpx
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
import logging

from app.core.config import settings
from app.models.user import User
from app.core.auth_utils import create_access_token

logger = logging.getLogger(__name__)


class TwitterOAuthService:
    """Service for handling Twitter OAuth 2.0 authentication flow."""
    
    TWITTER_OAUTH_URL = "https://twitter.com/i/oauth2/authorize"
    TWITTER_TOKEN_URL = "https://api.twitter.com/2/oauth2/token"
    TWITTER_USER_URL = "https://api.twitter.com/2/users/me"
    OAUTH_SCOPE = "tweet.read users.read offline.access"
    
    @staticmethod
    def generate_code_verifier() -> str:
        """Generate a code verifier for PKCE (RFC 7636)."""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    
    @staticmethod
    def generate_code_challenge(code_verifier: str) -> str:
        """Generate a code challenge from code verifier using SHA256."""
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')
    
    @classmethod
    def init_oauth_flow(cls) -> Dict[str, str]:
        """
        Initialize Twitter OAuth 2.0 flow with PKCE.

        Returns:
            Dict containing authorization_url, state, and code_verifier

        Raises:
            HTTPException: If OAuth initialization fails
        """
        try:
            # Validate required configuration
            if not settings.TWITTER_CLIENT_ID:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Twitter OAuth configuration is missing: TWITTER_CLIENT_ID"
                )

            if not settings.TWITTER_OAUTH_REDIRECT_URI:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Twitter OAuth configuration is missing: TWITTER_OAUTH_REDIRECT_URI"
                )

            # Generate PKCE parameters
            code_verifier = cls.generate_code_verifier()
            code_challenge = cls.generate_code_challenge(code_verifier)
            state = secrets.token_urlsafe(32)

            # Build authorization URL
            params = {
                'response_type': 'code',
                'client_id': settings.TWITTER_CLIENT_ID,
                'redirect_uri': settings.TWITTER_OAUTH_REDIRECT_URI,
                'scope': cls.OAUTH_SCOPE,
                'state': state,
                'code_challenge': code_challenge,
                'code_challenge_method': 'S256'
            }

            authorization_url = f"{cls.TWITTER_OAUTH_URL}?{urllib.parse.urlencode(params)}"

            logger.info(f"OAuth flow initialized for redirect URI: {settings.TWITTER_OAUTH_REDIRECT_URI}")

            return {
                "authorization_url": authorization_url,
                "state": state,
                "code_verifier": code_verifier
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to initialize OAuth flow: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to initialize Twitter OAuth: {str(e)}"
            )
    
    @classmethod
    async def exchange_code_for_token(cls, code: str, code_verifier: str) -> Dict[str, Any]:
        """
        Exchange authorization code for access token.
        
        Args:
            code: Authorization code from Twitter
            code_verifier: PKCE code verifier
            
        Returns:
            Token information from Twitter API
            
        Raises:
            HTTPException: If token exchange fails
        """
        token_data = {
            'grant_type': 'authorization_code',
            'client_id': settings.TWITTER_CLIENT_ID,
            'client_secret': settings.TWITTER_CLIENT_SECRET,
            'code': code,
            'redirect_uri': settings.TWITTER_OAUTH_REDIRECT_URI,
            'code_verifier': code_verifier
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    cls.TWITTER_TOKEN_URL,
                    data=token_data,
                    auth=(settings.TWITTER_CLIENT_ID, settings.TWITTER_CLIENT_SECRET),
                    headers={'Content-Type': 'application/x-www-form-urlencoded'}
                )
                
                if response.status_code != 200:
                    logger.error(f"Token exchange failed: {response.text}")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to get access token: {response.text}"
                    )
                
                token_info = response.json()
                logger.info("Successfully exchanged authorization code for access token")
                return token_info
                
        except httpx.RequestError as e:
            logger.error(f"Network error during token exchange: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Unable to connect to Twitter API"
            )
    
    @classmethod
    async def get_twitter_user_info(cls, access_token: str) -> Dict[str, Any]:
        """
        Get user information from Twitter API.
        
        Args:
            access_token: Twitter access token
            
        Returns:
            User information from Twitter API
            
        Raises:
            HTTPException: If user info retrieval fails
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    cls.TWITTER_USER_URL,
                    headers={'Authorization': f'Bearer {access_token}'}
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to get user info: {response.text}")
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Failed to get user info: {response.text}"
                    )
                
                user_data = response.json()['data']
                logger.info(f"Retrieved user info for Twitter ID: {user_data['id']}")
                return user_data
                
        except httpx.RequestError as e:
            logger.error(f"Network error during user info retrieval: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Unable to connect to Twitter API"
            )
    
    @classmethod
    def create_or_update_user(
        cls, 
        db: Session, 
        twitter_user: Dict[str, Any], 
        access_token: str, 
        refresh_token: Optional[str] = None
    ) -> User:
        """
        Create a new user or update existing user with Twitter OAuth data.
        
        Args:
            db: Database session
            twitter_user: Twitter user data
            access_token: Twitter access token
            refresh_token: Twitter refresh token (optional)
            
        Returns:
            User object (created or updated)
            
        Raises:
            HTTPException: If user creation/update fails
        """
        try:
            # Check if user exists by twitter_user_id
            existing_user = db.query(User).filter(
                User.twitter_user_id == str(twitter_user['id'])
            ).first()
            
            if existing_user:
                # Update existing user's tokens
                existing_user.twitter_access_token = access_token
                existing_user.twitter_username = twitter_user['username']
                if refresh_token:
                    existing_user.twitter_refresh_token = refresh_token
                
                db.commit()
                logger.info(f"Updated existing user: {existing_user.id}")
                return existing_user
            else:
                # Create new user
                username = cls._generate_unique_username(db, twitter_user['username'], twitter_user['id'])
                
                new_user = User(
                    twitter_user_id=str(twitter_user['id']),
                    twitter_username=twitter_user['username'],
                    twitter_access_token=access_token,
                    twitter_refresh_token=refresh_token,
                    username=username,
                    full_name=twitter_user.get('name', twitter_user['username']),
                    is_active=True
                )
                
                db.add(new_user)
                db.commit()
                db.refresh(new_user)
                logger.info(f"Created new user: {new_user.id}")
                return new_user
                
        except Exception as e:
            db.rollback()
            logger.error(f"Failed to create/update user: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create or update user account"
            )
    
    @classmethod
    def _generate_unique_username(cls, db: Session, preferred_username: str, twitter_id: str) -> str:
        """Generate a unique username, appending Twitter ID if needed."""
        # Check if preferred username is available
        existing_user = db.query(User).filter(User.username == preferred_username).first()
        
        if not existing_user:
            return preferred_username
        
        # If username exists, append twitter ID to make it unique
        unique_username = f"{preferred_username}_{twitter_id}"
        logger.info(f"Username {preferred_username} exists, using {unique_username}")
        return unique_username
    
    @classmethod
    def create_jwt_token(cls, user: User) -> str:
        """Create JWT token for authenticated user."""
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        return create_access_token(
            data={"sub": user.username},
            expires_delta=access_token_expires
        )
    
    @classmethod
    async def handle_oauth_callback(
        cls, 
        db: Session, 
        code: str, 
        state: str, 
        code_verifier: str
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Handle complete OAuth callback flow.
        
        Args:
            db: Database session
            code: Authorization code from Twitter
            state: OAuth state parameter
            code_verifier: PKCE code verifier
            
        Returns:
            Tuple of (jwt_token, user_data)
            
        Raises:
            HTTPException: If OAuth callback handling fails
        """
        # Exchange code for token
        token_info = await cls.exchange_code_for_token(code, code_verifier)
        access_token = token_info['access_token']
        refresh_token = token_info.get('refresh_token')
        
        # Get user info from Twitter
        twitter_user = await cls.get_twitter_user_info(access_token)
        
        # Create or update user
        user = cls.create_or_update_user(db, twitter_user, access_token, refresh_token)
        
        # Create JWT token
        jwt_token = cls.create_jwt_token(user)
        
        user_data = {
            "id": user.id,
            "username": user.username,
            "twitter_username": user.twitter_username,
            "twitter_user_id": user.twitter_user_id,
            "full_name": user.full_name,
            "is_active": user.is_active
        }
        
        logger.info(f"OAuth callback completed successfully for user: {user.id}")
        return jwt_token, user_data
