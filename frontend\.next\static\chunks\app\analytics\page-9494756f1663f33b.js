(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[745],{108:(e,t,s)=>{Promise.resolve().then(s.bind(s,7864))},1125:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(5155),n=s(6874),a=s.n(n);let l=e=>{let{href:t="/dashboard",size:s="md",showText:n=!0}=e,l={sm:{icon:"w-6 h-6",text:"text-lg"},md:{icon:"w-8 h-8",text:"text-xl"},lg:{icon:"w-10 h-10",text:"text-2xl"}},i=(0,r.jsxs)("div",{className:"flex items-center space-x-2 select-none",children:[(0,r.jsx)("div",{className:"".concat(l[s].icon," bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0"),children:(0,r.jsx)("span",{className:"text-white font-bold ".concat("sm"===s?"text-sm":"text-lg"),children:"A"})}),n&&(0,r.jsx)("span",{className:"".concat(l[s].text," font-bold text-gray-900 whitespace-nowrap"),children:"AutoReach"})]});return t?(0,r.jsx)(a(),{href:t,children:i}):i}},7864:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var r=s(5155),n=s(6874),a=s.n(n),l=s(5695),i=s(2115),o=s(4611),d=s(1125),c=s(8794);let m=e=>{var t,s;let{showNavigation:n=!0}=e,m=(0,l.usePathname)(),[x,h]=(0,i.useState)(!1),[u,g]=(0,i.useState)(!1),{user:p,logout:b,isAuthenticated:j}=(0,c.A)();return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 relative z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(d.A,{})}),n&&(0,r.jsx)("nav",{className:"hidden md:flex space-x-6",children:o.Ij.map(e=>(0,r.jsx)(a(),{href:e.path,className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(m===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"," ").concat("Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100":""),children:e.name},e.path))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[n&&(0,r.jsx)("button",{onClick:()=>h(!x),className:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle mobile menu",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:x?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),j?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>g(!u),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-purple-600",children:(null==p||null==(t=p.full_name)?void 0:t[0])||(null==p||null==(s=p.username)?void 0:s[0])||"U"})}),(0,r.jsx)("span",{className:"hidden sm:block text-sm font-medium",children:(null==p?void 0:p.full_name)||(null==p?void 0:p.username)}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),u&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,r.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 border-b border-gray-100",children:[(0,r.jsx)("p",{className:"font-medium",children:(null==p?void 0:p.full_name)||(null==p?void 0:p.username)}),(null==p?void 0:p.twitter_username)&&(0,r.jsxs)("p",{className:"text-gray-500",children:["@",p.twitter_username]})]}),(0,r.jsx)(a(),{href:"/settings",onClick:()=>g(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Settings"}),(0,r.jsx)("button",{onClick:()=>{g(!1),b()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})]}):(0,r.jsx)(a(),{href:"/login",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})]})]}),n&&x&&(0,r.jsx)("div",{className:"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,r.jsx)("nav",{className:"px-4 py-4 space-y-2",children:o.Ij.map(e=>(0,r.jsx)(a(),{href:e.path,onClick:()=>h(!1),className:"block px-4 py-3 rounded-lg text-base font-medium transition-colors ".concat(m===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"," ").concat("Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100 border border-green-200":""),children:(0,r.jsxs)("span",{className:"flex items-center gap-3",children:[(0,r.jsxs)("span",{className:"text-lg",children:["Dashboard"===e.name&&"\uD83D\uDCCA","Content"===e.name&&"✍️","Analytics"===e.name&&"\uD83D\uDCC8","Settings"===e.name&&"⚙️","Auth"===e.name&&"\uD83D\uDD10","Test API"===e.name&&"\uD83E\uDDEA"]}),e.name]})},e.path))})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,794,441,684,358],()=>t(108)),_N_E=e.O()}]);