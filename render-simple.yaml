# Simplified Render Blueprint for Reachly Platform
# This version addresses Render's current requirements and limitations

databases:
  - name: reachly-db
    databaseName: reachly
    user: reachly_user
    plan: free

services:
  # Backend API Service
  - type: web
    name: reachly-backend
    runtime: python
    plan: free
    rootDir: backend
    buildCommand: pip install -r requirements.txt
    startCommand: python start_server.py
    healthCheckPath: /health
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: reachly-db
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: "false"
      # Add these manually in Render dashboard after deployment
      - key: TWITTER_CLIENT_ID
        sync: false
      - key: TWITTER_CLIENT_SECRET
        sync: false
      - key: TWITTER_BEARER_TOKEN
        sync: false
      - key: TWITTER_API_KEY
        sync: false
      - key: TWITTER_API_SECRET
        sync: false
      - key: TWITTER_ACCESS_TOKEN
        sync: false
      - key: TWITTER_ACCESS_TOKEN_SECRET
        sync: false
      - key: OPENAI_API_KEY
        sync: false

  # Frontend Service
  - type: web
    name: reachly-frontend
    runtime: node
    plan: free
    rootDir: frontend
    buildCommand: npm ci && npm run build
    startCommand: npm run start
    envVars:
      - key: NEXT_PUBLIC_API_URL
        fromService:
          type: web
          name: reachly-backend
          property: host

# Note: Redis removed from blueprint due to IP allow list requirements
# You can add Redis manually in the Render dashboard if needed
# For development, the app can work without Redis caching
