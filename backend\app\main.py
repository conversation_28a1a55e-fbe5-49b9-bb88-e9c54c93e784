from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os

# Load environment variables FIRST
load_dotenv()

# Debug print
print(f"=== STARTUP DEBUG ===")
print(f"TWITTER_CLIENT_ID: {os.getenv('TWITTER_CLIENT_ID', 'NOT SET')[:10]}...")
print(f"Working directory: {os.getcwd()}")

from app.core.config import settings
from app.api import auth, content

app = FastAPI(title="AutoReach API")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[settings.FRONTEND_URL],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers with /api prefix to match frontend expectations
app.include_router(auth.router, prefix="/api/auth")
app.include_router(content.router, prefix="/api/content")

@app.get("/")
async def root():
    return {"message": "AutoReach API is running"}

@app.get("/health")
async def health_check():
    return {"status": "ok"}


