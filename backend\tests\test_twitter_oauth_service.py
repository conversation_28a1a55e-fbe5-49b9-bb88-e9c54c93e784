"""
Tests for Twitter OAuth 2.0 service.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session

from app.services.twitter_oauth_service import TwitterOAuthService
from app.models.user import User


class TestTwitterOAuthService:
    """Test cases for TwitterOAuthService."""
    
    def test_generate_code_verifier(self):
        """Test PKCE code verifier generation."""
        verifier = TwitterOAuthService.generate_code_verifier()
        
        assert isinstance(verifier, str)
        assert len(verifier) >= 43  # Base64 encoded 32 bytes should be at least 43 chars
        assert len(verifier) <= 128  # RFC 7636 maximum
        # Should not contain padding characters
        assert not verifier.endswith('=')
    
    def test_generate_code_challenge(self):
        """Test PKCE code challenge generation."""
        verifier = "test_code_verifier_123"
        challenge = TwitterOAuthService.generate_code_challenge(verifier)
        
        assert isinstance(challenge, str)
        assert len(challenge) == 43  # SHA256 base64 encoded without padding
        assert not challenge.endswith('=')
        
        # Same verifier should always produce same challenge
        challenge2 = TwitterOAuthService.generate_code_challenge(verifier)
        assert challenge == challenge2
    
    @patch('app.services.twitter_oauth_service.settings')
    def test_init_oauth_flow_success(self, mock_settings):
        """Test successful OAuth flow initialization."""
        mock_settings.TWITTER_CLIENT_ID = "test_client_id"
        mock_settings.TWITTER_OAUTH_REDIRECT_URI = "http://localhost:3000/callback"
        
        result = TwitterOAuthService.init_oauth_flow()
        
        assert "authorization_url" in result
        assert "state" in result
        assert "code_verifier" in result
        
        # Check authorization URL contains required parameters
        auth_url = result["authorization_url"]
        assert "twitter.com/i/oauth2/authorize" in auth_url
        assert "client_id=test_client_id" in auth_url
        assert "response_type=code" in auth_url
        assert "code_challenge_method=S256" in auth_url
        assert f"state={result['state']}" in auth_url
    
    @patch('app.services.twitter_oauth_service.settings')
    def test_init_oauth_flow_missing_config(self, mock_settings):
        """Test OAuth flow initialization with missing configuration."""
        mock_settings.TWITTER_CLIENT_ID = ""
        
        with pytest.raises(HTTPException) as exc_info:
            TwitterOAuthService.init_oauth_flow()
        
        assert exc_info.value.status_code == 500
    
    @pytest.mark.asyncio
    @patch('app.services.twitter_oauth_service.httpx.AsyncClient')
    @patch('app.services.twitter_oauth_service.settings')
    async def test_exchange_code_for_token_success(self, mock_settings, mock_client):
        """Test successful token exchange."""
        mock_settings.TWITTER_CLIENT_ID = "test_client_id"
        mock_settings.TWITTER_CLIENT_SECRET = "test_client_secret"
        mock_settings.TWITTER_OAUTH_REDIRECT_URI = "http://localhost:3000/callback"
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test_access_token",
            "token_type": "bearer",
            "expires_in": 7200
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        result = await TwitterOAuthService.exchange_code_for_token("test_code", "test_verifier")
        
        assert result["access_token"] == "test_access_token"
        assert result["token_type"] == "bearer"
        
        # Verify the request was made correctly
        mock_client_instance.post.assert_called_once()
        call_args = mock_client_instance.post.call_args
        assert call_args[0][0] == TwitterOAuthService.TWITTER_TOKEN_URL
    
    @pytest.mark.asyncio
    @patch('app.services.twitter_oauth_service.httpx.AsyncClient')
    async def test_exchange_code_for_token_failure(self, mock_client):
        """Test token exchange failure."""
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = '{"error": "invalid_grant"}'
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        with pytest.raises(HTTPException) as exc_info:
            await TwitterOAuthService.exchange_code_for_token("invalid_code", "test_verifier")
        
        assert exc_info.value.status_code == 400
        assert "invalid_grant" in str(exc_info.value.detail)
    
    @pytest.mark.asyncio
    @patch('app.services.twitter_oauth_service.httpx.AsyncClient')
    async def test_get_twitter_user_info_success(self, mock_client):
        """Test successful user info retrieval."""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": {
                "id": "123456789",
                "name": "Test User",
                "username": "testuser"
            }
        }
        
        mock_client_instance = AsyncMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        result = await TwitterOAuthService.get_twitter_user_info("test_access_token")
        
        assert result["id"] == "123456789"
        assert result["name"] == "Test User"
        assert result["username"] == "testuser"
        
        # Verify the request was made correctly
        mock_client_instance.get.assert_called_once_with(
            TwitterOAuthService.TWITTER_USER_URL,
            headers={'Authorization': 'Bearer test_access_token'}
        )
    
    @pytest.mark.asyncio
    @patch('app.services.twitter_oauth_service.httpx.AsyncClient')
    async def test_get_twitter_user_info_failure(self, mock_client):
        """Test user info retrieval failure."""
        # Mock failed response
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.text = '{"error": "unauthorized"}'
        
        mock_client_instance = AsyncMock()
        mock_client_instance.get.return_value = mock_response
        mock_client.return_value.__aenter__.return_value = mock_client_instance
        
        with pytest.raises(HTTPException) as exc_info:
            await TwitterOAuthService.get_twitter_user_info("invalid_token")
        
        assert exc_info.value.status_code == 400
        assert "unauthorized" in str(exc_info.value.detail)
    
    def test_create_or_update_user_new_user(self):
        """Test creating a new user."""
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        twitter_user = {
            "id": "123456789",
            "name": "Test User",
            "username": "testuser"
        }
        
        result = TwitterOAuthService.create_or_update_user(
            db=mock_db,
            twitter_user=twitter_user,
            access_token="test_token",
            refresh_token="test_refresh_token"
        )
        
        # Verify user was added to database
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.refresh.assert_called_once()
        
        # Check the user object that was added
        added_user = mock_db.add.call_args[0][0]
        assert added_user.twitter_user_id == "123456789"
        assert added_user.twitter_username == "testuser"
        assert added_user.twitter_access_token == "test_token"
        assert added_user.twitter_refresh_token == "test_refresh_token"
        assert added_user.username == "testuser"
        assert added_user.full_name == "Test User"
        assert added_user.is_active is True
    
    def test_create_or_update_user_existing_user(self):
        """Test updating an existing user."""
        mock_db = Mock(spec=Session)
        
        # Mock existing user
        existing_user = Mock(spec=User)
        existing_user.id = 1
        existing_user.twitter_access_token = "old_token"
        existing_user.twitter_username = "oldusername"
        existing_user.twitter_refresh_token = "old_refresh"
        
        mock_db.query.return_value.filter.return_value.first.return_value = existing_user
        
        twitter_user = {
            "id": "123456789",
            "name": "Updated User",
            "username": "updateduser"
        }
        
        result = TwitterOAuthService.create_or_update_user(
            db=mock_db,
            twitter_user=twitter_user,
            access_token="new_token",
            refresh_token="new_refresh_token"
        )
        
        # Verify user was updated
        assert existing_user.twitter_access_token == "new_token"
        assert existing_user.twitter_username == "updateduser"
        assert existing_user.twitter_refresh_token == "new_refresh_token"
        
        mock_db.commit.assert_called_once()
        assert result == existing_user
    
    def test_generate_unique_username_available(self):
        """Test username generation when preferred username is available."""
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        username = TwitterOAuthService._generate_unique_username(
            db=mock_db,
            preferred_username="testuser",
            twitter_id="123456789"
        )
        
        assert username == "testuser"
    
    def test_generate_unique_username_taken(self):
        """Test username generation when preferred username is taken."""
        mock_db = Mock(spec=Session)
        mock_db.query.return_value.filter.return_value.first.return_value = Mock()  # User exists
        
        username = TwitterOAuthService._generate_unique_username(
            db=mock_db,
            preferred_username="testuser",
            twitter_id="123456789"
        )
        
        assert username == "testuser_123456789"
    
    @patch('app.services.twitter_oauth_service.create_access_token')
    def test_create_jwt_token(self, mock_create_token):
        """Test JWT token creation."""
        mock_create_token.return_value = "test_jwt_token"
        
        mock_user = Mock(spec=User)
        mock_user.username = "testuser"
        
        result = TwitterOAuthService.create_jwt_token(mock_user)
        
        assert result == "test_jwt_token"
        mock_create_token.assert_called_once()
        
        # Check the call arguments
        call_args = mock_create_token.call_args
        assert call_args[1]["data"]["sub"] == "testuser"
        assert "expires_delta" in call_args[1]
    
    @pytest.mark.asyncio
    @patch.object(TwitterOAuthService, 'exchange_code_for_token')
    @patch.object(TwitterOAuthService, 'get_twitter_user_info')
    @patch.object(TwitterOAuthService, 'create_or_update_user')
    @patch.object(TwitterOAuthService, 'create_jwt_token')
    async def test_handle_oauth_callback_success(
        self, 
        mock_create_jwt, 
        mock_create_user, 
        mock_get_user_info, 
        mock_exchange_token
    ):
        """Test complete OAuth callback handling."""
        # Setup mocks
        mock_exchange_token.return_value = {
            "access_token": "test_token",
            "refresh_token": "test_refresh"
        }
        
        mock_get_user_info.return_value = {
            "id": "123456789",
            "name": "Test User",
            "username": "testuser"
        }
        
        mock_user = Mock(spec=User)
        mock_user.id = 1
        mock_user.username = "testuser"
        mock_user.twitter_username = "testuser"
        mock_user.twitter_user_id = "123456789"
        mock_user.full_name = "Test User"
        mock_user.is_active = True
        mock_create_user.return_value = mock_user
        
        mock_create_jwt.return_value = "test_jwt_token"
        
        mock_db = Mock(spec=Session)
        
        # Execute
        jwt_token, user_data = await TwitterOAuthService.handle_oauth_callback(
            db=mock_db,
            code="test_code",
            state="test_state",
            code_verifier="test_verifier"
        )
        
        # Verify
        assert jwt_token == "test_jwt_token"
        assert user_data["id"] == 1
        assert user_data["username"] == "testuser"
        assert user_data["twitter_username"] == "testuser"
        
        # Verify all methods were called
        mock_exchange_token.assert_called_once_with("test_code", "test_verifier")
        mock_get_user_info.assert_called_once_with("test_token")
        mock_create_user.assert_called_once()
        mock_create_jwt.assert_called_once_with(mock_user)
