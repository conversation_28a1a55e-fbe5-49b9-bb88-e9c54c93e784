<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/96c5a8811fcdd0f0.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-d3d0a601b14eaefe.js"/><script src="/_next/static/chunks/4bd1b696-dcfcdc570c79c271.js" async=""></script><script src="/_next/static/chunks/684-53733ca48c812d60.js" async=""></script><script src="/_next/static/chunks/main-app-bf250d8ffdf20ddb.js" async=""></script><script src="/_next/static/chunks/464-d428eda3d36d5b11.js" async=""></script><script src="/_next/static/chunks/794-28dd786855561576.js" async=""></script><script src="/_next/static/chunks/app/layout-1c48f3a60e9fb700.js" async=""></script><script src="/_next/static/chunks/874-0451bdd8dbf73b46.js" async=""></script><script src="/_next/static/chunks/app/settings/page-c6d8f4e33eb2c8f3.js" async=""></script><title>Reachly</title><meta name="description" content="Automate your Twitter growth with AI-powered content creation and engagement"/><link rel="shortcut icon" href="/favicon.svg"/><link rel="icon" href="/favicon.svg"/><link rel="apple-touch-icon" href="/favicon.svg"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_e8ce0c font-sans antialiased"><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm border-b border-gray-200 relative z-50"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between items-center h-16"><div class="flex items-center"><a href="/dashboard"><div class="flex items-center space-x-2 select-none"><div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0"><span class="text-white font-bold text-lg">A</span></div><span class="text-xl font-bold text-gray-900 whitespace-nowrap">AutoReach</span></div></a></div><nav class="hidden md:flex space-x-6"><a class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-50 " href="/dashboard">Dashboard</a><a class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-50 " href="/content">Content</a><a class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-50 " href="/analytics">Analytics</a><a class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-primary-600 bg-primary-50 " href="/settings">Settings</a><a class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-50 " href="/auth">Auth</a><a class="px-3 py-2 rounded-md text-sm font-medium transition-colors text-gray-600 hover:text-gray-900 hover:bg-gray-50 bg-green-50 text-green-700 hover:bg-green-100" href="/test-connection">Test API</a></nav><div class="flex items-center space-x-2"><button class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500" aria-label="Toggle mobile menu"><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button><a class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors" href="/login">Sign In</a></div></div></div></header><main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="mb-8"><h1 class="text-3xl font-bold text-gray-900">Settings</h1><p class="text-gray-600 mt-2">Manage your account and application preferences.</p></div><div class="grid grid-cols-1 lg:grid-cols-3 gap-8"><div class="lg:col-span-1"><nav class="bg-white rounded-lg shadow-sm border border-gray-200 p-4"><ul class="space-y-2"><li><button class="w-full text-left px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md">Account</button></li><li><button class="w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md">Twitter Integration</button></li><li><button class="w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md">Content Preferences</button></li><li><button class="w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md">Notifications</button></li><li><button class="w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md">Billing</button></li></ul></nav></div><div class="lg:col-span-2"><div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"><h2 class="text-xl font-semibold text-gray-900 mb-6">Account Settings</h2><form class="space-y-6"><div><h3 class="text-lg font-medium text-gray-900 mb-4">Profile Information</h3><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name</label><input type="text" id="firstName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="John"/></div><div><label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label><input type="text" id="lastName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="Doe"/></div></div><div class="mt-4"><label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label><input type="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="<EMAIL>"/></div></div><div class="border-t border-gray-200 pt-6"><h3 class="text-lg font-medium text-gray-900 mb-4">Connected Accounts</h3><div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg"><div class="flex items-center space-x-2"><svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg><span class="text-yellow-800">Please log in to connect your Twitter account</span></div></div></div><div class="border-t border-gray-200 pt-6"><h3 class="text-lg font-medium text-gray-900 mb-4">Content Preferences</h3><div class="space-y-4"><div><label for="defaultTone" class="block text-sm font-medium text-gray-700 mb-2">Default Tone</label><select id="defaultTone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"><option value="professional">Professional</option><option value="casual">Casual</option><option value="humorous">Humorous</option><option value="inspirational">Inspirational</option></select></div><div><label for="defaultLength" class="block text-sm font-medium text-gray-700 mb-2">Default Length</label><select id="defaultLength" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"><option value="short">Short</option><option value="medium">Medium</option><option value="long">Long</option></select></div><div class="space-y-3"><div class="flex items-center"><input type="checkbox" id="autoHashtags" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" checked=""/><label for="autoHashtags" class="ml-2 text-sm text-gray-700">Automatically include hashtags</label></div><div class="flex items-center"><input type="checkbox" id="autoEmojis" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"/><label for="autoEmojis" class="ml-2 text-sm text-gray-700">Automatically include emojis</label></div></div></div></div><div class="border-t border-gray-200 pt-6"><h3 class="text-lg font-medium text-gray-900 mb-4">Notifications</h3><div class="space-y-3"><div class="flex items-center"><input type="checkbox" id="emailNotifications" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" checked=""/><label for="emailNotifications" class="ml-2 text-sm text-gray-700">Email notifications for published posts</label></div><div class="flex items-center"><input type="checkbox" id="weeklyReports" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" checked=""/><label for="weeklyReports" class="ml-2 text-sm text-gray-700">Weekly analytics reports</label></div><div class="flex items-center"><input type="checkbox" id="failureAlerts" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" checked=""/><label for="failureAlerts" class="ml-2 text-sm text-gray-700">Alerts for failed posts</label></div></div></div><div class="border-t border-gray-200 pt-6"><div class="flex justify-end space-x-3"><button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">Cancel</button><button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">Save Changes</button></div></div></form></div></div></div></main></div><!--$--><!--/$--><!--$--><!--/$--><script src="/_next/static/chunks/webpack-d3d0a601b14eaefe.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[8794,[\"464\",\"static/chunks/464-d428eda3d36d5b11.js\",\"794\",\"static/chunks/794-28dd786855561576.js\",\"177\",\"static/chunks/app/layout-1c48f3a60e9fb700.js\"],\"AuthProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[7864,[\"464\",\"static/chunks/464-d428eda3d36d5b11.js\",\"874\",\"static/chunks/874-0451bdd8dbf73b46.js\",\"794\",\"static/chunks/794-28dd786855561576.js\",\"662\",\"static/chunks/app/settings/page-c6d8f4e33eb2c8f3.js\"],\"default\"]\n6:I[4590,[\"464\",\"static/chunks/464-d428eda3d36d5b11.js\",\"874\",\"static/chunks/874-0451bdd8dbf73b46.js\",\"794\",\"static/chunks/794-28dd786855561576.js\",\"662\",\"static/chunks/app/settings/page-c6d8f4e33eb2c8f3.js\"],\"default\"]\n7:I[9665,[],\"MetadataBoundary\"]\n9:I[9665,[],\"OutletBoundary\"]\nc:I[4911,[],\"AsyncMetadataOutlet\"]\ne:I[9665,[],\"ViewportBoundary\"]\n10:I[6614,[],\"\"]\n:HL[\"/_next/static/css/96c5a8811fcdd0f0.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"o4ILAc_RI2SHu5NpYzW5F\",\"p\":\"\",\"c\":[\"\",\"settings\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"settings\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/96c5a8811fcdd0f0.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_e8ce0c font-sans antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"settings\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[[\"$\",\"$L5\",null,{}],[\"$\",\"main\",null,{\"className\":\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-8\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold text-gray-900\",\"children\":\"Settings\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 mt-2\",\"children\":\"Manage your account and application preferences.\"}]]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-3 gap-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"lg:col-span-1\",\"children\":[\"$\",\"nav\",null,{\"className\":\"bg-white rounded-lg shadow-sm border border-gray-200 p-4\",\"children\":[\"$\",\"ul\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"button\",null,{\"className\":\"w-full text-left px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md\",\"children\":\"Account\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"button\",null,{\"className\":\"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md\",\"children\":\"Twitter Integration\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"button\",null,{\"className\":\"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md\",\"children\":\"Content Preferences\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"button\",null,{\"className\":\"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md\",\"children\":\"Notifications\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"button\",null,{\"className\":\"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md\",\"children\":\"Billing\"}]}]]}]}]}],[\"$\",\"div\",null,{\"className\":\"lg:col-span-2\",\"children\":[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900 mb-6\",\"children\":\"Account Settings\"}],[\"$\",\"form\",null,{\"className\":\"space-y-6\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-medium text-gray-900 mb-4\",\"children\":\"Profile Information\"}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 md:grid-cols-2 gap-4\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"label\",null,{\"htmlFor\":\"firstName\",\"className\":\"block text-sm font-medium text-gray-700 mb-2\",\"children\":\"First Name\"}],[\"$\",\"input\",null,{\"type\":\"text\",\"id\":\"firstName\",\"defaultValue\":\"John\",\"className\":\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"label\",null,{\"htmlFor\":\"lastName\",\"className\":\"block text-sm font-medium text-gray-700 mb-2\",\"children\":\"Last Name\"}],[\"$\",\"input\",null,{\"type\":\"text\",\"id\":\"lastName\",\"defaultValue\":\"Doe\",\"className\":\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-4\",\"children\":[[\"$\",\"label\",null,{\"htmlFor\":\"email\",\"className\":\"block text-sm font-medium text-gray-700 mb-2\",\"children\":\"Email Address\"}],[\"$\",\"input\",null,{\"type\":\"email\",\"id\":\"email\",\"defaultValue\":\"<EMAIL>\",\"className\":\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"border-t border-gray-200 pt-6\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-medium text-gray-900 mb-4\",\"children\":\"Connected Accounts\"}],[\"$\",\"$L6\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"border-t border-gray-200 pt-6\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-medium text-gray-900 mb-4\",\"children\":\"Content Preferences\"}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"div\",null,{\"children\":[[\"$\",\"label\",null,{\"htmlFor\":\"defaultTone\",\"className\":\"block text-sm font-medium text-gray-700 mb-2\",\"children\":\"Default Tone\"}],[\"$\",\"select\",null,{\"id\":\"defaultTone\",\"className\":\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\"children\":[[\"$\",\"option\",null,{\"value\":\"professional\",\"children\":\"Professional\"}],[\"$\",\"option\",null,{\"value\":\"casual\",\"children\":\"Casual\"}],[\"$\",\"option\",null,{\"value\":\"humorous\",\"children\":\"Humorous\"}],[\"$\",\"option\",null,{\"value\":\"inspirational\",\"children\":\"Inspirational\"}]]}]]}],[\"$\",\"div\",null,{\"children\":[[\"$\",\"label\",null,{\"htmlFor\":\"defaultLength\",\"className\":\"block text-sm font-medium text-gray-700 mb-2\",\"children\":\"Default Length\"}],[\"$\",\"select\",null,{\"id\":\"defaultLength\",\"className\":\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\",\"children\":[[\"$\",\"option\",null,{\"value\":\"short\",\"children\":\"Short\"}],[\"$\",\"option\",null,{\"value\":\"medium\",\"children\":\"Medium\"}],[\"$\",\"option\",null,{\"value\":\"long\",\"children\":\"Long\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-3\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"input\",null,{\"type\":\"checkbox\",\"id\":\"autoHashtags\",\"defaultChecked\":true,\"className\":\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}],[\"$\",\"label\",null,{\"htmlFor\":\"autoHashtags\",\"className\":\"ml-2 text-sm text-gray-700\",\"children\":\"Automatically include hashtags\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"input\",null,{\"type\":\"checkbox\",\"id\":\"autoEmojis\",\"className\":\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}],[\"$\",\"label\",null,{\"htmlFor\":\"autoEmojis\",\"className\":\"ml-2 text-sm text-gray-700\",\"children\":\"Automatically include emojis\"}]]}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"border-t border-gray-200 pt-6\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-lg font-medium text-gray-900 mb-4\",\"children\":\"Notifications\"}],[\"$\",\"div\",null,{\"className\":\"space-y-3\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"input\",null,{\"type\":\"checkbox\",\"id\":\"emailNotifications\",\"defaultChecked\":true,\"className\":\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}],[\"$\",\"label\",null,{\"htmlFor\":\"emailNotifications\",\"className\":\"ml-2 text-sm text-gray-700\",\"children\":\"Email notifications for published posts\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"input\",null,{\"type\":\"checkbox\",\"id\":\"weeklyReports\",\"defaultChecked\":true,\"className\":\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}],[\"$\",\"label\",null,{\"htmlFor\":\"weeklyReports\",\"className\":\"ml-2 text-sm text-gray-700\",\"children\":\"Weekly analytics reports\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"input\",null,{\"type\":\"checkbox\",\"id\":\"failureAlerts\",\"defaultChecked\":true,\"className\":\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"}],[\"$\",\"label\",null,{\"htmlFor\":\"failureAlerts\",\"className\":\"ml-2 text-sm text-gray-700\",\"children\":\"Alerts for failed posts\"}]]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"border-t border-gray-200 pt-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex justify-end space-x-3\",\"children\":[[\"$\",\"button\",null,{\"type\":\"button\",\"className\":\"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\"children\":\"Cancel\"}],[\"$\",\"button\",null,{\"type\":\"submit\",\"className\":\"px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\",\"children\":\"Save Changes\"}]]}]}]]}]]}]}]]}]]}]]}],[\"$\",\"$L7\",null,{\"children\":\"$L8\"}],null,[\"$\",\"$L9\",null,{\"children\":[\"$La\",\"$Lb\",[\"$\",\"$Lc\",null,{\"promise\":\"$@d\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"pQFLO5U90jvIqO6UTlcmX\",{\"children\":[[\"$\",\"$Le\",null,{\"children\":\"$Lf\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$10\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"11:\"$Sreact.suspense\"\n12:I[4911,[],\"AsyncMetadata\"]\n8:[\"$\",\"$11\",null,{\"fallback\":null,\"children\":[\"$\",\"$L12\",null,{\"promise\":\"$@13\"}]}]\n"])</script><script>self.__next_f.push([1,"b:null\n"])</script><script>self.__next_f.push([1,"f:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\na:null\n"])</script><script>self.__next_f.push([1,"13:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Reachly\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Automate your Twitter growth with AI-powered content creation and engagement\"}],[\"$\",\"link\",\"2\",{\"rel\":\"shortcut icon\",\"href\":\"/favicon.svg\"}],[\"$\",\"link\",\"3\",{\"rel\":\"icon\",\"href\":\"/favicon.svg\"}],[\"$\",\"link\",\"4\",{\"rel\":\"apple-touch-icon\",\"href\":\"/favicon.svg\"}]],\"error\":null,\"digest\":\"$undefined\"}\nd:{\"metadata\":\"$13:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>