1:"$Sreact.fragment"
2:I[8794,["464","static/chunks/464-d428eda3d36d5b11.js","794","static/chunks/794-28dd786855561576.js","177","static/chunks/app/layout-1c48f3a60e9fb700.js"],"AuthProvider"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[7864,["464","static/chunks/464-d428eda3d36d5b11.js","874","static/chunks/874-0451bdd8dbf73b46.js","794","static/chunks/794-28dd786855561576.js","662","static/chunks/app/settings/page-c6d8f4e33eb2c8f3.js"],"default"]
6:I[4590,["464","static/chunks/464-d428eda3d36d5b11.js","874","static/chunks/874-0451bdd8dbf73b46.js","794","static/chunks/794-28dd786855561576.js","662","static/chunks/app/settings/page-c6d8f4e33eb2c8f3.js"],"default"]
7:I[9665,[],"MetadataBoundary"]
9:I[9665,[],"OutletBoundary"]
c:I[4911,[],"AsyncMetadataOutlet"]
e:I[9665,[],"ViewportBoundary"]
10:I[6614,[],""]
:HL["/_next/static/css/96c5a8811fcdd0f0.css","style"]
0:{"P":null,"b":"E1ryna7ZOGUwrkQgq6t1C","p":"","c":["","settings"],"i":false,"f":[[["",{"children":["settings",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/96c5a8811fcdd0f0.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_e8ce0c font-sans antialiased","children":["$","$L2",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["settings",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-gray-50","children":[["$","$L5",null,{}],["$","main",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8","children":[["$","div",null,{"className":"mb-8","children":[["$","h1",null,{"className":"text-3xl font-bold text-gray-900","children":"Settings"}],["$","p",null,{"className":"text-gray-600 mt-2","children":"Manage your account and application preferences."}]]}],["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-3 gap-8","children":[["$","div",null,{"className":"lg:col-span-1","children":["$","nav",null,{"className":"bg-white rounded-lg shadow-sm border border-gray-200 p-4","children":["$","ul",null,{"className":"space-y-2","children":[["$","li",null,{"children":["$","button",null,{"className":"w-full text-left px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md","children":"Account"}]}],["$","li",null,{"children":["$","button",null,{"className":"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md","children":"Twitter Integration"}]}],["$","li",null,{"children":["$","button",null,{"className":"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md","children":"Content Preferences"}]}],["$","li",null,{"children":["$","button",null,{"className":"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md","children":"Notifications"}]}],["$","li",null,{"children":["$","button",null,{"className":"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md","children":"Billing"}]}]]}]}]}],["$","div",null,{"className":"lg:col-span-2","children":["$","div",null,{"className":"bg-white rounded-lg shadow-sm border border-gray-200 p-6","children":[["$","h2",null,{"className":"text-xl font-semibold text-gray-900 mb-6","children":"Account Settings"}],["$","form",null,{"className":"space-y-6","children":[["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-4","children":"Profile Information"}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 gap-4","children":[["$","div",null,{"children":[["$","label",null,{"htmlFor":"firstName","className":"block text-sm font-medium text-gray-700 mb-2","children":"First Name"}],["$","input",null,{"type":"text","id":"firstName","defaultValue":"John","className":"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}]]}],["$","div",null,{"children":[["$","label",null,{"htmlFor":"lastName","className":"block text-sm font-medium text-gray-700 mb-2","children":"Last Name"}],["$","input",null,{"type":"text","id":"lastName","defaultValue":"Doe","className":"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}]]}]]}],["$","div",null,{"className":"mt-4","children":[["$","label",null,{"htmlFor":"email","className":"block text-sm font-medium text-gray-700 mb-2","children":"Email Address"}],["$","input",null,{"type":"email","id":"email","defaultValue":"<EMAIL>","className":"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"}]]}]]}],["$","div",null,{"className":"border-t border-gray-200 pt-6","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-4","children":"Connected Accounts"}],["$","$L6",null,{}]]}],["$","div",null,{"className":"border-t border-gray-200 pt-6","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-4","children":"Content Preferences"}],["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"children":[["$","label",null,{"htmlFor":"defaultTone","className":"block text-sm font-medium text-gray-700 mb-2","children":"Default Tone"}],["$","select",null,{"id":"defaultTone","className":"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent","children":[["$","option",null,{"value":"professional","children":"Professional"}],["$","option",null,{"value":"casual","children":"Casual"}],["$","option",null,{"value":"humorous","children":"Humorous"}],["$","option",null,{"value":"inspirational","children":"Inspirational"}]]}]]}],["$","div",null,{"children":[["$","label",null,{"htmlFor":"defaultLength","className":"block text-sm font-medium text-gray-700 mb-2","children":"Default Length"}],["$","select",null,{"id":"defaultLength","className":"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent","children":[["$","option",null,{"value":"short","children":"Short"}],["$","option",null,{"value":"medium","children":"Medium"}],["$","option",null,{"value":"long","children":"Long"}]]}]]}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex items-center","children":[["$","input",null,{"type":"checkbox","id":"autoHashtags","defaultChecked":true,"className":"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}],["$","label",null,{"htmlFor":"autoHashtags","className":"ml-2 text-sm text-gray-700","children":"Automatically include hashtags"}]]}],["$","div",null,{"className":"flex items-center","children":[["$","input",null,{"type":"checkbox","id":"autoEmojis","className":"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}],["$","label",null,{"htmlFor":"autoEmojis","className":"ml-2 text-sm text-gray-700","children":"Automatically include emojis"}]]}]]}]]}]]}],["$","div",null,{"className":"border-t border-gray-200 pt-6","children":[["$","h3",null,{"className":"text-lg font-medium text-gray-900 mb-4","children":"Notifications"}],["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex items-center","children":[["$","input",null,{"type":"checkbox","id":"emailNotifications","defaultChecked":true,"className":"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}],["$","label",null,{"htmlFor":"emailNotifications","className":"ml-2 text-sm text-gray-700","children":"Email notifications for published posts"}]]}],["$","div",null,{"className":"flex items-center","children":[["$","input",null,{"type":"checkbox","id":"weeklyReports","defaultChecked":true,"className":"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}],["$","label",null,{"htmlFor":"weeklyReports","className":"ml-2 text-sm text-gray-700","children":"Weekly analytics reports"}]]}],["$","div",null,{"className":"flex items-center","children":[["$","input",null,{"type":"checkbox","id":"failureAlerts","defaultChecked":true,"className":"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}],["$","label",null,{"htmlFor":"failureAlerts","className":"ml-2 text-sm text-gray-700","children":"Alerts for failed posts"}]]}]]}]]}],["$","div",null,{"className":"border-t border-gray-200 pt-6","children":["$","div",null,{"className":"flex justify-end space-x-3","children":[["$","button",null,{"type":"button","className":"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2","children":"Cancel"}],["$","button",null,{"type":"submit","className":"px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2","children":"Save Changes"}]]}]}]]}]]}]}]]}]]}]]}],["$","$L7",null,{"children":"$L8"}],null,["$","$L9",null,{"children":["$La","$Lb",["$","$Lc",null,{"promise":"$@d"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","ha1e1QOn_zxpkbOUOLnXI",{"children":[["$","$Le",null,{"children":"$Lf"}],null]}],null]}],false]],"m":"$undefined","G":["$10","$undefined"],"s":false,"S":true}
11:"$Sreact.suspense"
12:I[4911,[],"AsyncMetadata"]
8:["$","$11",null,{"fallback":null,"children":["$","$L12",null,{"promise":"$@13"}]}]
b:null
f:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
a:null
13:{"metadata":[["$","title","0",{"children":"Reachly"}],["$","meta","1",{"name":"description","content":"Automate your Twitter growth with AI-powered content creation and engagement"}],["$","link","2",{"rel":"shortcut icon","href":"/favicon.svg"}],["$","link","3",{"rel":"icon","href":"/favicon.svg"}],["$","link","4",{"rel":"apple-touch-icon","href":"/favicon.svg"}]],"error":null,"digest":"$undefined"}
d:{"metadata":"$13:metadata","error":null,"digest":"$undefined"}
