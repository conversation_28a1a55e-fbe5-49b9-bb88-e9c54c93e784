(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[35],{4:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(5155),r=a(2115),n=a(3851),i=a(8600);function l(){let[e,t]=(0,r.useState)(null),[a,l]=(0,r.useState)(null),[o,c]=(0,r.useState)(!1),d=async()=>{c(!0),l(null),t(null);try{let e=await n.y.initiateTwitterOAuth2();t(e)}catch(e){l(e instanceof Error?e.message:"An unknown error occurred")}finally{c(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Twitter OAuth 2.0 Test"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Test the Twitter OAuth 2.0 implementation for user authentication"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Manual API Test"}),(0,s.jsx)("button",{onClick:d,disabled:o,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md disabled:opacity-50 mb-4",children:o?"Testing...":"Test OAuth 2.0 Init"}),a&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4",children:[(0,s.jsx)("h3",{className:"font-medium",children:"Error:"}),(0,s.jsx)("p",{className:"text-sm",children:a})]}),e&&(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md",children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Success:"}),(0,s.jsx)("pre",{className:"text-xs overflow-auto bg-white p-2 rounded border",children:JSON.stringify(e,null,2)})]})]}),(0,s.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Component Test"}),(0,s.jsx)(i.A,{onError:e=>{l(e)}}),e&&"success"in e&&e.success&&(0,s.jsxs)("div",{className:"mt-4 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md",children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Login Success:"}),(0,s.jsx)("pre",{className:"text-xs overflow-auto bg-white p-2 rounded border",children:JSON.stringify(e.user,null,2)})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 bg-white shadow-md rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"API Endpoints"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"POST /api/auth/oauth2/twitter/init"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Initialize Twitter OAuth 2.0 flow"}),(0,s.jsx)("code",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:'{ "redirect_uri": "http://localhost:3000/auth/twitter/oauth2-callback" }'})]}),(0,s.jsxs)("div",{className:"border-l-4 border-green-500 pl-4",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900",children:"POST /api/auth/oauth2/twitter/callback"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Handle Twitter OAuth 2.0 callback"}),(0,s.jsx)("code",{className:"text-xs bg-gray-100 px-2 py-1 rounded",children:'{ "code": "...", "state": "...", "code_verifier": "..." }'})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 bg-white shadow-md rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Environment Check"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Backend URL:"}),(0,s.jsx)("span",{className:"font-mono",children:"http://127.0.0.1:8000"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Frontend URL:"}),(0,s.jsx)("span",{className:"font-mono",children:"http://localhost:3000"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"OAuth Callback:"}),(0,s.jsx)("span",{className:"font-mono",children:"http://localhost:3000/auth/twitter/oauth2-callback"})]})]})]})]})})}},2703:(e,t,a)=>{"use strict";a.d(t,{I:()=>r});class s{getItem(e){try{return localStorage.getItem(e)}catch(e){return null}}setItem(e,t){try{localStorage.setItem(e,t)}catch(e){}}removeItem(e){try{localStorage.removeItem(e)}catch(e){}}clear(){try{localStorage.clear()}catch(e){}}}let r=new s},3851:(e,t,a)=>{"use strict";a.d(t,{y:()=>l});var s=a(4611),r=a(2703),n=a(4205);class i{async login(e){this.validateLoginRequest(e);let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let a=await this.apiClient.instance.post("/auth/token",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return r.I.setItem(s.d5.AUTH_TOKEN,a.data.access_token),a.data}async register(e){return this.validateRegisterRequest(e),this.apiClient.post("/users/",e)}async getCurrentUser(){return this.apiClient.get("/auth/me")}async logout(){r.I.removeItem(s.d5.AUTH_TOKEN),window.location.href="/login"}isAuthenticated(){return!!r.I.getItem(s.d5.AUTH_TOKEN)}getToken(){return r.I.getItem(s.d5.AUTH_TOKEN)}async initiateTwitterOAuth2(){return(await this.apiClient.instance.post("/auth/oauth2/twitter/init")).data}async handleTwitterOAuth2Callback(e){let t=await this.apiClient.instance.post("/auth/oauth2/twitter/callback",e);return r.I.setItem(s.d5.AUTH_TOKEN,t.data.access_token),t.data}validateLoginRequest(e){var t,a;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(a=e.password)?void 0:a.trim()))throw Error("Password is required")}validateRegisterRequest(e){var t,a,s;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(a=e.email)?void 0:a.trim()))throw Error("Email is required");if(!(null==(s=e.password)?void 0:s.trim()))throw Error("Password is required");if(e.password.length<6)throw Error("Password must be at least 6 characters");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email))throw Error("Please enter a valid email address")}constructor(e){this.apiClient=e}}let l=new i(n.uE)},4205:(e,t,a)=>{"use strict";a.d(t,{uE:()=>l});var s=a(3464),r=a(4611),n=a(2703);class i{createClient(){return s.A.create({baseURL:r.i3.BASE_URL,timeout:r.i3.TIMEOUT,headers:{"Content-Type":"application/json"}})}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.storage.getItem(r.d5.AUTH_TOKEN);return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(this.handleError(e))),this.client.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(this.handleError(e))})}handleUnauthorized(){this.storage.removeItem(r.d5.AUTH_TOKEN),window.location.href="/login"}handleError(e){if(!e.response)return Error(r.UU.NETWORK_ERROR);switch(e.response.status){case 401:return Error(r.UU.UNAUTHORIZED);case 400:return Error(r.UU.VALIDATION_ERROR);default:let t=e.response.data;return Error((null==t?void 0:t.message)||r.UU.GENERIC_ERROR)}}get instance(){return this.client}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(e=n.I){this.storage=e,this.client=this.createClient(),this.setupInterceptors()}}let l=new i},4611:(e,t,a)=>{"use strict";a.d(t,{ID:()=>r,Ij:()=>s,KA:()=>o,Ot:()=>l,RW:()=>E,UU:()=>u,WF:()=>m,d5:()=>d,gY:()=>c,gx:()=>h,i3:()=>i,m0:()=>n});let s=[{name:"Dashboard",path:"/dashboard"},{name:"Content",path:"/content"},{name:"Analytics",path:"/analytics"},{name:"Settings",path:"/settings"},{name:"Auth",path:"/auth"},{name:"Test API",path:"/test-connection"}],r=[{value:"engaging",label:"Engaging"},{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"educational",label:"Educational"},{value:"humorous",label:"Humorous"},{value:"informative",label:"Informative"},{value:"helpful",label:"Helpful"}],n=[{value:"short",label:"Short (1-2 sentences)"},{value:"medium",label:"Medium (3-5 sentences)"},{value:"long",label:"Long (6+ sentences)"}],i={BASE_URL:"http://localhost:8000/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},l={MAX_TWEET_LENGTH:280,MAX_THREAD_TWEETS:25,DEFAULT_THREAD_SIZE:3,MAX_HASHTAGS_RECOMMENDED:3,MAX_MENTIONS_RECOMMENDED:5},o={DEFAULT_STYLE:"engaging",DEFAULT_LANGUAGE:"en",SUPPORTED_STYLES:["engaging","professional","casual","educational","humorous","informative","helpful"],SUPPORTED_LANGUAGES:[{code:"en",name:"English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"}]},c={MIN_TOPIC_LENGTH:3,MAX_TOPIC_LENGTH:200,MIN_CONTENT_LENGTH:1,MAX_CONTENT_LENGTH:2e3,MIN_USERNAME_LENGTH:3,MAX_USERNAME_LENGTH:50,MIN_PASSWORD_LENGTH:8,MAX_PASSWORD_LENGTH:128},d={AUTH_TOKEN:"authToken",USER_PREFERENCES:"userPreferences",DRAFT_CONTENT:"draftContent",THEME:"theme"},u={NETWORK_ERROR:"Network error. Please check your connection.",UNAUTHORIZED:"Your session has expired. Please log in again.",FORBIDDEN:"Access denied.",NOT_FOUND:"The requested resource was not found.",VALIDATION_ERROR:"Please check your input and try again.",GENERATION_FAILED:"Content generation failed. Please try again.",RATE_LIMIT_EXCEEDED:"Rate limit exceeded. Please try again later.",GENERIC_ERROR:"Something went wrong. Please try again."},h={OK:200,CREATED:201,NO_CONTENT:204,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,CONFLICT:409,UNPROCESSABLE_ENTITY:422,TOO_MANY_REQUESTS:429,INTERNAL_SERVER_ERROR:500,SERVICE_UNAVAILABLE:503},m={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,USERNAME:/^[a-zA-Z0-9_]+$/,PASSWORD:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,HASHTAG:/#\w+/g,MENTION:/@\w+/g,URL:/https?:\/\/[^\s]+/g},E=e=>{switch(e){case h.UNAUTHORIZED:return u.UNAUTHORIZED;case h.FORBIDDEN:return u.FORBIDDEN;case h.NOT_FOUND:return u.NOT_FOUND;case h.UNPROCESSABLE_ENTITY:return u.VALIDATION_ERROR;case h.TOO_MANY_REQUESTS:return u.RATE_LIMIT_EXCEEDED;case h.INTERNAL_SERVER_ERROR:case h.SERVICE_UNAVAILABLE:return u.GENERATION_FAILED;default:return u.GENERIC_ERROR}}},6849:(e,t,a)=>{Promise.resolve().then(a.bind(a,4))},8600:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var s=a(5155),r=a(2115),n=a(3851);function i(e){let{onError:t,className:a=""}=e,[i,l]=(0,r.useState)(!1),[o,c]=(0,r.useState)(null),d=async()=>{l(!0),c(null);try{console.log("Starting Twitter OAuth...");let e=await n.y.initiateTwitterOAuth2();console.log("Auth data received:",{hasAuthUrl:!!e.authorization_url,state:e.state}),sessionStorage.setItem("twitter_oauth2_state",e.state),sessionStorage.setItem("twitter_oauth2_code_verifier",e.code_verifier),window.location.href=e.authorization_url}catch(a){let e=a instanceof Error?"Failed to start Twitter login: ".concat(a.message):"Failed to start Twitter login: Unknown error occurred";c(e),null==t||t(e),l(!1)}};return(0,s.jsxs)("div",{className:"space-y-4 ".concat(a),children:[o&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:(0,s.jsx)("p",{className:"text-sm",children:o})}),(0,s.jsx)("button",{onClick:d,disabled:i,className:"\n          w-full flex items-center justify-center px-4 py-3 border border-transparent\n          text-base font-medium rounded-md text-white bg-blue-500 hover:bg-blue-600\n          focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\n          disabled:opacity-50 disabled:cursor-not-allowed\n          transition-colors duration-200\n        ",children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Connecting to Twitter..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})}),"Continue with Twitter"]})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"By continuing, you agree to our Terms of Service and Privacy Policy"})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,441,684,358],()=>t(6849)),_N_E=e.O()}]);