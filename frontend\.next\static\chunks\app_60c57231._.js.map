{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/constants.ts"], "sourcesContent": ["// Navigation configuration\nexport const NAVIGATION_ITEMS = [\n  { name: 'Dashboard', path: '/dashboard' },\n  { name: 'Content', path: '/content' },\n  { name: 'Analytics', path: '/analytics' },\n  { name: 'Settings', path: '/settings' },\n  { name: 'Auth', path: '/auth' },\n  { name: 'Test API', path: '/test-connection' },\n] as const;\n\n// Content generation options - synchronized with backend\nexport const CONTENT_TONES = [\n  { value: 'engaging', label: 'Engaging' },\n  { value: 'professional', label: 'Professional' },\n  { value: 'casual', label: 'Casual' },\n  { value: 'educational', label: 'Educational' },\n  { value: 'humorous', label: 'Humorous' },\n  { value: 'informative', label: 'Informative' },\n  { value: 'helpful', label: 'Helpful' },\n] as const;\n\nexport const CONTENT_LENGTHS = [\n  { value: 'short', label: 'Short (1-2 sentences)' },\n  { value: 'medium', label: 'Medium (3-5 sentences)' },\n  { value: 'long', label: 'Long (6+ sentences)' },\n] as const;\n\n// API configuration\nexport const API_CONFIG = {\n  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',\n  TIMEOUT: 30000,\n  RETRY_ATTEMPTS: 3,\n  RETRY_DELAY: 1000, // 1 second\n} as const;\n\n// Twitter Constants\nexport const TWITTER_CONSTANTS = {\n  MAX_TWEET_LENGTH: 280,\n  MAX_THREAD_TWEETS: 25,\n  DEFAULT_THREAD_SIZE: 3,\n  MAX_HASHTAGS_RECOMMENDED: 3,\n  MAX_MENTIONS_RECOMMENDED: 5,\n} as const;\n\n// Content Generation\nexport const CONTENT_CONSTANTS = {\n  DEFAULT_STYLE: 'engaging',\n  DEFAULT_LANGUAGE: 'en',\n  SUPPORTED_STYLES: [\n    'engaging',\n    'professional',\n    'casual',\n    'educational',\n    'humorous',\n    'informative',\n    'helpful'\n  ],\n  SUPPORTED_LANGUAGES: [\n    { code: 'en', name: 'English' },\n    { code: 'es', name: 'Spanish' },\n    { code: 'fr', name: 'French' },\n    { code: 'de', name: 'German' },\n    { code: 'it', name: 'Italian' },\n    { code: 'pt', name: 'Portuguese' }\n  ],\n} as const;\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  MIN_TOPIC_LENGTH: 3,\n  MAX_TOPIC_LENGTH: 200,\n  MIN_CONTENT_LENGTH: 1,\n  MAX_CONTENT_LENGTH: 2000,\n  MIN_USERNAME_LENGTH: 3,\n  MAX_USERNAME_LENGTH: 50,\n  MIN_PASSWORD_LENGTH: 8,\n  MAX_PASSWORD_LENGTH: 128,\n} as const;\n\n// UI Constants\nexport const UI_CONSTANTS = {\n  DEFAULT_PAGE_SIZE: 20,\n  MAX_PAGE_SIZE: 100,\n  DEBOUNCE_DELAY: 300, // milliseconds\n  TOAST_DURATION: 5000, // 5 seconds\n  LOADING_SPINNER_DELAY: 200, // milliseconds\n} as const;\n\n// Storage keys\nexport const STORAGE_KEYS = {\n  AUTH_TOKEN: 'authToken',\n  USER_PREFERENCES: 'userPreferences',\n  DRAFT_CONTENT: 'draftContent',\n  THEME: 'theme',\n} as const;\n\n// Content Generation Modes\nexport const CONTENT_MODES = {\n  NEW_TWEET: 'new_tweet',\n  REPLY: 'reply',\n  THREAD: 'thread',\n  REWRITE: 'rewrite',\n} as const;\n\n// Post Status\nexport const POST_STATUS = {\n  PENDING: 'pending',\n  POSTED: 'posted',\n  FAILED: 'failed',\n  CANCELLED: 'cancelled',\n} as const;\n\n// Error messages\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'Your session has expired. Please log in again.',\n  FORBIDDEN: 'Access denied.',\n  NOT_FOUND: 'The requested resource was not found.',\n  VALIDATION_ERROR: 'Please check your input and try again.',\n  GENERATION_FAILED: 'Content generation failed. Please try again.',\n  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',\n  GENERIC_ERROR: 'Something went wrong. Please try again.',\n} as const;\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  CONTENT_GENERATED: 'Content generated successfully!',\n  CONTENT_SAVED: 'Content saved successfully!',\n  SETTINGS_UPDATED: 'Settings updated successfully!',\n  LOGIN_SUCCESS: 'Login successful!',\n  LOGOUT_SUCCESS: 'Logout successful!',\n} as const;\n\n// HTTP Status Codes\nexport const HTTP_STATUS = {\n  OK: 200,\n  CREATED: 201,\n  NO_CONTENT: 204,\n  BAD_REQUEST: 400,\n  UNAUTHORIZED: 401,\n  FORBIDDEN: 403,\n  NOT_FOUND: 404,\n  CONFLICT: 409,\n  UNPROCESSABLE_ENTITY: 422,\n  TOO_MANY_REQUESTS: 429,\n  INTERNAL_SERVER_ERROR: 500,\n  SERVICE_UNAVAILABLE: 503,\n} as const;\n\n// Regular Expressions\nexport const REGEX_PATTERNS = {\n  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/,\n  USERNAME: /^[a-zA-Z0-9_]+$/,\n  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).+$/,\n  HASHTAG: /#\\w+/g,\n  MENTION: /@\\w+/g,\n  URL: /https?:\\/\\/[^\\s]+/g,\n} as const;\n\n// Export types for TypeScript\nexport type ContentStyle = typeof CONTENT_CONSTANTS.SUPPORTED_STYLES[number];\nexport type ContentMode = typeof CONTENT_MODES[keyof typeof CONTENT_MODES];\nexport type PostStatus = typeof POST_STATUS[keyof typeof POST_STATUS];\nexport type NavigationItem = typeof NAVIGATION_ITEMS[number];\nexport type SupportedLanguage = typeof CONTENT_CONSTANTS.SUPPORTED_LANGUAGES[number];\n\n// Utility function to get error message by status code\nexport const getErrorMessageByStatus = (status: number): string => {\n  switch (status) {\n    case HTTP_STATUS.UNAUTHORIZED:\n      return ERROR_MESSAGES.UNAUTHORIZED;\n    case HTTP_STATUS.FORBIDDEN:\n      return ERROR_MESSAGES.FORBIDDEN;\n    case HTTP_STATUS.NOT_FOUND:\n      return ERROR_MESSAGES.NOT_FOUND;\n    case HTTP_STATUS.UNPROCESSABLE_ENTITY:\n      return ERROR_MESSAGES.VALIDATION_ERROR;\n    case HTTP_STATUS.TOO_MANY_REQUESTS:\n      return ERROR_MESSAGES.RATE_LIMIT_EXCEEDED;\n    case HTTP_STATUS.INTERNAL_SERVER_ERROR:\n    case HTTP_STATUS.SERVICE_UNAVAILABLE:\n      return ERROR_MESSAGES.GENERATION_FAILED;\n    default:\n      return ERROR_MESSAGES.GENERIC_ERROR;\n  }\n};\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;;;;;;;;;;;;;;;;AA6Bf;AA5BL,MAAM,mBAAmB;IAC9B;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAmB;CAC9C;AAGM,MAAM,gBAAgB;IAC3B;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAgB,OAAO;IAAe;IAC/C;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAW,OAAO;IAAU;CACtC;AAEM,MAAM,kBAAkB;IAC7B;QAAE,OAAO;QAAS,OAAO;IAAwB;IACjD;QAAE,OAAO;QAAU,OAAO;IAAyB;IACnD;QAAE,OAAO;QAAQ,OAAO;IAAsB;CAC/C;AAGM,MAAM,aAAa;IACxB,UAAU,iEAAmC;IAC7C,SAAS;IACT,gBAAgB;IAChB,aAAa;AACf;AAGO,MAAM,oBAAoB;IAC/B,kBAAkB;IAClB,mBAAmB;IACnB,qBAAqB;IACrB,0BAA0B;IAC1B,0BAA0B;AAC5B;AAGO,MAAM,oBAAoB;IAC/B,eAAe;IACf,kBAAkB;IAClB,kBAAkB;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,qBAAqB;QACnB;YAAE,MAAM;YAAM,MAAM;QAAU;QAC9B;YAAE,MAAM;YAAM,MAAM;QAAU;QAC9B;YAAE,MAAM;YAAM,MAAM;QAAS;QAC7B;YAAE,MAAM;YAAM,MAAM;QAAS;QAC7B;YAAE,MAAM;YAAM,MAAM;QAAU;QAC9B;YAAE,MAAM;YAAM,MAAM;QAAa;KAClC;AACH;AAGO,MAAM,mBAAmB;IAC9B,kBAAkB;IAClB,kBAAkB;IAClB,oBAAoB;IACpB,oBAAoB;IACpB,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;AACvB;AAGO,MAAM,eAAe;IAC1B,mBAAmB;IACnB,eAAe;IACf,gBAAgB;IAChB,gBAAgB;IAChB,uBAAuB;AACzB;AAGO,MAAM,eAAe;IAC1B,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,OAAO;AACT;AAGO,MAAM,gBAAgB;IAC3B,WAAW;IACX,OAAO;IACP,QAAQ;IACR,SAAS;AACX;AAGO,MAAM,cAAc;IACzB,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,WAAW;AACb;AAGO,MAAM,iBAAiB;IAC5B,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,kBAAkB;IAClB,mBAAmB;IACnB,qBAAqB;IACrB,eAAe;AACjB;AAGO,MAAM,mBAAmB;IAC9B,mBAAmB;IACnB,eAAe;IACf,kBAAkB;IAClB,eAAe;IACf,gBAAgB;AAClB;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,SAAS;IACT,YAAY;IACZ,aAAa;IACb,cAAc;IACd,WAAW;IACX,WAAW;IACX,UAAU;IACV,sBAAsB;IACtB,mBAAmB;IACnB,uBAAuB;IACvB,qBAAqB;AACvB;AAGO,MAAM,iBAAiB;IAC5B,OAAO;IACP,UAAU;IACV,UAAU;IACV,SAAS;IACT,SAAS;IACT,KAAK;AACP;AAUO,MAAM,0BAA0B,CAAC;IACtC,OAAQ;QACN,KAAK,YAAY,YAAY;YAC3B,OAAO,eAAe,YAAY;QACpC,KAAK,YAAY,SAAS;YACxB,OAAO,eAAe,SAAS;QACjC,KAAK,YAAY,SAAS;YACxB,OAAO,eAAe,SAAS;QACjC,KAAK,YAAY,oBAAoB;YACnC,OAAO,eAAe,gBAAgB;QACxC,KAAK,YAAY,iBAAiB;YAChC,OAAO,eAAe,mBAAmB;QAC3C,KAAK,YAAY,qBAAqB;QACtC,KAAK,YAAY,mBAAmB;YAClC,OAAO,eAAe,iBAAiB;QACzC;YACE,OAAO,eAAe,aAAa;IACvC;AACF", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/storage.ts"], "sourcesContent": ["// Storage abstraction following Dependency Inversion Principle\nexport interface IStorage {\n  getItem(key: string): string | null;\n  setItem(key: string, value: string): void;\n  removeItem(key: string): void;\n  clear(): void;\n}\n\nclass LocalStorageAdapter implements IStorage {\n  getItem(key: string): string | null {\n    if (typeof window === 'undefined') return null;\n    try {\n      return localStorage.getItem(key);\n    } catch {\n      return null;\n    }\n  }\n\n  setItem(key: string, value: string): void {\n    if (typeof window === 'undefined') return;\n    try {\n      localStorage.setItem(key, value);\n    } catch {\n      // Silently fail if storage is not available\n    }\n  }\n\n  removeItem(key: string): void {\n    if (typeof window === 'undefined') return;\n    try {\n      localStorage.removeItem(key);\n    } catch {\n      // Silently fail if storage is not available\n    }\n  }\n\n  clear(): void {\n    if (typeof window === 'undefined') return;\n    try {\n      localStorage.clear();\n    } catch {\n      // Silently fail if storage is not available\n    }\n  }\n}\n\n// Export singleton instance\nexport const storage: IStorage = new LocalStorageAdapter();\n"], "names": [], "mappings": "AAAA,+DAA+D;;;;AAQ/D,MAAM;IACJ,QAAQ,GAAW,EAAiB;QAClC,uCAAmC;;QAAW;QAC9C,IAAI;YACF,OAAO,aAAa,OAAO,CAAC;QAC9B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,QAAQ,GAAW,EAAE,KAAa,EAAQ;QACxC,uCAAmC;;QAAM;QACzC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK;QAC5B,EAAE,OAAM;QACN,4CAA4C;QAC9C;IACF;IAEA,WAAW,GAAW,EAAQ;QAC5B,uCAAmC;;QAAM;QACzC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAM;QACN,4CAA4C;QAC9C;IACF;IAEA,QAAc;QACZ,uCAAmC;;QAAM;QACzC,IAAI;YACF,aAAa,KAAK;QACpB,EAAE,OAAM;QACN,4CAA4C;QAC9C;IACF;AACF;AAGO,MAAM,UAAoB,IAAI", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/apiClient.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosError } from 'axios';\nimport { API_CONFIG, STORAGE_KEYS, ERROR_MESSAGES } from './constants';\nimport { storage, IStorage } from './storage';\n\n// API Client following Single Responsibility and Dependency Inversion principles\nexport class ApiClient {\n  private client: AxiosInstance;\n  private storage: IStorage;\n\n  constructor(storageAdapter: IStorage = storage) {\n    this.storage = storageAdapter;\n    this.client = this.createClient();\n    this.setupInterceptors();\n  }\n\n  private createClient(): AxiosInstance {\n    return axios.create({\n      baseURL: API_CONFIG.BASE_URL,\n      timeout: API_CONFIG.TIMEOUT,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor\n    this.client.interceptors.request.use(\n      (config) => {\n        const token = this.storage.getItem(STORAGE_KEYS.AUTH_TOKEN);\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(this.handleError(error))\n    );\n\n    // Response interceptor\n    this.client.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          this.handleUnauthorized();\n        }\n        return Promise.reject(this.handleError(error));\n      }\n    );\n  }\n\n  private handleUnauthorized(): void {\n    this.storage.removeItem(STORAGE_KEYS.AUTH_TOKEN);\n    if (typeof window !== 'undefined') {\n      window.location.href = '/login';\n    }\n  }\n\n  private handleError(error: AxiosError): Error {\n    if (!error.response) {\n      return new Error(ERROR_MESSAGES.NETWORK_ERROR);\n    }\n\n    switch (error.response.status) {\n      case 401:\n        return new Error(ERROR_MESSAGES.UNAUTHORIZED);\n      case 400:\n        return new Error(ERROR_MESSAGES.VALIDATION_ERROR);\n      default:\n        const responseData = error.response.data as { message?: string } | undefined;\n        return new Error(responseData?.message || ERROR_MESSAGES.GENERIC_ERROR);\n    }\n  }\n\n  // Public API methods\n  get instance(): AxiosInstance {\n    return this.client;\n  }\n\n  async get<T>(url: string, config?: Record<string, unknown>): Promise<T> {\n    const response = await this.client.get(url, config);\n    return response.data;\n  }\n\n  async post<T>(url: string, data?: Record<string, unknown>, config?: Record<string, unknown>): Promise<T> {\n    const response = await this.client.post(url, data, config);\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: Record<string, unknown>, config?: Record<string, unknown>): Promise<T> {\n    const response = await this.client.put(url, data, config);\n    return response.data;\n  }\n\n  async delete<T>(url: string, config?: Record<string, unknown>): Promise<T> {\n    const response = await this.client.delete(url, config);\n    return response.data;\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\n\n// Export default for backward compatibility\nexport default apiClient;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAGO,MAAM;IACH,OAAsB;IACtB,QAAkB;IAE1B,YAAY,iBAA2B,wHAAA,CAAA,UAAO,CAAE;QAC9C,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY;QAC/B,IAAI,CAAC,iBAAiB;IACxB;IAEQ,eAA8B;QACpC,OAAO,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAClB,SAAS,0HAAA,CAAA,aAAU,CAAC,QAAQ;YAC5B,SAAS,0HAAA,CAAA,aAAU,CAAC,OAAO;YAC3B,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;IAEQ,oBAA0B;QAChC,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU;YAC1D,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;QAG7C,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,WAAa,UACd,CAAC;YACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,IAAI,CAAC,kBAAkB;YACzB;YACA,OAAO,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;QACzC;IAEJ;IAEQ,qBAA2B;QACjC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU;QAC/C,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEQ,YAAY,KAAiB,EAAS;QAC5C,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,aAAa;QAC/C;QAEA,OAAQ,MAAM,QAAQ,CAAC,MAAM;YAC3B,KAAK;gBACH,OAAO,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,YAAY;YAC9C,KAAK;gBACH,OAAO,IAAI,MAAM,0HAAA,CAAA,iBAAc,CAAC,gBAAgB;YAClD;gBACE,MAAM,eAAe,MAAM,QAAQ,CAAC,IAAI;gBACxC,OAAO,IAAI,MAAM,cAAc,WAAW,0HAAA,CAAA,iBAAc,CAAC,aAAa;QAC1E;IACF;IAEA,qBAAqB;IACrB,IAAI,WAA0B;QAC5B,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,MAAM,IAAO,GAAW,EAAE,MAAgC,EAAc;QACtE,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK;QAC5C,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,KAAQ,GAAW,EAAE,IAA8B,EAAE,MAAgC,EAAc;QACvG,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,IAAO,GAAW,EAAE,IAA8B,EAAE,MAAgC,EAAc;QACtG,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,MAAM;QAClD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAU,GAAW,EAAE,MAAgC,EAAc;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK;QAC/C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY,IAAI;uCAGd", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/authService.ts"], "sourcesContent": ["import { ApiClient } from './apiClient';\nimport { STORAGE_KEYS } from './constants';\nimport { storage } from './storage';\n\n// Types for authentication\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access_token: string;\n  token_type: string;\n}\n\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  full_name?: string;\n  is_active: boolean;\n  twitter_username?: string;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  email: string;\n  password: string;\n  full_name?: string;\n}\n\n// Twitter OAuth 2.0 Types\nexport interface TwitterOAuth2InitRequest {\n  redirect_uri?: string;\n}\n\nexport interface TwitterOAuth2InitResponse {\n  authorization_url: string;\n  state: string;\n  code_verifier: string;\n}\n\nexport interface TwitterOAuth2CallbackRequest {\n  code: string;\n  state: string;\n  code_verifier: string;\n}\n\nexport interface TwitterOAuth2CallbackResponse {\n  access_token: string;\n  token_type: string;\n  user: {\n    id: number;\n    username: string;\n    twitter_username: string;\n    twitter_user_id: string;\n    full_name?: string;\n    is_active: boolean;\n  };\n}\n\n// Authentication Service\nexport class AuthService {\n  constructor(private apiClient: ApiClient) {}\n\n  async login(credentials: LoginRequest): Promise<LoginResponse> {\n    this.validateLoginRequest(credentials);\n\n    // Backend expects form data for OAuth2PasswordRequestForm\n    const formData = new URLSearchParams();\n    formData.append('username', credentials.username);\n    formData.append('password', credentials.password);\n\n    const response = await this.apiClient.instance.post<LoginResponse>('/auth/token', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    });\n\n    // Store the token\n    storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.access_token);\n\n    return response.data;\n  }\n\n  async register(userData: RegisterRequest): Promise<User> {\n    this.validateRegisterRequest(userData);\n    return this.apiClient.post<User>('/users/', userData as unknown as Record<string, unknown>);\n  }\n\n  async getCurrentUser(): Promise<User> {\n    return this.apiClient.get<User>('/auth/me');\n  }\n\n  async logout(): Promise<void> {\n    storage.removeItem(STORAGE_KEYS.AUTH_TOKEN);\n    if (typeof window !== 'undefined') {\n      window.location.href = '/login';\n    }\n  }\n\n  isAuthenticated(): boolean {\n    return !!storage.getItem(STORAGE_KEYS.AUTH_TOKEN);\n  }\n\n  getToken(): string | null {\n    return storage.getItem(STORAGE_KEYS.AUTH_TOKEN);\n  }\n\n  // Twitter OAuth 2.0 Methods\n  async initiateTwitterOAuth2(): Promise<TwitterOAuth2InitResponse> {\n    const response = await this.apiClient.instance.post<TwitterOAuth2InitResponse>(\n      '/auth/oauth2/twitter/init'\n    );\n\n    return response.data;\n  }\n\n  async handleTwitterOAuth2Callback(callbackData: TwitterOAuth2CallbackRequest): Promise<TwitterOAuth2CallbackResponse> {\n    const response = await this.apiClient.instance.post<TwitterOAuth2CallbackResponse>(\n      '/auth/oauth2/twitter/callback',\n      callbackData\n    );\n\n    // Store the JWT token\n    storage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.access_token);\n\n    return response.data;\n  }\n\n  // Validation methods\n  private validateLoginRequest(request: LoginRequest): void {\n    if (!request.username?.trim()) {\n      throw new Error('Username is required');\n    }\n    if (!request.password?.trim()) {\n      throw new Error('Password is required');\n    }\n  }\n\n  private validateRegisterRequest(request: RegisterRequest): void {\n    if (!request.username?.trim()) {\n      throw new Error('Username is required');\n    }\n    if (!request.email?.trim()) {\n      throw new Error('Email is required');\n    }\n    if (!request.password?.trim()) {\n      throw new Error('Password is required');\n    }\n    if (request.password.length < 6) {\n      throw new Error('Password must be at least 6 characters');\n    }\n\n    // Basic email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(request.email)) {\n      throw new Error('Please enter a valid email address');\n    }\n  }\n}\n\n// Export singleton instance\nimport { apiClient } from './apiClient';\nexport const authService = new AuthService(apiClient);\n"], "names": [], "mappings": ";;;;AACA;AACA;AAgKA,4BAA4B;AAC5B;;;AArGO,MAAM;;IACX,YAAY,AAAQ,SAAoB,CAAE;aAAtB,YAAA;IAAuB;IAE3C,MAAM,MAAM,WAAyB,EAA0B;QAC7D,IAAI,CAAC,oBAAoB,CAAC;QAE1B,0DAA0D;QAC1D,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAChD,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAEhD,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAgB,eAAe,UAAU;YAC1F,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,kBAAkB;QAClB,wHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,YAAY;QAEnE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,SAAS,QAAyB,EAAiB;QACvD,IAAI,CAAC,uBAAuB,CAAC;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAO,WAAW;IAC9C;IAEA,MAAM,iBAAgC;QACpC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAO;IAClC;IAEA,MAAM,SAAwB;QAC5B,wHAAA,CAAA,UAAO,CAAC,UAAU,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU;QAC1C,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,kBAA2B;QACzB,OAAO,CAAC,CAAC,wHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU;IAClD;IAEA,WAA0B;QACxB,OAAO,wHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU;IAChD;IAEA,4BAA4B;IAC5B,MAAM,wBAA4D;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CACjD;QAGF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,4BAA4B,YAA0C,EAA0C;QACpH,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CACjD,iCACA;QAGF,sBAAsB;QACtB,wHAAA,CAAA,UAAO,CAAC,OAAO,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,YAAY;QAEnE,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACb,qBAAqB,OAAqB,EAAQ;QACxD,IAAI,CAAC,QAAQ,QAAQ,EAAE,QAAQ;YAC7B,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,QAAQ,QAAQ,EAAE,QAAQ;YAC7B,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,wBAAwB,OAAwB,EAAQ;QAC9D,IAAI,CAAC,QAAQ,QAAQ,EAAE,QAAQ;YAC7B,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,QAAQ,KAAK,EAAE,QAAQ;YAC1B,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,QAAQ,QAAQ,EAAE,QAAQ;YAC7B,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,yBAAyB;QACzB,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,KAAK,GAAG;YACnC,MAAM,IAAI,MAAM;QAClB;IACF;AACF;;AAIO,MAAM,cAAc,IAAI,YAAY,0HAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { authService } from '@/lib/authService';\n\ninterface User {\n  id: number;\n  username: string;\n  twitter_username?: string;\n  twitter_user_id?: string;\n  full_name?: string;\n  is_active: boolean;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (credentials: { username: string; password: string }) => Promise<void>;\n  logout: () => void;\n  refreshUser: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Public routes that don't require authentication\nconst PUBLIC_ROUTES = [\n  '/',\n  '/login',\n  '/register',\n  '/auth/twitter/callback',\n  '/auth/twitter/oauth2-callback',\n  '/test-twitter-oauth2',\n  '/terms',\n  '/privacy',\n  '/about',\n  '/contact'\n];\n\n// Routes that should not trigger redirects (OAuth callbacks)\nconst OAUTH_CALLBACK_ROUTES = [\n  '/auth/twitter/callback',\n  '/auth/twitter/oauth2-callback'\n];\n\n// Routes that should redirect to login if not authenticated\nconst PROTECTED_ROUTES = [\n  '/dashboard',\n  '/content',\n  '/analytics',\n  '/settings',\n  '/profile'\n];\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const isAuthenticated = !!user && !!authService.getToken();\n\n  // Check if current route is public\n  const isPublicRoute = PUBLIC_ROUTES.some(route => {\n    if (route === '/') return pathname === '/';\n    return pathname.startsWith(route);\n  });\n\n  // Check if current route is protected\n  const isProtectedRoute = PROTECTED_ROUTES.some(route => pathname.startsWith(route));\n\n  // Check if current route is an OAuth callback\n  const isOAuthCallback = OAUTH_CALLBACK_ROUTES.some(route => pathname.startsWith(route));\n\n  const refreshUser = async () => {\n    try {\n      console.log('🔄 AuthContext: Starting refreshUser...');\n      const token = authService.getToken();\n      console.log('🔍 AuthContext: Token check:', { hasToken: !!token, tokenLength: token?.length });\n\n      if (!token) {\n        console.log('❌ AuthContext: No token found, setting user to null');\n        setUser(null);\n        return;\n      }\n\n      console.log('🔄 AuthContext: Fetching current user...');\n      const currentUser = await authService.getCurrentUser();\n      console.log('✅ AuthContext: User fetched successfully:', {\n        userId: currentUser.id,\n        username: currentUser.username,\n        twitterUsername: currentUser.twitter_username\n      });\n      setUser(currentUser);\n    } catch (error) {\n      console.error('❌ AuthContext: Failed to refresh user:', error);\n      setUser(null);\n      authService.logout();\n    }\n  };\n\n  const login = async (credentials: { username: string; password: string }) => {\n    try {\n      await authService.login(credentials);\n      // After successful login, fetch user data\n      await refreshUser();\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const logout = () => {\n    authService.logout();\n    setUser(null);\n    router.push('/');\n  };\n\n  // Handle authentication state and routing\n  useEffect(() => {\n    const initAuth = async () => {\n      setIsLoading(true);\n\n      try {\n        const token = authService.getToken();\n\n        if (token) {\n          // Try to get current user\n          await refreshUser();\n        } else {\n          // No token, user is not authenticated\n          setUser(null);\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        authService.logout();\n        setUser(null);\n      } finally {\n        // Add a small delay to prevent flickering\n        setTimeout(() => {\n          setIsLoading(false);\n        }, 200);\n      }\n    };\n\n    initAuth();\n  }, []);\n\n  // Handle route protection with debouncing to prevent flickering\n  useEffect(() => {\n    if (isLoading) return;\n\n    // Don't redirect if we're on an OAuth callback page\n    if (isOAuthCallback) return;\n\n    // Add a small delay to prevent rapid redirects during initialization\n    const timeoutId = setTimeout(() => {\n      // If user is not authenticated and trying to access protected route\n      if (!isAuthenticated && isProtectedRoute) {\n        console.log('Redirecting to login - protected route accessed without auth');\n        router.push('/login');\n        return;\n      }\n\n      // If user is authenticated and trying to access login page\n      if (isAuthenticated && pathname === '/login') {\n        console.log('Redirecting to dashboard - already authenticated');\n        router.push('/dashboard');\n        return;\n      }\n\n      // For any other route that's not explicitly public, redirect to login\n      if (!isAuthenticated && !isPublicRoute) {\n        console.log('Redirecting to login - non-public route accessed without auth');\n        router.push('/login');\n        return;\n      }\n    }, 100); // Small delay to prevent flickering\n\n    return () => clearTimeout(timeoutId);\n  }, [isAuthenticated, isLoading, pathname, isProtectedRoute, isPublicRoute, isOAuthCallback, router]);\n\n  const value: AuthContextType = {\n    user,\n    isLoading,\n    isAuthenticated,\n    login,\n    logout,\n    refreshUser\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n}\n\n// Higher-order component for protecting routes\nexport function withAuth<P extends object>(Component: React.ComponentType<P>) {\n  return function AuthenticatedComponent(props: P) {\n    const { isAuthenticated, isLoading } = useAuth();\n    const router = useRouter();\n\n    useEffect(() => {\n      if (!isLoading && !isAuthenticated) {\n        router.push('/login');\n      }\n    }, [isAuthenticated, isLoading, router]);\n\n    if (isLoading) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n        </div>\n      );\n    }\n\n    if (!isAuthenticated) {\n      return null;\n    }\n\n    return <Component {...props} />;\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAwBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,kDAAkD;AAClD,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,6DAA6D;AAC7D,MAAM,wBAAwB;IAC5B;IACA;CACD;AAED,4DAA4D;AAC5D,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;CACD;AAMM,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,4HAAA,CAAA,cAAW,CAAC,QAAQ;IAExD,mCAAmC;IACnC,MAAM,gBAAgB,cAAc,IAAI,CAAC,CAAA;QACvC,IAAI,UAAU,KAAK,OAAO,aAAa;QACvC,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,sCAAsC;IACtC,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;IAE5E,8CAA8C;IAC9C,MAAM,kBAAkB,sBAAsB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;IAEhF,MAAM,cAAc;QAClB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,QAAQ,4HAAA,CAAA,cAAW,CAAC,QAAQ;YAClC,QAAQ,GAAG,CAAC,gCAAgC;gBAAE,UAAU,CAAC,CAAC;gBAAO,aAAa,OAAO;YAAO;YAE5F,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,QAAQ;gBACR;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,MAAM,cAAc,MAAM,4HAAA,CAAA,cAAW,CAAC,cAAc;YACpD,QAAQ,GAAG,CAAC,6CAA6C;gBACvD,QAAQ,YAAY,EAAE;gBACtB,UAAU,YAAY,QAAQ;gBAC9B,iBAAiB,YAAY,gBAAgB;YAC/C;YACA,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,QAAQ;YACR,4HAAA,CAAA,cAAW,CAAC,MAAM;QACpB;IACF;IAEA,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,4HAAA,CAAA,cAAW,CAAC,KAAK,CAAC;YACxB,0CAA0C;YAC1C,MAAM;QACR,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,4HAAA,CAAA,cAAW,CAAC,MAAM;QAClB,QAAQ;QACR,OAAO,IAAI,CAAC;IACd;IAEA,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;mDAAW;oBACf,aAAa;oBAEb,IAAI;wBACF,MAAM,QAAQ,4HAAA,CAAA,cAAW,CAAC,QAAQ;wBAElC,IAAI,OAAO;4BACT,0BAA0B;4BAC1B,MAAM;wBACR,OAAO;4BACL,sCAAsC;4BACtC,QAAQ;wBACV;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;wBAC5C,4HAAA,CAAA,cAAW,CAAC,MAAM;wBAClB,QAAQ;oBACV,SAAU;wBACR,0CAA0C;wBAC1C;+DAAW;gCACT,aAAa;4BACf;8DAAG;oBACL;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW;YAEf,oDAAoD;YACpD,IAAI,iBAAiB;YAErB,qEAAqE;YACrE,MAAM,YAAY;oDAAW;oBAC3B,oEAAoE;oBACpE,IAAI,CAAC,mBAAmB,kBAAkB;wBACxC,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;oBAEA,2DAA2D;oBAC3D,IAAI,mBAAmB,aAAa,UAAU;wBAC5C,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;oBAEA,sEAAsE;oBACtE,IAAI,CAAC,mBAAmB,CAAC,eAAe;wBACtC,QAAQ,GAAG,CAAC;wBACZ,OAAO,IAAI,CAAC;wBACZ;oBACF;gBACF;mDAAG,MAAM,oCAAoC;YAE7C;0CAAO,IAAM,aAAa;;QAC5B;iCAAG;QAAC;QAAiB;QAAW;QAAU;QAAkB;QAAe;QAAiB;KAAO;IAEnG,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA7IgB;;QAGC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAJd;AA+IT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS,SAA2B,SAAiC;;IAC1E,UAAO,SAAS,uBAAuB,KAAQ;;QAC7C,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG;QACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yDAAE;gBACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;oBAClC,OAAO,IAAI,CAAC;gBACd;YACF;wDAAG;YAAC;YAAiB;YAAW;SAAO;QAEvC,IAAI,WAAW;YACb,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;QAGrB;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO;QACT;QAEA,qBAAO,6LAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;;YAtByC;YACxB,qIAAA,CAAA,YAAS;;;AAsB5B", "debugId": null}}]}