"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[926],{4989:(e,t,r)=>{r.d(t,{Iz:()=>s,Z$:()=>c,zc:()=>n});var a=r(4611);let i={NETWORK:"NETWORK",VALIDATION:"VALIDATION",AUTHENTICATION:"AUTHENTICATION",AUTHORIZATION:"AUTHORIZATION",RATE_LIMIT:"RATE_LIMIT",SERVER:"SERVER",CLIENT:"CLIENT",UNKNOWN:"UNKNOWN"};class n{static handleError(e,t){let r=this.getErrorMessage(e),a=this.getErrorType(e),i=this.getUserFriendlyMessage(a),n={type:a,message:r,userMessage:i,context:t,timestamp:Date.now()};return this.logError(e,t),n}static getErrorMessage(e){return e instanceof Error?e.message:"string"==typeof e?e:e&&"object"==typeof e&&"message"in e?String(e.message):"An unknown error occurred"}static getErrorType(e){let t=this.getErrorMessage(e).toLowerCase();return t.includes("network")||t.includes("fetch")?i.NETWORK:t.includes("validation")||t.includes("invalid")?i.VALIDATION:t.includes("unauthorized")||t.includes("auth")?i.AUTHENTICATION:t.includes("forbidden")?i.AUTHORIZATION:t.includes("too many requests")||t.includes("rate limit")?i.RATE_LIMIT:t.includes("server error")||t.includes("internal")?i.SERVER:t.includes("bad request")||t.includes("client")?i.CLIENT:i.UNKNOWN}static getUserFriendlyMessage(e){switch(e){case i.NETWORK:return"Network error. Please check your connection.";case i.VALIDATION:return"Please check your input and try again.";case i.AUTHENTICATION:return"Please log in to continue.";case i.AUTHORIZATION:return"You do not have permission to perform this action.";case i.RATE_LIMIT:return"Too many requests. Please wait a moment and try again.";case i.SERVER:return"Server error. Please try again later.";case i.CLIENT:return"Invalid request. Please check your input.";default:return"An unexpected error occurred. Please try again."}}static isRetryableError(e){let t=this.getErrorType(e),r=this.getErrorMessage(e).toLowerCase();return!!(t===i.NETWORK||r.includes("timeout")||t===i.SERVER||r.includes("server error"))||t===i.RATE_LIMIT}static logError(e,t){console.error("Error occurred:",{message:this.getErrorMessage(e),context:t,timestamp:new Date().toISOString(),stack:e instanceof Error?e.stack:void 0})}static handleApiError(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{showToast:i=!1,logError:n=!0,fallbackMessage:s=a.UU.GENERIC_ERROR}=r;if("object"==typeof e&&null!==e&&"response"in e){let r=e.response.status,i=e.response.data;t={message:(null==i?void 0:i.message)||(0,a.RW)(r),status:r,code:null==i?void 0:i.code,details:null==i?void 0:i.details}}else if("object"==typeof e&&null!==e&&"request"in e)t={message:a.UU.NETWORK_ERROR,status:0,code:"NETWORK_ERROR"};else t={message:"object"==typeof e&&null!==e&&"message"in e?e.message:s,code:"UNKNOWN_ERROR"};return n&&console.error("API Error:",t,e),t}static handleValidationError(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{logError:i=!0}=r,n={message:Array.isArray(e)?e.join(", "):"object"==typeof e&&null!==e?Object.values(e).join(", "):a.UU.VALIDATION_ERROR,status:a.gx.UNPROCESSABLE_ENTITY,code:"VALIDATION_ERROR",details:"object"==typeof e&&null!==e?e:void 0};return i&&console.warn("Validation Error:",n),n}static async handleAsyncError(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return{data:await e()}}catch(e){return{error:this.handleApiError(e,t)}}}static createErrorBoundaryHandler(e){return(t,r)=>(console.error("Error Boundary caught an error:",t,r),e)}}class s{static handleGenerationError(e){return n.handleApiError(e,{fallbackMessage:a.UU.GENERATION_FAILED,logError:!0})}static handleContentValidationError(e){return n.handleValidationError(e,{logError:!0})}}class o{static handleAuthError(e){let t=n.handleApiError(e,{logError:!0});return t.status===a.gx.UNAUTHORIZED&&localStorage.removeItem("authToken"),t}static handleTokenExpiration(){localStorage.removeItem("authToken")}}class l{static handleNetworkError(e){return n.handleApiError(e,{fallbackMessage:a.UU.NETWORK_ERROR,logError:!0})}static handleRateLimitError(e){return n.handleApiError(e,{fallbackMessage:a.UU.RATE_LIMIT_EXCEEDED,logError:!0})}}class c{static async retryOperation(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3;for(let i=0;i<=r;i++)try{return await e()}catch(n){if(t=n,i===r)throw n;let e=a*Math.pow(2,i);await new Promise(t=>setTimeout(t,e))}throw t}static isRetryableError(e){if(!e.response)return!0;let t=e.response.status;return t>=500||t===a.gx.TOO_MANY_REQUESTS}}s.handleGenerationError,o.handleAuthError,l.handleNetworkError},6801:(e,t,r)=>{r.d(t,{$D:()=>n,ne:()=>i});var a=r(4611);class i{static validateRequired(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This field";return null==e||"string"==typeof e&&!e.trim()||Array.isArray(e)&&0===e.length?t.toLowerCase().includes("required")||t.toLowerCase().includes("error")?{isValid:!1,error:t}:{isValid:!1,error:"".concat(t," is required")}:{isValid:!0}}static validateLength(e,t,r){if("string"!=typeof e)return{isValid:!1,error:"Value must be a string"};let a=e.length;return void 0!==t&&a<t?{isValid:!1,error:"Must be at least ".concat(t," characters long")}:void 0!==r&&a>r?{isValid:!1,error:"Must be no more than ".concat(r," characters long")}:{isValid:!0}}static validateEmail(e){return e?a.WF.EMAIL.test(e)?{isValid:!0}:{isValid:!1,error:"Please enter a valid email address"}:{isValid:!1,error:"Email is required"}}static validatePattern(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Invalid format";return t.test(e)?{isValid:!0}:{isValid:!1,error:r}}static validateRange(e,t,r){return e<t||e>r?{isValid:!1,error:"Must be between ".concat(t," and ").concat(r)}:{isValid:!0}}static validateChoice(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Please select a valid option";return t.includes(e)?{isValid:!0}:{isValid:!1,error:r}}static validateUsername(e){let t=this.validateRequired(e,"Username");if(!t.isValid)return t;let r=this.validateLength(e,3,20);return r.isValid?a.WF.USERNAME.test(e)?{isValid:!0}:{isValid:!1,error:"Username can only contain letters, numbers, and underscores"}:r}static validatePassword(e){let t=this.validateRequired(e,"Password");return t.isValid?e.length<a.gY.MIN_PASSWORD_LENGTH?{isValid:!1,error:"Password must be at least ".concat(a.gY.MIN_PASSWORD_LENGTH," characters long")}:a.WF.PASSWORD.test(e)?{isValid:!0}:{isValid:!1,error:"Password must contain at least one uppercase letter, one lowercase letter, and one number"}:t}}class n{static validateTopic(e){let t=i.validateRequired(e,"Topic");if(!t.isValid)return t;let r=e.length;return r<a.gY.MIN_TOPIC_LENGTH?{isValid:!1,error:"Topic must be at least ".concat(a.gY.MIN_TOPIC_LENGTH," characters long")}:r>a.gY.MAX_TOPIC_LENGTH?{isValid:!1,error:"Topic must be no more than ".concat(a.gY.MAX_TOPIC_LENGTH," characters long")}:{isValid:!0}}static validateContent(e){let t=i.validateRequired(e,"Content");if(!t.isValid)return t;if(e.length>a.Ot.MAX_TWEET_LENGTH)return{isValid:!1,error:"Content must be no more than ".concat(a.Ot.MAX_TWEET_LENGTH," characters long")};let r=[];return n.countHashtags(e)>3&&r.push("Consider using fewer hashtags for better engagement"),n.countMentions(e)>5&&r.push("Too many mentions may reduce visibility"),{isValid:!0,warnings:r.length>0?r:void 0}}static validateStyle(e){return i.validateChoice(e,a.KA.SUPPORTED_STYLES,"Please select a valid style")}static validateLanguage(e){let t=a.KA.SUPPORTED_LANGUAGES.map(e=>e.code);return i.validateChoice(e,t,"Please select a valid language")}static validateThreadSize(e){return e<2?{isValid:!1,error:"Thread must have at least 2 tweets"}:e>a.Ot.MAX_THREAD_TWEETS?{isValid:!1,error:"Thread cannot have more than ".concat(a.Ot.MAX_THREAD_TWEETS," tweets")}:{isValid:!0}}static countHashtags(e){let t=e.match(a.WF.HASHTAG);return t?t.length:0}static countMentions(e){let t=e.match(a.WF.MENTION);return t?t.length:0}static countUrls(e){let t=e.match(a.WF.URL);return t?t.length:0}static getContentStats(e){let t=e.length;return{characterCount:t,wordCount:e.trim()?e.trim().split(/\s+/).length:0,hashtagCount:n.countHashtags(e),mentionCount:n.countMentions(e),urlCount:n.countUrls(e),length:t,remaining:a.Ot.MAX_TWEET_LENGTH-t}}}},7926:(e,t,r)=>{r.d(t,{l:()=>l});var a=r(6801),i=r(4989),n=r(4611),s=r(4205);class o{async generateTweet(e){try{return this.validateTweetRequest(e),await this.apiClient.post("/content/generate-tweet",e)}catch(e){throw i.Iz.handleGenerationError(e)}}async generateThread(e){try{return this.validateThreadRequest(e),await this.apiClient.post("/content/generate-thread",e)}catch(e){throw i.Iz.handleGenerationError(e)}}async generateReply(e){try{return this.validateReplyRequest(e),await this.apiClient.post("/content/generate-reply",e)}catch(e){throw i.Iz.handleGenerationError(e)}}async getContentHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,r=new URLSearchParams({skip:e.toString(),limit:t.toString()});return this.apiClient.get("/content/history?".concat(r))}async generateContent(e){this.validateContentRequest(e);let t={topic:e.topic,style:e.tone,user_context:"Length: ".concat(e.length,", Include hashtags: ").concat(e.includeHashtags,", Include emojis: ").concat(e.includeEmojis)},r=await this.generateTweet(t);return{id:Date.now().toString(),content:r.content||"",hashtags:[],createdAt:new Date().toISOString(),status:"draft"}}validateTweetRequest(e){let t=a.$D.validateTopic(e.topic);if(!t.isValid)throw Error(t.error)}validateThreadRequest(e){let t=a.$D.validateTopic(e.topic);if(!t.isValid)throw Error(t.error);if(e.num_tweets){if(e.num_tweets<2)throw Error("Thread must contain at least 2 tweets");if(e.num_tweets>n.Ot.MAX_THREAD_TWEETS)throw Error("Thread cannot exceed ".concat(n.Ot.MAX_THREAD_TWEETS," tweets"))}}validateReplyRequest(e){let t=a.ne.validateRequired(e.original_tweet,"Original tweet");if(!t.isValid)throw Error(t.error);let r=a.ne.validateLength(e.original_tweet,1,n.Ot.MAX_TWEET_LENGTH);if(!r.isValid)throw Error(r.error)}validateContentRequest(e){let t=a.$D.validateTopic(e.topic);if(!t.isValid)throw Error(t.error)}buildQueryParams(e){if(!e)return"";let t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&t.append(r,String(a))});let r=t.toString();return r?"?".concat(r):""}constructor(e){this.apiClient=e}}let l=new o(s.uE)}}]);