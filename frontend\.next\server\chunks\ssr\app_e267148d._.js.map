{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  href?: string;\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n}\n\nconst Logo = ({ href = '/dashboard', size = 'md', showText = true }: LogoProps) => {\n  const sizes = {\n    sm: { icon: 'w-6 h-6', text: 'text-lg' },\n    md: { icon: 'w-8 h-8', text: 'text-xl' },\n    lg: { icon: 'w-10 h-10', text: 'text-2xl' },\n  };\n\n  const logoContent = (\n    <div className=\"flex items-center space-x-2 select-none\">\n      <div className={`${sizes[size].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`}>\n        <span className={`text-white font-bold ${size === 'sm' ? 'text-sm' : 'text-lg'}`}>A</span>\n      </div>\n      {showText && (\n        <span className={`${sizes[size].text} font-bold text-gray-900 whitespace-nowrap`}>AutoReach</span>\n      )}\n    </div>\n  );\n\n  if (href) {\n    return (\n      <Link href={href}>\n        {logoContent}\n      </Link>\n    );\n  }\n\n  return logoContent;\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,OAAO,CAAC,EAAE,OAAO,YAAY,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI,EAAa;IAC5E,MAAM,QAAQ;QACZ,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAa,MAAM;QAAW;IAC5C;IAEA,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,yEAAyE,CAAC;0BAC5G,cAAA,8OAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,SAAS,OAAO,YAAY,WAAW;8BAAE;;;;;;;;;;;YAEnF,0BACC,8OAAC;gBAAK,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC;0BAAE;;;;;;;;;;;;IAKxF,IAAI,MAAM;QACR,qBACE,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;sBACT;;;;;;IAGP;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useState } from 'react';\nimport { NAVIGATION_ITEMS } from '@/lib/constants';\nimport Logo from '@/components/ui/Logo';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface HeaderProps {\n  showNavigation?: boolean;\n}\n\nconst Header = ({ showNavigation = true }: HeaderProps) => {\n  const pathname = usePathname();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const { user, logout, isAuthenticated } = useAuth();\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 relative z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n          </div>\n\n          {/* Desktop Navigation */}\n          {showNavigation && (\n            <nav className=\"hidden md:flex space-x-6\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.path}\n                  href={item.path}\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    pathname === item.path\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  } ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100' : ''}`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          )}\n\n          {/* Mobile menu button and User menu */}\n          <div className=\"flex items-center space-x-2\">\n            {/* Mobile menu button */}\n            {showNavigation && (\n              <button\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                aria-label=\"Toggle mobile menu\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  {isMobileMenuOpen ? (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  ) : (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  )}\n                </svg>\n              </button>\n            )}\n\n            {/* User menu */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md\"\n                >\n                  <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-purple-600\">\n                      {user?.full_name?.[0] || user?.username?.[0] || 'U'}\n                    </span>\n                  </div>\n                  <span className=\"hidden sm:block text-sm font-medium\">\n                    {user?.full_name || user?.username}\n                  </span>\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* User dropdown menu */}\n                {isUserMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200\">\n                    <div className=\"px-4 py-2 text-sm text-gray-700 border-b border-gray-100\">\n                      <p className=\"font-medium\">{user?.full_name || user?.username}</p>\n                      {user?.twitter_username && (\n                        <p className=\"text-gray-500\">@{user.twitter_username}</p>\n                      )}\n                    </div>\n                    <Link\n                      href=\"/settings\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Settings\n                    </Link>\n                    <button\n                      onClick={() => {\n                        setIsUserMenuOpen(false);\n                        logout();\n                      }}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <Link\n                href=\"/login\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        </div>\n\n        {/* Mobile Navigation Menu */}\n        {showNavigation && isMobileMenuOpen && (\n          <div className=\"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg\">\n            <nav className=\"px-4 py-4 space-y-2\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.path}\n                  href={item.path}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`block px-4 py-3 rounded-lg text-base font-medium transition-colors ${\n                    pathname === item.path\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  } ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100 border border-green-200' : ''}`}\n                >\n                  <span className=\"flex items-center gap-3\">\n                    <span className=\"text-lg\">\n                      {item.name === 'Dashboard' && '📊'}\n                      {item.name === 'Content' && '✍️'}\n                      {item.name === 'Analytics' && '📈'}\n                      {item.name === 'Settings' && '⚙️'}\n                      {item.name === 'Auth' && '🔐'}\n                      {item.name === 'Test API' && '🧪'}\n                    </span>\n                    {item.name}\n                  </span>\n                </Link>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaA,MAAM,SAAS,CAAC,EAAE,iBAAiB,IAAI,EAAe;IACpD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;;;;;;;;;;wBAIN,gCACC,8OAAC;4BAAI,WAAU;sCACZ,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,IAAI,GAClB,mCACA,qDACL,CAAC,EAAE,KAAK,IAAI,KAAK,aAAa,kDAAkD,IAAI;8CAEpF,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAetB,8OAAC;4BAAI,WAAU;;gCAEZ,gCACC,8OAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChE,iCACC,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;iEAErE,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;gCAO5E,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,MAAM,WAAW,CAAC,EAAE,IAAI,MAAM,UAAU,CAAC,EAAE,IAAI;;;;;;;;;;;8DAGpD,8OAAC;oDAAK,WAAU;8DACb,MAAM,aAAa,MAAM;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAKxE,gCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAe,MAAM,aAAa,MAAM;;;;;;wDACpD,MAAM,kCACL,8OAAC;4DAAE,WAAU;;gEAAgB;gEAAE,KAAK,gBAAgB;;;;;;;;;;;;;8DAGxD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;wDACP,kBAAkB;wDAClB;oDACF;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;yDAOP,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;gBAQN,kBAAkB,kCACjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,oBAAoB;gCACnC,WAAW,CAAC,mEAAmE,EAC7E,aAAa,KAAK,IAAI,GAClB,mCACA,qDACL,CAAC,EAAE,KAAK,IAAI,KAAK,aAAa,0EAA0E,IAAI;0CAE7G,cAAA,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAK,WAAU;;gDACb,KAAK,IAAI,KAAK,eAAe;gDAC7B,KAAK,IAAI,KAAK,aAAa;gDAC3B,KAAK,IAAI,KAAK,eAAe;gDAC7B,KAAK,IAAI,KAAK,cAAc;gDAC5B,KAAK,IAAI,KAAK,UAAU;gDACxB,KAAK,IAAI,KAAK,cAAc;;;;;;;wCAE9B,KAAK,IAAI;;;;;;;+BAlBP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BhC;uCAEe", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/validation.ts"], "sourcesContent": ["/**\n * Frontend validation utilities following DRY and KISS principles.\n * Centralizes validation logic to eliminate code duplication.\n */\n\nimport {\n  VALIDATION_RULES,\n  REGEX_PATTERNS,\n  CONTENT_CONSTANTS,\n  TWITTER_CONSTANTS\n} from './constants';\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n}\n\nexport interface FieldValidationResult {\n  isValid: boolean;\n  error?: string;\n  warnings?: string[];\n}\n\n/**\n * Base validation utilities\n */\nexport class ValidationUtils {\n  /**\n   * Validate required field\n   */\n  static validateRequired(value: unknown, fieldName: string = 'This field'): FieldValidationResult {\n    if (value === null || value === undefined) {\n      // If fieldName contains \"is required\" or similar, use it as is\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    if (typeof value === 'string' && !value.trim()) {\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    if (Array.isArray(value) && value.length === 0) {\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate string length\n   */\n  static validateLength(\n    value: string,\n    minLength?: number,\n    maxLength?: number\n  ): FieldValidationResult {\n    if (typeof value !== 'string') {\n      return { isValid: false, error: 'Value must be a string' };\n    }\n\n    const length = value.length;\n\n    if (minLength !== undefined && length < minLength) {\n      return {\n        isValid: false,\n        error: `Must be at least ${minLength} characters long`\n      };\n    }\n\n    if (maxLength !== undefined && length > maxLength) {\n      return {\n        isValid: false,\n        error: `Must be no more than ${maxLength} characters long`\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate email format\n   */\n  static validateEmail(email: string): FieldValidationResult {\n    if (!email) {\n      return { isValid: false, error: 'Email is required' };\n    }\n\n    if (!REGEX_PATTERNS.EMAIL.test(email)) {\n      return { isValid: false, error: 'Please enter a valid email address' };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate pattern match\n   */\n  static validatePattern(value: string, pattern: RegExp, errorMessage: string = 'Invalid format'): FieldValidationResult {\n    if (!pattern.test(value)) {\n      return { isValid: false, error: errorMessage };\n    }\n    return { isValid: true };\n  }\n\n  /**\n   * Validate numeric range\n   */\n  static validateRange(value: number, min: number, max: number): FieldValidationResult {\n    if (value < min || value > max) {\n      return { isValid: false, error: `Must be between ${min} and ${max}` };\n    }\n    return { isValid: true };\n  }\n\n  /**\n   * Validate choice from options\n   */\n  static validateChoice(value: string, choices: readonly string[] | string[], errorMessage: string = 'Please select a valid option'): FieldValidationResult {\n    if (!choices.includes(value)) {\n      return { isValid: false, error: errorMessage };\n    }\n    return { isValid: true };\n  }\n\n\n\n  /**\n   * Validate username\n   */\n  static validateUsername(username: string): FieldValidationResult {\n    const requiredResult = this.validateRequired(username, 'Username');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const lengthResult = this.validateLength(username, 3, 20);\n    if (!lengthResult.isValid) return lengthResult;\n\n    if (!REGEX_PATTERNS.USERNAME.test(username)) {\n      return { isValid: false, error: 'Username can only contain letters, numbers, and underscores' };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate password\n   */\n  static validatePassword(password: string): FieldValidationResult {\n    const requiredResult = this.validateRequired(password, 'Password');\n    if (!requiredResult.isValid) return requiredResult;\n\n    if (password.length < VALIDATION_RULES.MIN_PASSWORD_LENGTH) {\n      return {\n        isValid: false,\n        error: `Password must be at least ${VALIDATION_RULES.MIN_PASSWORD_LENGTH} characters long`\n      };\n    }\n\n    if (!REGEX_PATTERNS.PASSWORD.test(password)) {\n      return {\n        isValid: false,\n        error: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'\n      };\n    }\n\n    return { isValid: true };\n  }\n}\n\n/**\n * Content-specific validation utilities\n */\nexport class ContentValidation {\n  /**\n   * Validate topic for content generation\n   */\n  static validateTopic(topic: string): FieldValidationResult {\n    const requiredResult = ValidationUtils.validateRequired(topic, 'Topic');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const length = topic.length;\n    if (length < VALIDATION_RULES.MIN_TOPIC_LENGTH) {\n      return {\n        isValid: false,\n        error: `Topic must be at least ${VALIDATION_RULES.MIN_TOPIC_LENGTH} characters long`\n      };\n    }\n\n    if (length > VALIDATION_RULES.MAX_TOPIC_LENGTH) {\n      return {\n        isValid: false,\n        error: `Topic must be no more than ${VALIDATION_RULES.MAX_TOPIC_LENGTH} characters long`\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate content for tweets\n   */\n  static validateContent(content: string): FieldValidationResult {\n    const requiredResult = ValidationUtils.validateRequired(content, 'Content');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const length = content.length;\n    if (length > TWITTER_CONSTANTS.MAX_TWEET_LENGTH) {\n      return {\n        isValid: false,\n        error: `Content must be no more than ${TWITTER_CONSTANTS.MAX_TWEET_LENGTH} characters long`\n      };\n    }\n\n    const warnings: string[] = [];\n\n    // Check for too many hashtags\n    const hashtagCount = ContentValidation.countHashtags(content);\n    if (hashtagCount > 3) {\n      warnings.push('Consider using fewer hashtags for better engagement');\n    }\n\n    // Check for too many mentions\n    const mentionCount = ContentValidation.countMentions(content);\n    if (mentionCount > 5) {\n      warnings.push('Too many mentions may reduce visibility');\n    }\n\n    return {\n      isValid: true,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n  }\n\n  /**\n   * Validate content style\n   */\n  static validateStyle(style: string): FieldValidationResult {\n    return ValidationUtils.validateChoice(\n      style,\n      CONTENT_CONSTANTS.SUPPORTED_STYLES,\n      'Please select a valid style'\n    );\n  }\n\n  /**\n   * Validate language\n   */\n  static validateLanguage(language: string): FieldValidationResult {\n    const supportedLanguages = CONTENT_CONSTANTS.SUPPORTED_LANGUAGES.map(lang => lang.code);\n    return ValidationUtils.validateChoice(\n      language,\n      supportedLanguages,\n      'Please select a valid language'\n    );\n  }\n\n  /**\n   * Validate thread size\n   */\n  static validateThreadSize(size: number): FieldValidationResult {\n    if (size < 2) {\n      return { isValid: false, error: 'Thread must have at least 2 tweets' };\n    }\n    if (size > TWITTER_CONSTANTS.MAX_THREAD_TWEETS) {\n      return { isValid: false, error: `Thread cannot have more than ${TWITTER_CONSTANTS.MAX_THREAD_TWEETS} tweets` };\n    }\n    return { isValid: true };\n  }\n\n\n\n  /**\n   * Count hashtags in content\n   */\n  static countHashtags(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.HASHTAG);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Count mentions in content\n   */\n  static countMentions(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.MENTION);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Count URLs in content\n   */\n  static countUrls(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.URL);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Get content statistics\n   */\n  static getContentStats(content: string) {\n    const characterCount = content.length;\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n\n    return {\n      characterCount,\n      wordCount,\n      hashtagCount: ContentValidation.countHashtags(content),\n      mentionCount: ContentValidation.countMentions(content),\n      urlCount: ContentValidation.countUrls(content),\n      // Add properties expected by tests\n      length: characterCount,\n      remaining: TWITTER_CONSTANTS.MAX_TWEET_LENGTH - characterCount,\n    };\n  }\n}\n\n/**\n * Form validation utilities\n */\nexport class FormValidation {\n  /**\n   * Validate multiple fields and return combined result\n   */\n  static validateFields(\n    validations: Array<() => FieldValidationResult>\n  ): ValidationResult {\n    const errors: string[] = [];\n\n    for (const validation of validations) {\n      const result = validation();\n      if (!result.isValid && result.error) {\n        errors.push(result.error);\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n    };\n  }\n\n  /**\n   * Validate form data against schema\n   */\n  static validateFormData<T extends Record<string, unknown>>(\n    data: T,\n    schema: Record<keyof T, (value: unknown) => FieldValidationResult>\n  ): Record<keyof T, string | undefined> {\n    const errors: Record<keyof T, string | undefined> = {} as Record<keyof T, string | undefined>;\n\n    for (const [field, validator] of Object.entries(schema)) {\n      const result = validator(data[field as keyof T]);\n      if (!result.isValid && result.error) {\n        errors[field as keyof T] = result.error;\n      }\n    }\n\n    return errors;\n  }\n\n  /**\n   * Validate single field\n   */\n  static validateField(\n    value: unknown,\n    validator: (value: unknown) => FieldValidationResult\n  ): FieldValidationResult {\n    return validator(value);\n  }\n}\n\n/**\n * Real-time validation hook utilities\n */\nexport class ValidationHooks {\n  /**\n   * Debounced validation for real-time feedback\n   */\n  static createDebouncedValidator(\n    validator: (value: unknown) => FieldValidationResult,\n    delay: number = 300\n  ) {\n    let timeoutId: NodeJS.Timeout;\n\n    return (value: unknown, callback: (result: FieldValidationResult) => void) => {\n      clearTimeout(timeoutId);\n      timeoutId = setTimeout(() => {\n        const result = validator(value);\n        callback(result);\n      }, delay);\n    };\n  }\n}\n\n/**\n * Common validation schemas for forms\n */\nexport const ValidationSchemas = {\n  contentGeneration: {\n    topic: (value: unknown) => ContentValidation.validateTopic(value as string),\n    style: (value: unknown) => ContentValidation.validateStyle(value as string),\n    language: (value: unknown) => ContentValidation.validateLanguage(value as string),\n  },\n\n  userRegistration: {\n    username: (value: unknown) => ValidationUtils.validateUsername(value as string),\n    email: (value: unknown) => ValidationUtils.validateEmail(value as string),\n    password: (value: unknown) => ValidationUtils.validatePassword(value as string),\n  },\n\n  userLogin: {\n    email: (value: unknown) => ValidationUtils.validateEmail(value as string),\n    password: (value: unknown) => ValidationUtils.validateRequired(value, 'Password'),\n  },\n};\n\n/**\n * Utility functions for common validation patterns\n */\nexport const validateContentGenerationForm = (data: {\n  topic: string;\n  style?: string;\n  language?: string;\n}) => {\n  const errors: Record<string, string | undefined> = {};\n\n  const topicValidation = ValidationSchemas.contentGeneration.topic(data.topic);\n  if (!topicValidation.isValid && topicValidation.error) {\n    errors.topic = topicValidation.error;\n  }\n\n  return errors;\n};\n\nexport const validateUserRegistrationForm = (data: {\n  username: string;\n  email: string;\n  password: string;\n}) => {\n  return FormValidation.validateFormData(data, ValidationSchemas.userRegistration);\n};\n\nexport const validateUserLoginForm = (data: {\n  email: string;\n  password: string;\n}) => {\n  return FormValidation.validateFormData(data, ValidationSchemas.userLogin);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;;AAqBO,MAAM;IACX;;GAEC,GACD,OAAO,iBAAiB,KAAc,EAAE,YAAoB,YAAY,EAAyB;QAC/F,IAAI,UAAU,QAAQ,UAAU,WAAW;YACzC,+DAA+D;YAC/D,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,IAAI;YAC9C,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;YAC9C,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,eACL,KAAa,EACb,SAAkB,EAClB,SAAkB,EACK;QACvB,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QAEA,MAAM,SAAS,MAAM,MAAM;QAE3B,IAAI,cAAc,aAAa,SAAS,WAAW;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,iBAAiB,EAAE,UAAU,gBAAgB,CAAC;YACxD;QACF;QAEA,IAAI,cAAc,aAAa,SAAS,WAAW;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,qBAAqB,EAAE,UAAU,gBAAgB,CAAC;YAC5D;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,IAAI,CAAC,OAAO;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoB;QACtD;QAEA,IAAI,CAAC,uHAAA,CAAA,iBAAc,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,gBAAgB,KAAa,EAAE,OAAe,EAAE,eAAuB,gBAAgB,EAAyB;QACrH,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;YACxB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAE,GAAW,EAAE,GAAW,EAAyB;QACnF,IAAI,QAAQ,OAAO,QAAQ,KAAK;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,gBAAgB,EAAE,IAAI,KAAK,EAAE,KAAK;YAAC;QACtE;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,eAAe,KAAa,EAAE,OAAqC,EAAE,eAAuB,8BAA8B,EAAyB;QACxJ,IAAI,CAAC,QAAQ,QAAQ,CAAC,QAAQ;YAC5B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAIA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,iBAAiB,IAAI,CAAC,gBAAgB,CAAC,UAAU;QACvD,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,eAAe,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QACtD,IAAI,CAAC,aAAa,OAAO,EAAE,OAAO;QAElC,IAAI,CAAC,uHAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;YAC3C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA8D;QAChG;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,iBAAiB,IAAI,CAAC,gBAAgB,CAAC,UAAU;QACvD,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,IAAI,SAAS,MAAM,GAAG,uHAAA,CAAA,mBAAgB,CAAC,mBAAmB,EAAE;YAC1D,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,0BAA0B,EAAE,uHAAA,CAAA,mBAAgB,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;YAC5F;QACF;QAEA,IAAI,CAAC,uHAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,MAAM,iBAAiB,gBAAgB,gBAAgB,CAAC,OAAO;QAC/D,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,SAAS,MAAM,MAAM;QAC3B,IAAI,SAAS,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,EAAE;YAC9C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,uBAAuB,EAAE,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YACtF;QACF;QAEA,IAAI,SAAS,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,EAAE;YAC9C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,2BAA2B,EAAE,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YAC1F;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAe,EAAyB;QAC7D,MAAM,iBAAiB,gBAAgB,gBAAgB,CAAC,SAAS;QACjE,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,SAAS,QAAQ,MAAM;QAC7B,IAAI,SAAS,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB,EAAE;YAC/C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,6BAA6B,EAAE,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YAC7F;QACF;QAEA,MAAM,WAAqB,EAAE;QAE7B,8BAA8B;QAC9B,MAAM,eAAe,kBAAkB,aAAa,CAAC;QACrD,IAAI,eAAe,GAAG;YACpB,SAAS,IAAI,CAAC;QAChB;QAEA,8BAA8B;QAC9B,MAAM,eAAe,kBAAkB,aAAa,CAAC;QACrD,IAAI,eAAe,GAAG;YACpB,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO;YACL,SAAS;YACT,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;QAC7C;IACF;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,OAAO,gBAAgB,cAAc,CACnC,OACA,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB,EAClC;IAEJ;IAEA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QACtF,OAAO,gBAAgB,cAAc,CACnC,UACA,oBACA;IAEJ;IAEA;;GAEC,GACD,OAAO,mBAAmB,IAAY,EAAyB;QAC7D,IAAI,OAAO,GAAG;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QACA,IAAI,OAAO,uHAAA,CAAA,oBAAiB,CAAC,iBAAiB,EAAE;YAC9C,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,6BAA6B,EAAE,uHAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAAC;QAC/G;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAIA;;GAEC,GACD,OAAO,cAAc,OAAe,EAAU;QAC5C,MAAM,UAAU,QAAQ,KAAK,CAAC,uHAAA,CAAA,iBAAc,CAAC,OAAO;QACpD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,cAAc,OAAe,EAAU;QAC5C,MAAM,UAAU,QAAQ,KAAK,CAAC,uHAAA,CAAA,iBAAc,CAAC,OAAO;QACpD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,UAAU,OAAe,EAAU;QACxC,MAAM,UAAU,QAAQ,KAAK,CAAC,uHAAA,CAAA,iBAAc,CAAC,GAAG;QAChD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAe,EAAE;QACtC,MAAM,iBAAiB,QAAQ,MAAM;QACrC,MAAM,YAAY,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,GAAG;QAExE,OAAO;YACL;YACA;YACA,cAAc,kBAAkB,aAAa,CAAC;YAC9C,cAAc,kBAAkB,aAAa,CAAC;YAC9C,UAAU,kBAAkB,SAAS,CAAC;YACtC,mCAAmC;YACnC,QAAQ;YACR,WAAW,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB,GAAG;QAClD;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,eACL,WAA+C,EAC7B;QAClB,MAAM,SAAmB,EAAE;QAE3B,KAAK,MAAM,cAAc,YAAa;YACpC,MAAM,SAAS;YACf,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBACnC,OAAO,IAAI,CAAC,OAAO,KAAK;YAC1B;QACF;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;QACF;IACF;IAEA;;GAEC,GACD,OAAO,iBACL,IAAO,EACP,MAAkE,EAC7B;QACrC,MAAM,SAA8C,CAAC;QAErD,KAAK,MAAM,CAAC,OAAO,UAAU,IAAI,OAAO,OAAO,CAAC,QAAS;YACvD,MAAM,SAAS,UAAU,IAAI,CAAC,MAAiB;YAC/C,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBACnC,MAAM,CAAC,MAAiB,GAAG,OAAO,KAAK;YACzC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,cACL,KAAc,EACd,SAAoD,EAC7B;QACvB,OAAO,UAAU;IACnB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,yBACL,SAAoD,EACpD,QAAgB,GAAG,EACnB;QACA,IAAI;QAEJ,OAAO,CAAC,OAAgB;YACtB,aAAa;YACb,YAAY,WAAW;gBACrB,MAAM,SAAS,UAAU;gBACzB,SAAS;YACX,GAAG;QACL;IACF;AACF;AAKO,MAAM,oBAAoB;IAC/B,mBAAmB;QACjB,OAAO,CAAC,QAAmB,kBAAkB,aAAa,CAAC;QAC3D,OAAO,CAAC,QAAmB,kBAAkB,aAAa,CAAC;QAC3D,UAAU,CAAC,QAAmB,kBAAkB,gBAAgB,CAAC;IACnE;IAEA,kBAAkB;QAChB,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC;QAC/D,OAAO,CAAC,QAAmB,gBAAgB,aAAa,CAAC;QACzD,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC;IACjE;IAEA,WAAW;QACT,OAAO,CAAC,QAAmB,gBAAgB,aAAa,CAAC;QACzD,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC,OAAO;IACxE;AACF;AAKO,MAAM,gCAAgC,CAAC;IAK5C,MAAM,SAA6C,CAAC;IAEpD,MAAM,kBAAkB,kBAAkB,iBAAiB,CAAC,KAAK,CAAC,KAAK,KAAK;IAC5E,IAAI,CAAC,gBAAgB,OAAO,IAAI,gBAAgB,KAAK,EAAE;QACrD,OAAO,KAAK,GAAG,gBAAgB,KAAK;IACtC;IAEA,OAAO;AACT;AAEO,MAAM,+BAA+B,CAAC;IAK3C,OAAO,eAAe,gBAAgB,CAAC,MAAM,kBAAkB,gBAAgB;AACjF;AAEO,MAAM,wBAAwB,CAAC;IAIpC,OAAO,eAAe,gBAAgB,CAAC,MAAM,kBAAkB,SAAS;AAC1E", "debugId": null}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/errorHandling.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Frontend error handling utilities following DRY and KISS principles.\n * Centralizes error handling logic to eliminate code duplication.\n */\n\nimport { HTTP_STATUS, ERROR_MESSAGES, getErrorMessageByStatus } from './constants';\n\nexport interface ApiError {\n  message: string;\n  status?: number;\n  code?: string;\n  details?: Record<string, unknown>;\n}\n\nexport interface ErrorHandlerOptions {\n  showToast?: boolean;\n  logError?: boolean;\n  fallbackMessage?: string;\n}\n\nexport interface ErrorResult {\n  type: string;\n  message: string;\n  userMessage: string;\n  context?: any;\n  timestamp: number;\n}\n\n// Error type constants\nexport const ErrorTypes = {\n  NETWORK: 'NETWORK',\n  VALIDATION: 'VALIDATION',\n  AUTHENTICATION: 'AUTHENTICATION',\n  AUTHORIZATION: 'AUTHORIZATION',\n  RATE_LIMIT: 'RATE_LIMIT',\n  SERVER: 'SERVER',\n  CLIENT: 'CLIENT',\n  UNKNOWN: 'UNKNOWN'\n} as const;\n\n/**\n * Base error handling utilities\n */\nexport class ErrorHandler {\n  /**\n   * Main error handler that processes errors and returns structured result\n   */\n  static handleError(error: unknown, context?: any): ErrorResult {\n    const message = this.getErrorMessage(error);\n    const type = this.getErrorType(error);\n    const userMessage = this.getUserFriendlyMessage(type);\n\n    const result: ErrorResult = {\n      type,\n      message,\n      userMessage,\n      context,\n      timestamp: Date.now()\n    };\n\n    this.logError(error, context);\n\n    return result;\n  }\n\n  /**\n   * Get error message from various error types\n   */\n  static getErrorMessage(error: unknown): string {\n    if (error instanceof Error) {\n      return error.message;\n    }\n    if (typeof error === 'string') {\n      return error;\n    }\n    if (error && typeof error === 'object' && 'message' in error) {\n      return String((error as any).message);\n    }\n    return 'An unknown error occurred';\n  }\n\n  /**\n   * Determine error type based on error content\n   */\n  static getErrorType(error: unknown): string {\n    const message = this.getErrorMessage(error).toLowerCase();\n\n    if (message.includes('network') || message.includes('fetch')) {\n      return ErrorTypes.NETWORK;\n    }\n    if (message.includes('validation') || message.includes('invalid')) {\n      return ErrorTypes.VALIDATION;\n    }\n    if (message.includes('unauthorized') || message.includes('auth')) {\n      return ErrorTypes.AUTHENTICATION;\n    }\n    if (message.includes('forbidden')) {\n      return ErrorTypes.AUTHORIZATION;\n    }\n    if (message.includes('too many requests') || message.includes('rate limit')) {\n      return ErrorTypes.RATE_LIMIT;\n    }\n    if (message.includes('server error') || message.includes('internal')) {\n      return ErrorTypes.SERVER;\n    }\n    if (message.includes('bad request') || message.includes('client')) {\n      return ErrorTypes.CLIENT;\n    }\n\n    return ErrorTypes.UNKNOWN;\n  }\n\n  /**\n   * Get user-friendly message for error type\n   */\n  static getUserFriendlyMessage(errorType: string): string {\n    switch (errorType) {\n      case ErrorTypes.NETWORK:\n        return 'Network error. Please check your connection.';\n      case ErrorTypes.VALIDATION:\n        return 'Please check your input and try again.';\n      case ErrorTypes.AUTHENTICATION:\n        return 'Please log in to continue.';\n      case ErrorTypes.AUTHORIZATION:\n        return 'You do not have permission to perform this action.';\n      case ErrorTypes.RATE_LIMIT:\n        return 'Too many requests. Please wait a moment and try again.';\n      case ErrorTypes.SERVER:\n        return 'Server error. Please try again later.';\n      case ErrorTypes.CLIENT:\n        return 'Invalid request. Please check your input.';\n      default:\n        return 'An unexpected error occurred. Please try again.';\n    }\n  }\n\n  /**\n   * Check if error is retryable\n   */\n  static isRetryableError(error: unknown): boolean {\n    const type = this.getErrorType(error);\n    const message = this.getErrorMessage(error).toLowerCase();\n\n    // Network errors are retryable\n    if (type === ErrorTypes.NETWORK) {\n      return true;\n    }\n\n    // Timeout errors are retryable\n    if (message.includes('timeout')) {\n      return true;\n    }\n\n    // Server errors (5xx) are retryable\n    if (type === ErrorTypes.SERVER || message.includes('server error')) {\n      return true;\n    }\n\n    // Rate limit errors are retryable (after delay)\n    if (type === ErrorTypes.RATE_LIMIT) {\n      return true;\n    }\n\n    // Authentication, validation, and client errors are not retryable\n    return false;\n  }\n\n  /**\n   * Log error with context\n   */\n  static logError(error: unknown, context?: any): void {\n    const message = this.getErrorMessage(error);\n    const logData = {\n      message,\n      context,\n      timestamp: new Date().toISOString(),\n      stack: error instanceof Error ? error.stack : undefined\n    };\n\n    console.error('Error occurred:', logData);\n  }\n\n  /**\n   * Handle API errors with consistent formatting\n   */\n  static handleApiError(\n    error: unknown,\n    options: ErrorHandlerOptions = {}\n  ): ApiError {\n    const {\n      showToast = false,\n      logError = true,\n      fallbackMessage = ERROR_MESSAGES.GENERIC_ERROR\n    } = options;\n\n    let apiError: ApiError;\n\n    // Type guard for error objects\n    const isErrorWithResponse = (err: unknown): err is { response: { status: number; data?: { message?: string; code?: string; details?: Record<string, unknown> } } } => {\n      return typeof err === 'object' && err !== null && 'response' in err;\n    };\n\n    const isErrorWithRequest = (err: unknown): err is { request: unknown } => {\n      return typeof err === 'object' && err !== null && 'request' in err;\n    };\n\n    const isErrorWithMessage = (err: unknown): err is { message: string } => {\n      return typeof err === 'object' && err !== null && 'message' in err;\n    };\n\n    if (isErrorWithResponse(error)) {\n      // HTTP error response\n      const status = error.response.status;\n      const data = error.response.data;\n\n      apiError = {\n        message: data?.message || getErrorMessageByStatus(status),\n        status,\n        code: data?.code,\n        details: data?.details\n      };\n    } else if (isErrorWithRequest(error)) {\n      // Network error\n      apiError = {\n        message: ERROR_MESSAGES.NETWORK_ERROR,\n        status: 0,\n        code: 'NETWORK_ERROR'\n      };\n    } else {\n      // Other error\n      apiError = {\n        message: isErrorWithMessage(error) ? error.message : fallbackMessage,\n        code: 'UNKNOWN_ERROR'\n      };\n    }\n\n    if (logError) {\n      console.error('API Error:', apiError, error);\n    }\n\n    if (showToast) {\n      // This would integrate with your toast system\n      // toast.error(apiError.message);\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle validation errors\n   */\n  static handleValidationError(\n    errors: Record<string, string> | string[] | unknown,\n    options: ErrorHandlerOptions = {}\n  ): ApiError {\n    const { logError = true } = options;\n\n    let message: string;\n\n    if (Array.isArray(errors)) {\n      message = errors.join(', ');\n    } else if (typeof errors === 'object' && errors !== null) {\n      message = Object.values(errors).join(', ');\n    } else {\n      message = ERROR_MESSAGES.VALIDATION_ERROR;\n    }\n\n    const apiError: ApiError = {\n      message,\n      status: HTTP_STATUS.UNPROCESSABLE_ENTITY,\n      code: 'VALIDATION_ERROR',\n      details: typeof errors === 'object' && errors !== null ? errors as Record<string, unknown> : undefined\n    };\n\n    if (logError) {\n      console.warn('Validation Error:', apiError);\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle async operation errors\n   */\n  static async handleAsyncError<T>(\n    operation: () => Promise<T>,\n    options: ErrorHandlerOptions = {}\n  ): Promise<{ data?: T; error?: ApiError }> {\n    try {\n      const data = await operation();\n      return { data };\n    } catch (error) {\n      const apiError = this.handleApiError(error, options);\n      return { error: apiError };\n    }\n  }\n\n  /**\n   * Create error boundary handler\n   */\n  static createErrorBoundaryHandler(\n    fallbackComponent?: React.ComponentType<{ error: Error }>\n  ) {\n    return (error: Error, errorInfo: any) => {\n      console.error('Error Boundary caught an error:', error, errorInfo);\n\n      // Log to error reporting service\n      // errorReportingService.captureException(error, errorInfo);\n\n      return fallbackComponent;\n    };\n  }\n}\n\n/**\n * Specific error handlers for different scenarios\n */\nexport class ContentErrorHandler {\n  /**\n   * Handle content generation errors\n   */\n  static handleGenerationError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.GENERATION_FAILED,\n      logError: true\n    });\n  }\n\n  /**\n   * Handle content validation errors\n   */\n  static handleContentValidationError(errors: any): ApiError {\n    return ErrorHandler.handleValidationError(errors, {\n      logError: true\n    });\n  }\n}\n\n/**\n * Authentication error handlers\n */\nexport class AuthErrorHandler {\n  /**\n   * Handle authentication errors\n   */\n  static handleAuthError(error: any): ApiError {\n    const apiError = ErrorHandler.handleApiError(error, {\n      logError: true\n    });\n\n    // Handle specific auth scenarios\n    if (apiError.status === HTTP_STATUS.UNAUTHORIZED) {\n      // Clear auth tokens\n      localStorage.removeItem('authToken');\n\n      // Redirect to login if needed\n      // router.push('/login');\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle token expiration\n   */\n  static handleTokenExpiration(): void {\n    localStorage.removeItem('authToken');\n    // Show token expired message\n    // toast.error(ERROR_MESSAGES.UNAUTHORIZED);\n    // Redirect to login\n    // router.push('/login');\n  }\n}\n\n/**\n * Network error handlers\n */\nexport class NetworkErrorHandler {\n  /**\n   * Handle network connectivity issues\n   */\n  static handleNetworkError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.NETWORK_ERROR,\n      logError: true\n    });\n  }\n\n  /**\n   * Handle rate limiting\n   */\n  static handleRateLimitError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,\n      logError: true\n    });\n  }\n}\n\n/**\n * Error retry utilities\n */\nexport class RetryHandler {\n  /**\n   * Retry failed operations with exponential backoff\n   */\n  static async retryOperation<T>(\n    operation: () => Promise<T>,\n    maxRetries: number = 3,\n    baseDelay: number = 1000\n  ): Promise<T> {\n    let lastError: any;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        return await operation();\n      } catch (error) {\n        lastError = error;\n\n        if (attempt === maxRetries) {\n          throw error;\n        }\n\n        // Exponential backoff\n        const delay = baseDelay * Math.pow(2, attempt);\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n    }\n\n    throw lastError;\n  }\n\n  /**\n   * Check if error is retryable\n   */\n  static isRetryableError(error: any): boolean {\n    if (!error.response) {\n      return true; // Network errors are retryable\n    }\n\n    const status = error.response.status;\n    return status >= 500 || status === HTTP_STATUS.TOO_MANY_REQUESTS;\n  }\n}\n\n/**\n * Error logging utilities\n */\nexport class ErrorLogger {\n  /**\n   * Log error with timestamp and context\n   */\n  static log(error: unknown, context?: any): void {\n    const timestamp = new Date().toISOString();\n    const message = error instanceof Error ? error.message : String(error);\n\n    if (context) {\n      console.error(`[ERROR] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.error(`[ERROR] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log warning with timestamp and context\n   */\n  static warn(message: string, context?: any): void {\n    const timestamp = new Date().toISOString();\n\n    if (context) {\n      console.warn(`[WARN] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.warn(`[WARN] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log info message\n   */\n  static info(message: string, context?: any): void {\n    const timestamp = new Date().toISOString();\n\n    if (context) {\n      console.log(`[INFO] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.log(`[INFO] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log debug message (only in development)\n   */\n  static debug(message: string, context?: any): void {\n    if (process.env.NODE_ENV === 'development') {\n      const timestamp = new Date().toISOString();\n\n      if (context) {\n        console.log(`[DEBUG] ${timestamp}`, message, 'Context:', context);\n      } else {\n        console.log(`[DEBUG] ${timestamp}`, message);\n      }\n    }\n  }\n\n  /**\n   * Log error to console with context (legacy method)\n   */\n  static logError(\n    error: any,\n    context: string,\n    additionalData?: any\n  ): void {\n    console.group(`🚨 Error in ${context}`);\n    console.error('Error:', error);\n    if (additionalData) {\n      console.error('Additional Data:', additionalData);\n    }\n    console.error('Stack:', error.stack);\n    console.groupEnd();\n  }\n\n  /**\n   * Log warning with context (legacy method)\n   */\n  static logWarning(\n    message: string,\n    context: string,\n    additionalData?: any\n  ): void {\n    console.group(`⚠️ Warning in ${context}`);\n    console.warn('Message:', message);\n    if (additionalData) {\n      console.warn('Additional Data:', additionalData);\n    }\n    console.groupEnd();\n  }\n}\n\n/**\n * Utility functions for common error handling patterns\n */\nexport const withErrorHandling = <T extends any[], R>(\n  fn: (...args: T) => Promise<R>,\n  errorHandler?: (error: any) => void\n) => {\n  return async (...args: T): Promise<R | undefined> => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      if (errorHandler) {\n        errorHandler(error);\n      } else {\n        ErrorHandler.handleApiError(error, { logError: true });\n      }\n      return undefined;\n    }\n  };\n};\n\nexport const createAsyncErrorHandler = (\n  defaultErrorMessage: string = ERROR_MESSAGES.GENERIC_ERROR\n) => {\n  return (error: any) => {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: defaultErrorMessage,\n      logError: true\n    });\n  };\n};\n\n// Export commonly used error handlers\nexport const handleContentError = ContentErrorHandler.handleGenerationError;\nexport const handleAuthError = AuthErrorHandler.handleAuthError;\nexport const handleNetworkError = NetworkErrorHandler.handleNetworkError;\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD;;;CAGC;;;;;;;;;;;;;;AAED;;AAwBO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,gBAAgB;IAChB,eAAe;IACf,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,YAAY,KAAc,EAAE,OAAa,EAAe;QAC7D,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC;QACrC,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,MAAM,cAAc,IAAI,CAAC,sBAAsB,CAAC;QAEhD,MAAM,SAAsB;YAC1B;YACA;YACA;YACA;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,IAAI,CAAC,QAAQ,CAAC,OAAO;QAErB,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,gBAAgB,KAAc,EAAU;QAC7C,IAAI,iBAAiB,OAAO;YAC1B,OAAO,MAAM,OAAO;QACtB;QACA,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;QACT;QACA,IAAI,SAAS,OAAO,UAAU,YAAY,aAAa,OAAO;YAC5D,OAAO,OAAO,AAAC,MAAc,OAAO;QACtC;QACA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,aAAa,KAAc,EAAU;QAC1C,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,OAAO,WAAW;QAEvD,IAAI,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,UAAU;YAC5D,OAAO,WAAW,OAAO;QAC3B;QACA,IAAI,QAAQ,QAAQ,CAAC,iBAAiB,QAAQ,QAAQ,CAAC,YAAY;YACjE,OAAO,WAAW,UAAU;QAC9B;QACA,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,SAAS;YAChE,OAAO,WAAW,cAAc;QAClC;QACA,IAAI,QAAQ,QAAQ,CAAC,cAAc;YACjC,OAAO,WAAW,aAAa;QACjC;QACA,IAAI,QAAQ,QAAQ,CAAC,wBAAwB,QAAQ,QAAQ,CAAC,eAAe;YAC3E,OAAO,WAAW,UAAU;QAC9B;QACA,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,aAAa;YACpE,OAAO,WAAW,MAAM;QAC1B;QACA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,WAAW;YACjE,OAAO,WAAW,MAAM;QAC1B;QAEA,OAAO,WAAW,OAAO;IAC3B;IAEA;;GAEC,GACD,OAAO,uBAAuB,SAAiB,EAAU;QACvD,OAAQ;YACN,KAAK,WAAW,OAAO;gBACrB,OAAO;YACT,KAAK,WAAW,UAAU;gBACxB,OAAO;YACT,KAAK,WAAW,cAAc;gBAC5B,OAAO;YACT,KAAK,WAAW,aAAa;gBAC3B,OAAO;YACT,KAAK,WAAW,UAAU;gBACxB,OAAO;YACT,KAAK,WAAW,MAAM;gBACpB,OAAO;YACT,KAAK,WAAW,MAAM;gBACpB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,OAAO,iBAAiB,KAAc,EAAW;QAC/C,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,OAAO,WAAW;QAEvD,+BAA+B;QAC/B,IAAI,SAAS,WAAW,OAAO,EAAE;YAC/B,OAAO;QACT;QAEA,+BAA+B;QAC/B,IAAI,QAAQ,QAAQ,CAAC,YAAY;YAC/B,OAAO;QACT;QAEA,oCAAoC;QACpC,IAAI,SAAS,WAAW,MAAM,IAAI,QAAQ,QAAQ,CAAC,iBAAiB;YAClE,OAAO;QACT;QAEA,gDAAgD;QAChD,IAAI,SAAS,WAAW,UAAU,EAAE;YAClC,OAAO;QACT;QAEA,kEAAkE;QAClE,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,SAAS,KAAc,EAAE,OAAa,EAAQ;QACnD,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC;QACrC,MAAM,UAAU;YACd;YACA;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO,iBAAiB,QAAQ,MAAM,KAAK,GAAG;QAChD;QAEA,QAAQ,KAAK,CAAC,mBAAmB;IACnC;IAEA;;GAEC,GACD,OAAO,eACL,KAAc,EACd,UAA+B,CAAC,CAAC,EACvB;QACV,MAAM,EACJ,YAAY,KAAK,EACjB,WAAW,IAAI,EACf,kBAAkB,uHAAA,CAAA,iBAAc,CAAC,aAAa,EAC/C,GAAG;QAEJ,IAAI;QAEJ,+BAA+B;QAC/B,MAAM,sBAAsB,CAAC;YAC3B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,cAAc;QAClE;QAEA,MAAM,qBAAqB,CAAC;YAC1B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,aAAa;QACjE;QAEA,MAAM,qBAAqB,CAAC;YAC1B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,aAAa;QACjE;QAEA,IAAI,oBAAoB,QAAQ;YAC9B,sBAAsB;YACtB,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;YACpC,MAAM,OAAO,MAAM,QAAQ,CAAC,IAAI;YAEhC,WAAW;gBACT,SAAS,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE;gBAClD;gBACA,MAAM,MAAM;gBACZ,SAAS,MAAM;YACjB;QACF,OAAO,IAAI,mBAAmB,QAAQ;YACpC,gBAAgB;YAChB,WAAW;gBACT,SAAS,uHAAA,CAAA,iBAAc,CAAC,aAAa;gBACrC,QAAQ;gBACR,MAAM;YACR;QACF,OAAO;YACL,cAAc;YACd,WAAW;gBACT,SAAS,mBAAmB,SAAS,MAAM,OAAO,GAAG;gBACrD,MAAM;YACR;QACF;QAEA,IAAI,UAAU;YACZ,QAAQ,KAAK,CAAC,cAAc,UAAU;QACxC;QAEA,IAAI,WAAW;QACb,8CAA8C;QAC9C,iCAAiC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,sBACL,MAAmD,EACnD,UAA+B,CAAC,CAAC,EACvB;QACV,MAAM,EAAE,WAAW,IAAI,EAAE,GAAG;QAE5B,IAAI;QAEJ,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,UAAU,OAAO,IAAI,CAAC;QACxB,OAAO,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;YACxD,UAAU,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC;QACvC,OAAO;YACL,UAAU,uHAAA,CAAA,iBAAc,CAAC,gBAAgB;QAC3C;QAEA,MAAM,WAAqB;YACzB;YACA,QAAQ,uHAAA,CAAA,cAAW,CAAC,oBAAoB;YACxC,MAAM;YACN,SAAS,OAAO,WAAW,YAAY,WAAW,OAAO,SAAoC;QAC/F;QAEA,IAAI,UAAU;YACZ,QAAQ,IAAI,CAAC,qBAAqB;QACpC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,iBACX,SAA2B,EAC3B,UAA+B,CAAC,CAAC,EACQ;QACzC,IAAI;YACF,MAAM,OAAO,MAAM;YACnB,OAAO;gBAAE;YAAK;QAChB,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,OAAO;YAC5C,OAAO;gBAAE,OAAO;YAAS;QAC3B;IACF;IAEA;;GAEC,GACD,OAAO,2BACL,iBAAyD,EACzD;QACA,OAAO,CAAC,OAAc;YACpB,QAAQ,KAAK,CAAC,mCAAmC,OAAO;YAExD,iCAAiC;YACjC,4DAA4D;YAE5D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,sBAAsB,KAAU,EAAY;QACjD,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,uHAAA,CAAA,iBAAc,CAAC,iBAAiB;YACjD,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,6BAA6B,MAAW,EAAY;QACzD,OAAO,aAAa,qBAAqB,CAAC,QAAQ;YAChD,UAAU;QACZ;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,gBAAgB,KAAU,EAAY;QAC3C,MAAM,WAAW,aAAa,cAAc,CAAC,OAAO;YAClD,UAAU;QACZ;QAEA,iCAAiC;QACjC,IAAI,SAAS,MAAM,KAAK,uHAAA,CAAA,cAAW,CAAC,YAAY,EAAE;YAChD,oBAAoB;YACpB,aAAa,UAAU,CAAC;QAExB,8BAA8B;QAC9B,yBAAyB;QAC3B;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,wBAA8B;QACnC,aAAa,UAAU,CAAC;IACxB,6BAA6B;IAC7B,4CAA4C;IAC5C,oBAAoB;IACpB,yBAAyB;IAC3B;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,mBAAmB,KAAU,EAAY;QAC9C,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,uHAAA,CAAA,iBAAc,CAAC,aAAa;YAC7C,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,qBAAqB,KAAU,EAAY;QAChD,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,uHAAA,CAAA,iBAAc,CAAC,mBAAmB;YACnD,UAAU;QACZ;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,aAAa,eACX,SAA2B,EAC3B,aAAqB,CAAC,EACtB,YAAoB,IAAI,EACZ;QACZ,IAAI;QAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,OAAO,MAAM;YACf,EAAE,OAAO,OAAO;gBACd,YAAY;gBAEZ,IAAI,YAAY,YAAY;oBAC1B,MAAM;gBACR;gBAEA,sBAAsB;gBACtB,MAAM,QAAQ,YAAY,KAAK,GAAG,CAAC,GAAG;gBACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,MAAM;IACR;IAEA;;GAEC,GACD,OAAO,iBAAiB,KAAU,EAAW;QAC3C,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO,MAAM,+BAA+B;QAC9C;QAEA,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;QACpC,OAAO,UAAU,OAAO,WAAW,uHAAA,CAAA,cAAW,CAAC,iBAAiB;IAClE;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,IAAI,KAAc,EAAE,OAAa,EAAQ;QAC9C,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QAEhE,IAAI,SAAS;YACX,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,YAAY;QAC7D,OAAO;YACL,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE;QACxC;IACF;IAEA;;GAEC,GACD,OAAO,KAAK,OAAe,EAAE,OAAa,EAAQ;QAChD,MAAM,YAAY,IAAI,OAAO,WAAW;QAExC,IAAI,SAAS;YACX,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,YAAY;QAC3D,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE;QACtC;IACF;IAEA;;GAEC,GACD,OAAO,KAAK,OAAe,EAAE,OAAa,EAAQ;QAChD,MAAM,YAAY,IAAI,OAAO,WAAW;QAExC,IAAI,SAAS;YACX,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,YAAY;QAC1D,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE;QACrC;IACF;IAEA;;GAEC,GACD,OAAO,MAAM,OAAe,EAAE,OAAa,EAAQ;QACjD,wCAA4C;YAC1C,MAAM,YAAY,IAAI,OAAO,WAAW;YAExC,IAAI,SAAS;gBACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,YAAY;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE;YACtC;QACF;IACF;IAEA;;GAEC,GACD,OAAO,SACL,KAAU,EACV,OAAe,EACf,cAAoB,EACd;QACN,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,SAAS;QACtC,QAAQ,KAAK,CAAC,UAAU;QACxB,IAAI,gBAAgB;YAClB,QAAQ,KAAK,CAAC,oBAAoB;QACpC;QACA,QAAQ,KAAK,CAAC,UAAU,MAAM,KAAK;QACnC,QAAQ,QAAQ;IAClB;IAEA;;GAEC,GACD,OAAO,WACL,OAAe,EACf,OAAe,EACf,cAAoB,EACd;QACN,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS;QACxC,QAAQ,IAAI,CAAC,YAAY;QACzB,IAAI,gBAAgB;YAClB,QAAQ,IAAI,CAAC,oBAAoB;QACnC;QACA,QAAQ,QAAQ;IAClB;AACF;AAKO,MAAM,oBAAoB,CAC/B,IACA;IAEA,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,IAAI,cAAc;gBAChB,aAAa;YACf,OAAO;gBACL,aAAa,cAAc,CAAC,OAAO;oBAAE,UAAU;gBAAK;YACtD;YACA,OAAO;QACT;IACF;AACF;AAEO,MAAM,0BAA0B,CACrC,sBAA8B,uHAAA,CAAA,iBAAc,CAAC,aAAa;IAE1D,OAAO,CAAC;QACN,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB;YACjB,UAAU;QACZ;IACF;AACF;AAGO,MAAM,qBAAqB,oBAAoB,qBAAqB;AACpE,MAAM,kBAAkB,iBAAiB,eAAe;AACxD,MAAM,qBAAqB,oBAAoB,kBAAkB", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/contentService.ts"], "sourcesContent": ["import { ApiClient } from './apiClient';\nimport { ContentValidation, ValidationUtils } from './validation';\nimport { ContentErrorHandler } from './errorHandling';\nimport { TWITTER_CONSTANTS } from './constants';\nimport {\n  ContentGenerationRequest,\n  ThreadGenerationRequest,\n  ReplyGenerationRequest,\n  ContentResponse,\n  ContentHistoryResponse,\n} from '../types/api';\n\n// Legacy types for backward compatibility\nexport interface ContentRequest {\n  topic: string;\n  tone: 'professional' | 'casual' | 'humorous' | 'inspirational';\n  length: 'short' | 'medium' | 'long';\n  includeHashtags: boolean;\n  includeEmojis: boolean;\n}\n\nexport interface GeneratedContent {\n  id: string;\n  content: string;\n  hashtags: string[];\n  createdAt: string;\n  status: 'draft' | 'scheduled' | 'published';\n}\n\nexport interface ScheduleRequest {\n  contentId: string;\n  scheduledTime: string;\n}\n\nexport interface ContentFilters {\n  status?: 'draft' | 'scheduled' | 'published';\n  dateFrom?: string;\n  dateTo?: string;\n  limit?: number;\n  offset?: number;\n}\n\n// Content Service following Single Responsibility Principle\nexport class ContentService {\n  constructor(private apiClient: ApiClient) {}\n\n  // New backend API methods with error handling\n  async generateTweet(request: ContentGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateTweetRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-tweet', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async generateThread(request: ThreadGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateThreadRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-thread', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async generateReply(request: ReplyGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateReplyRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-reply', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async getContentHistory(skip: number = 0, limit: number = 50): Promise<ContentHistoryResponse> {\n    const params = new URLSearchParams({\n      skip: skip.toString(),\n      limit: limit.toString()\n    });\n    return this.apiClient.get<ContentHistoryResponse>(`/content/history?${params}`);\n  }\n\n  // Legacy methods for backward compatibility\n  async generateContent(request: ContentRequest): Promise<GeneratedContent> {\n    this.validateContentRequest(request);\n    // Convert legacy request to new format\n    const newRequest: ContentGenerationRequest = {\n      topic: request.topic,\n      style: request.tone,\n      user_context: `Length: ${request.length}, Include hashtags: ${request.includeHashtags}, Include emojis: ${request.includeEmojis}`\n    };\n\n    const response = await this.generateTweet(newRequest);\n\n    // Convert response to legacy format\n    return {\n      id: Date.now().toString(),\n      content: response.content || '',\n      hashtags: [],\n      createdAt: new Date().toISOString(),\n      status: 'draft'\n    };\n  }\n\n  // TODO: Implement these methods when backend endpoints are ready\n  // For now, these are removed to eliminate dead code\n  //\n  // Future implementations:\n  // - getContent(): Use history endpoint with filtering\n  // - getContentById(): Implement backend endpoint for single content retrieval\n  // - updateContent(): Implement backend endpoint for content updates\n  // - scheduleContent(): Use scheduled posts API\n  // - deleteContent(): Implement backend endpoint for content deletion\n  // - publishContent(): Use Twitter posting API\n\n  // Private validation methods using centralized validation utilities\n  private validateTweetRequest(request: ContentGenerationRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n  }\n\n  private validateThreadRequest(request: ThreadGenerationRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n\n    if (request.num_tweets) {\n      if (request.num_tweets < 2) {\n        throw new Error('Thread must contain at least 2 tweets');\n      }\n      if (request.num_tweets > TWITTER_CONSTANTS.MAX_THREAD_TWEETS) {\n        throw new Error(`Thread cannot exceed ${TWITTER_CONSTANTS.MAX_THREAD_TWEETS} tweets`);\n      }\n    }\n  }\n\n  private validateReplyRequest(request: ReplyGenerationRequest): void {\n    const requiredValidation = ValidationUtils.validateRequired(request.original_tweet, 'Original tweet');\n    if (!requiredValidation.isValid) {\n      throw new Error(requiredValidation.error);\n    }\n\n    const lengthValidation = ValidationUtils.validateLength(\n      request.original_tweet,\n      1,\n      TWITTER_CONSTANTS.MAX_TWEET_LENGTH\n    );\n    if (!lengthValidation.isValid) {\n      throw new Error(lengthValidation.error);\n    }\n  }\n\n  private validateContentRequest(request: ContentRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n  }\n\n  // Removed unused validation methods to eliminate dead code\n\n  private buildQueryParams(filters?: ContentFilters): string {\n    if (!filters) return '';\n\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, String(value));\n      }\n    });\n\n    const queryString = params.toString();\n    return queryString ? `?${queryString}` : '';\n  }\n}\n\n// Export singleton instance\nimport { apiClient } from './apiClient';\nexport const contentService = new ContentService(apiClient);\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAgLA,4BAA4B;AAC5B;;;;AAzIO,MAAM;;IACX,YAAY,AAAQ,SAAoB,CAAE;aAAtB,YAAA;IAAuB;IAE3C,8CAA8C;IAC9C,MAAM,cAAc,OAAiC,EAA4B;QAC/E,IAAI;YACF,IAAI,CAAC,oBAAoB,CAAC;YAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,2BAA2B;QAC/E,EAAE,OAAO,OAAO;YACd,MAAM,2HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,eAAe,OAAgC,EAA4B;QAC/E,IAAI;YACF,IAAI,CAAC,qBAAqB,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,4BAA4B;QAChF,EAAE,OAAO,OAAO;YACd,MAAM,2HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,cAAc,OAA+B,EAA4B;QAC7E,IAAI;YACF,IAAI,CAAC,oBAAoB,CAAC;YAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,2BAA2B;QAC/E,EAAE,OAAO,OAAO;YACd,MAAM,2HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,kBAAkB,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAmC;QAC7F,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAyB,CAAC,iBAAiB,EAAE,QAAQ;IAChF;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB,OAAuB,EAA6B;QACxE,IAAI,CAAC,sBAAsB,CAAC;QAC5B,uCAAuC;QACvC,MAAM,aAAuC;YAC3C,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,IAAI;YACnB,cAAc,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,oBAAoB,EAAE,QAAQ,eAAe,CAAC,kBAAkB,EAAE,QAAQ,aAAa,EAAE;QACnI;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;QAE1C,oCAAoC;QACpC,OAAO;YACL,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS,SAAS,OAAO,IAAI;YAC7B,UAAU,EAAE;YACZ,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;QACV;IACF;IAEA,iEAAiE;IACjE,oDAAoD;IACpD,EAAE;IACF,0BAA0B;IAC1B,sDAAsD;IACtD,8EAA8E;IAC9E,oEAAoE;IACpE,+CAA+C;IAC/C,qEAAqE;IACrE,8CAA8C;IAE9C,oEAAoE;IAC5D,qBAAqB,OAAiC,EAAQ;QACpE,MAAM,kBAAkB,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;IACF;IAEQ,sBAAsB,OAAgC,EAAQ;QACpE,MAAM,kBAAkB,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,IAAI,QAAQ,UAAU,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,QAAQ,UAAU,GAAG,uHAAA,CAAA,oBAAiB,CAAC,iBAAiB,EAAE;gBAC5D,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,uHAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACtF;QACF;IACF;IAEQ,qBAAqB,OAA+B,EAAQ;QAClE,MAAM,qBAAqB,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,QAAQ,cAAc,EAAE;QACpF,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC/B,MAAM,IAAI,MAAM,mBAAmB,KAAK;QAC1C;QAEA,MAAM,mBAAmB,wHAAA,CAAA,kBAAe,CAAC,cAAc,CACrD,QAAQ,cAAc,EACtB,GACA,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB;QAEpC,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B,MAAM,IAAI,MAAM,iBAAiB,KAAK;QACxC;IACF;IAEQ,uBAAuB,OAAuB,EAAQ;QAC5D,MAAM,kBAAkB,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;IACF;IAEA,2DAA2D;IAEnD,iBAAiB,OAAwB,EAAU;QACzD,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,OAAO;YAC5B;QACF;QAEA,MAAM,cAAc,OAAO,QAAQ;QACnC,OAAO,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG;IAC3C;AACF;;AAIO,MAAM,iBAAiB,IAAI,eAAe,uHAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/hooks/useErrorHandling.ts"], "sourcesContent": ["/**\n * Custom hooks for error handling following SOLID and KISS principles.\n * Provides consistent error handling across components.\n */\n\nimport { useState, useCallback, useRef } from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Content<PERSON><PERSON>r<PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>rror<PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>pi<PERSON>rror\n} from '@/lib/errorHandling';\nimport { API_CONFIG } from '@/lib/constants';\n\nexport interface ErrorState {\n  error: ApiError | null;\n  isError: boolean;\n  errorId: string | null;\n}\n\nexport interface RetryState {\n  isRetrying: boolean;\n  retryCount: number;\n  maxRetries: number;\n  canRetry: boolean;\n}\n\n/**\n * Hook for general error handling with retry logic\n */\nexport function useErrorHandling(maxRetries: number = API_CONFIG.RETRY_ATTEMPTS) {\n  const [errorState, setErrorState] = useState<ErrorState>({\n    error: null,\n    isError: false,\n    errorId: null\n  });\n\n  const [retryState, setRetryState] = useState<RetryState>({\n    isRetrying: false,\n    retryCount: 0,\n    maxRetries,\n    canRetry: true\n  });\n\n  const errorIdRef = useRef<number>(0);\n\n  // Handle error with optional retry capability\n  const handleError = useCallback((error: unknown, context?: string) => {\n    const apiError = ErrorHandler.handleApiError(error, {\n      logError: true,\n      fallbackMessage: `Error in ${context || 'operation'}`\n    });\n\n    const errorId = `error_${++errorIdRef.current}_${Date.now()}`;\n\n    setErrorState({\n      error: apiError,\n      isError: true,\n      errorId\n    });\n\n    // Determine if error is retryable\n    const canRetry = RetryHandler.isRetryableError(error) && retryState.retryCount < maxRetries;\n\n    setRetryState(prev => ({\n      ...prev,\n      canRetry,\n      isRetrying: false\n    }));\n\n    return apiError;\n  }, [maxRetries, retryState.retryCount]);\n\n  // Clear error state\n  const clearError = useCallback(() => {\n    setErrorState({\n      error: null,\n      isError: false,\n      errorId: null\n    });\n\n    setRetryState(prev => ({\n      ...prev,\n      isRetrying: false,\n      retryCount: 0,\n      canRetry: true\n    }));\n  }, []);\n\n  // Retry failed operation\n  const retry = useCallback(async (operation: () => Promise<unknown>) => {\n    if (!retryState.canRetry || retryState.isRetrying) {\n      return;\n    }\n\n    setRetryState(prev => ({\n      ...prev,\n      isRetrying: true,\n      retryCount: prev.retryCount + 1\n    }));\n\n    try {\n      const result = await RetryHandler.retryOperation(\n        operation,\n        maxRetries - retryState.retryCount,\n        API_CONFIG.RETRY_DELAY\n      );\n\n      clearError();\n      return result;\n    } catch (error) {\n      handleError(error, 'retry operation');\n      throw error;\n    }\n  }, [retryState, maxRetries, handleError, clearError]);\n\n  // Execute operation with error handling\n  const executeWithErrorHandling = useCallback(async <T>(\n    operation: () => Promise<T>,\n    context?: string\n  ): Promise<T | undefined> => {\n    try {\n      clearError();\n      return await operation();\n    } catch (error) {\n      handleError(error, context);\n      return undefined;\n    }\n  }, [handleError, clearError]);\n\n  return {\n    errorState,\n    retryState,\n    handleError,\n    clearError,\n    retry,\n    executeWithErrorHandling\n  };\n}\n\n/**\n * Hook for content-specific error handling\n */\nexport function useContentErrorHandling() {\n  const { handleError: baseHandleError, ...baseHooks } = useErrorHandling();\n\n  const handleContentError = useCallback((error: unknown, operation?: string) => {\n    const apiError = ContentErrorHandler.handleGenerationError(error);\n    return baseHandleError(apiError, `content ${operation || 'operation'}`);\n  }, [baseHandleError]);\n\n  const handleValidationError = useCallback((errors: unknown) => {\n    const apiError = ContentErrorHandler.handleContentValidationError(errors);\n    return baseHandleError(apiError, 'content validation');\n  }, [baseHandleError]);\n\n  return {\n    ...baseHooks,\n    handleContentError,\n    handleValidationError\n  };\n}\n\n/**\n * Hook for authentication error handling\n */\nexport function useAuthErrorHandling() {\n  const { handleError: baseHandleError, ...baseHooks } = useErrorHandling();\n\n  const handleAuthError = useCallback((error: unknown) => {\n    const apiError = AuthErrorHandler.handleAuthError(error);\n    return baseHandleError(apiError, 'authentication');\n  }, [baseHandleError]);\n\n  const handleTokenExpiration = useCallback(() => {\n    AuthErrorHandler.handleTokenExpiration();\n    // Clear error state since token expiration is handled\n    baseHooks.clearError();\n  }, [baseHooks]);\n\n  return {\n    ...baseHooks,\n    handleAuthError,\n    handleTokenExpiration\n  };\n}\n\n/**\n * Hook for network error handling\n */\nexport function useNetworkErrorHandling() {\n  const { handleError: baseHandleError, ...baseHooks } = useErrorHandling();\n\n  const handleNetworkError = useCallback((error: unknown) => {\n    const apiError = NetworkErrorHandler.handleNetworkError(error);\n    return baseHandleError(apiError, 'network operation');\n  }, [baseHandleError]);\n\n  const handleRateLimitError = useCallback((error: unknown) => {\n    const apiError = NetworkErrorHandler.handleRateLimitError(error);\n    return baseHandleError(apiError, 'rate limited operation');\n  }, [baseHandleError]);\n\n  return {\n    ...baseHooks,\n    handleNetworkError,\n    handleRateLimitError\n  };\n}\n\n/**\n * Hook for async operations with built-in error handling\n */\nexport function useAsyncOperation<T = unknown>() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [data, setData] = useState<T | null>(null);\n  const { errorState, handleError, clearError } = useErrorHandling();\n\n  const execute = useCallback(async (\n    operation: () => Promise<T>,\n    options?: {\n      onSuccess?: (data: T) => void;\n      onError?: (error: ApiError) => void;\n      context?: string;\n    }\n  ) => {\n    setIsLoading(true);\n    clearError();\n\n    try {\n      const result = await operation();\n      setData(result);\n      options?.onSuccess?.(result);\n      return result;\n    } catch (error) {\n      const apiError = handleError(error, options?.context);\n      options?.onError?.(apiError);\n      throw apiError;\n    } finally {\n      setIsLoading(false);\n    }\n  }, [handleError, clearError]);\n\n  const reset = useCallback(() => {\n    setData(null);\n    setIsLoading(false);\n    clearError();\n  }, [clearError]);\n\n  return {\n    isLoading,\n    data,\n    errorState,\n    execute,\n    reset\n  };\n}\n\n/**\n * Hook for creating error boundaries\n */\nexport function useErrorBoundary() {\n  const [hasError, setHasError] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n\n  const resetErrorBoundary = useCallback(() => {\n    setHasError(false);\n    setError(null);\n  }, []);\n\n  const captureError = useCallback((error: Error, errorInfo?: unknown) => {\n    setHasError(true);\n    setError(error);\n\n    // Log error for debugging\n    console.error('Error Boundary caught an error:', error, errorInfo);\n\n    // Here you could integrate with error reporting service\n    // errorReportingService.captureException(error, errorInfo);\n  }, []);\n\n  return {\n    hasError,\n    error,\n    resetErrorBoundary,\n    captureError\n  };\n}\n\n/**\n * Hook for managing error state with history\n */\nexport interface ErrorHistoryItem {\n  id: string;\n  message: string;\n  timestamp: number;\n  context?: unknown;\n}\n\nexport interface UseErrorHandlerOptions {\n  maxHistorySize?: number;\n}\n\nexport function useErrorHandler(options: UseErrorHandlerOptions = {}) {\n  const { maxHistorySize = 10 } = options;\n  const [error, setError] = useState<string | null>(null);\n  const [errorHistory, setErrorHistory] = useState<ErrorHistoryItem[]>([]);\n  const errorIdRef = useRef<number>(0);\n\n  const handleError = useCallback((error: unknown, context?: unknown) => {\n    const message = ErrorHandler.getErrorMessage(error);\n    const errorId = `error_${++errorIdRef.current}_${Date.now()}`;\n\n    // Process error through ErrorHandler\n    ErrorHandler.handleError(error, context);\n\n    setError(message);\n\n    const errorItem: ErrorHistoryItem = {\n      id: errorId,\n      message,\n      timestamp: Date.now(),\n      context\n    };\n\n    setErrorHistory(prev => {\n      const newHistory = [errorItem, ...prev];\n      return newHistory.slice(0, maxHistorySize);\n    });\n  }, [maxHistorySize]);\n\n  const clearError = useCallback(() => {\n    setError(null);\n  }, []);\n\n  const clearAllErrors = useCallback(() => {\n    setError(null);\n    setErrorHistory([]);\n  }, []);\n\n  const getErrorById = useCallback((id: string) => {\n    return errorHistory.find(item => item.id === id);\n  }, [errorHistory]);\n\n  return {\n    error,\n    hasError: error !== null,\n    errorHistory,\n    handleError,\n    clearError,\n    clearAllErrors,\n    getErrorById\n  };\n}\n\n/**\n * Hook for retryable operations\n */\nexport interface UseRetryableOperationOptions {\n  maxRetries?: number;\n  retryDelay?: number;\n}\n\nexport function useRetryableOperation<T extends unknown[], R>(\n  operation: (...args: T) => Promise<R>,\n  options: UseRetryableOperationOptions = {}\n) {\n  const { maxRetries = 3, retryDelay = 1000 } = options;\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [retryCount, setRetryCount] = useState(0);\n  const [canRetry, setCanRetry] = useState(true);\n\n  const execute = useCallback(async (...args: T): Promise<R> => {\n    setIsLoading(true);\n    setError(null);\n\n    let currentRetryCount = 0;\n\n    while (currentRetryCount <= maxRetries) {\n      try {\n        const result = await operation(...args);\n        setIsLoading(false);\n        setRetryCount(currentRetryCount);\n        return result;\n      } catch (err) {\n        const isRetryable = ErrorHandler.isRetryableError(err);\n\n        if (!isRetryable || currentRetryCount >= maxRetries) {\n          const message = ErrorHandler.getErrorMessage(err);\n          setError(message);\n          setIsLoading(false);\n          setRetryCount(currentRetryCount);\n          setCanRetry(false);\n          throw err;\n        }\n\n        currentRetryCount++;\n        setRetryCount(currentRetryCount);\n\n        if (currentRetryCount <= maxRetries) {\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n        }\n      }\n    }\n\n    throw new Error('Max retries exceeded');\n  }, [operation, maxRetries, retryDelay]);\n\n  const retry = useCallback(async (...args: T): Promise<R> => {\n    setRetryCount(prev => prev + 1);\n    return execute(...args);\n  }, [execute]);\n\n  const reset = useCallback(() => {\n    setError(null);\n    setRetryCount(0);\n    setCanRetry(true);\n    setIsLoading(false);\n  }, []);\n\n  // Helper methods for testing\n  const setError_ = useCallback((errorMessage: string) => {\n    setError(errorMessage);\n  }, []);\n\n  const setRetryCount_ = useCallback((count: number) => {\n    setRetryCount(count);\n  }, []);\n\n  return {\n    isLoading,\n    error,\n    retryCount,\n    canRetry,\n    execute,\n    retry,\n    reset,\n    // Expose setters for testing\n    setError: setError_,\n    setRetryCount: setRetryCount_\n  };\n}\n\n/**\n * Hook for toast notifications\n */\nexport interface ToastNotification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  message: string;\n  duration?: number;\n}\n\nexport function useToastNotifications() {\n  const [notifications, setNotifications] = useState<ToastNotification[]>([]);\n  const notificationIdRef = useRef<number>(0);\n\n  const removeNotification = useCallback((id: string) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  }, []);\n\n  const addNotification = useCallback((notification: Omit<ToastNotification, 'id'>) => {\n    const id = `toast_${++notificationIdRef.current}_${Date.now()}`;\n    const newNotification: ToastNotification = {\n      id,\n      ...notification\n    };\n\n    setNotifications(prev => [...prev, newNotification]);\n\n    // Auto-remove after duration\n    if (notification.duration) {\n      setTimeout(() => {\n        removeNotification(id);\n      }, notification.duration);\n    }\n  }, [removeNotification]);\n\n  const clearAll = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  return {\n    notifications,\n    addNotification,\n    removeNotification,\n    clearAll\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;AAED;AACA;AAQA;;;;AAkBO,SAAS,iBAAiB,aAAqB,uHAAA,CAAA,aAAU,CAAC,cAAc;IAC7E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,YAAY;QACZ,YAAY;QACZ;QACA,UAAU;IACZ;IAEA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAElC,8CAA8C;IAC9C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAgB;QAC/C,MAAM,WAAW,2HAAA,CAAA,eAAY,CAAC,cAAc,CAAC,OAAO;YAClD,UAAU;YACV,iBAAiB,CAAC,SAAS,EAAE,WAAW,aAAa;QACvD;QAEA,MAAM,UAAU,CAAC,MAAM,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;QAE7D,cAAc;YACZ,OAAO;YACP,SAAS;YACT;QACF;QAEA,kCAAkC;QAClC,MAAM,WAAW,2HAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,UAAU,WAAW,UAAU,GAAG;QAEjF,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP;gBACA,YAAY;YACd,CAAC;QAED,OAAO;IACT,GAAG;QAAC;QAAY,WAAW,UAAU;KAAC;IAEtC,oBAAoB;IACpB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,cAAc;YACZ,OAAO;YACP,SAAS;YACT,SAAS;QACX;QAEA,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,YAAY;gBACZ,YAAY;gBACZ,UAAU;YACZ,CAAC;IACH,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC/B,IAAI,CAAC,WAAW,QAAQ,IAAI,WAAW,UAAU,EAAE;YACjD;QACF;QAEA,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,YAAY;gBACZ,YAAY,KAAK,UAAU,GAAG;YAChC,CAAC;QAED,IAAI;YACF,MAAM,SAAS,MAAM,2HAAA,CAAA,eAAY,CAAC,cAAc,CAC9C,WACA,aAAa,WAAW,UAAU,EAClC,uHAAA,CAAA,aAAU,CAAC,WAAW;YAGxB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,YAAY,OAAO;YACnB,MAAM;QACR;IACF,GAAG;QAAC;QAAY;QAAY;QAAa;KAAW;IAEpD,wCAAwC;IACxC,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC3C,WACA;QAEA,IAAI;YACF;YACA,OAAO,MAAM;QACf,EAAE,OAAO,OAAO;YACd,YAAY,OAAO;YACnB,OAAO;QACT;IACF,GAAG;QAAC;QAAa;KAAW;IAE5B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,aAAa,eAAe,EAAE,GAAG,WAAW,GAAG;IAEvD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAgB;QACtD,MAAM,WAAW,2HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAC3D,OAAO,gBAAgB,UAAU,CAAC,QAAQ,EAAE,aAAa,aAAa;IACxE,GAAG;QAAC;KAAgB;IAEpB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,MAAM,WAAW,2HAAA,CAAA,sBAAmB,CAAC,4BAA4B,CAAC;QAClE,OAAO,gBAAgB,UAAU;IACnC,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,GAAG,SAAS;QACZ;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,aAAa,eAAe,EAAE,GAAG,WAAW,GAAG;IAEvD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,WAAW,2HAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;QAClD,OAAO,gBAAgB,UAAU;IACnC,GAAG;QAAC;KAAgB;IAEpB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,2HAAA,CAAA,mBAAgB,CAAC,qBAAqB;QACtC,sDAAsD;QACtD,UAAU,UAAU;IACtB,GAAG;QAAC;KAAU;IAEd,OAAO;QACL,GAAG,SAAS;QACZ;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,EAAE,aAAa,eAAe,EAAE,GAAG,WAAW,GAAG;IAEvD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,WAAW,2HAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC;QACxD,OAAO,gBAAgB,UAAU;IACnC,GAAG;QAAC;KAAgB;IAEpB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,WAAW,2HAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC;QAC1D,OAAO,gBAAgB,UAAU;IACnC,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,GAAG,SAAS;QACZ;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG;IAEhD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC1B,WACA;QAMA,aAAa;QACb;QAEA,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,QAAQ;YACR,SAAS,YAAY;YACrB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,YAAY,OAAO,SAAS;YAC7C,SAAS,UAAU;YACnB,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAa;KAAW;IAE5B,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,QAAQ;QACR,aAAa;QACb;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,YAAY;QACZ,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAc;QAC9C,YAAY;QACZ,SAAS;QAET,0BAA0B;QAC1B,QAAQ,KAAK,CAAC,mCAAmC,OAAO;IAExD,wDAAwD;IACxD,4DAA4D;IAC9D,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAgBO,SAAS,gBAAgB,UAAkC,CAAC,CAAC;IAClE,MAAM,EAAE,iBAAiB,EAAE,EAAE,GAAG;IAChC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACvE,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAElC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAgB;QAC/C,MAAM,UAAU,2HAAA,CAAA,eAAY,CAAC,eAAe,CAAC;QAC7C,MAAM,UAAU,CAAC,MAAM,EAAE,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;QAE7D,qCAAqC;QACrC,2HAAA,CAAA,eAAY,CAAC,WAAW,CAAC,OAAO;QAEhC,SAAS;QAET,MAAM,YAA8B;YAClC,IAAI;YACJ;YACA,WAAW,KAAK,GAAG;YACnB;QACF;QAEA,gBAAgB,CAAA;YACd,MAAM,aAAa;gBAAC;mBAAc;aAAK;YACvC,OAAO,WAAW,KAAK,CAAC,GAAG;QAC7B;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,SAAS;QACT,gBAAgB,EAAE;IACpB,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,OAAO,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC/C,GAAG;QAAC;KAAa;IAEjB,OAAO;QACL;QACA,UAAU,UAAU;QACpB;QACA;QACA;QACA;QACA;IACF;AACF;AAUO,SAAS,sBACd,SAAqC,EACrC,UAAwC,CAAC,CAAC;IAE1C,MAAM,EAAE,aAAa,CAAC,EAAE,aAAa,IAAI,EAAE,GAAG;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,GAAG;QACpC,aAAa;QACb,SAAS;QAET,IAAI,oBAAoB;QAExB,MAAO,qBAAqB,WAAY;YACtC,IAAI;gBACF,MAAM,SAAS,MAAM,aAAa;gBAClC,aAAa;gBACb,cAAc;gBACd,OAAO;YACT,EAAE,OAAO,KAAK;gBACZ,MAAM,cAAc,2HAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC;gBAElD,IAAI,CAAC,eAAe,qBAAqB,YAAY;oBACnD,MAAM,UAAU,2HAAA,CAAA,eAAY,CAAC,eAAe,CAAC;oBAC7C,SAAS;oBACT,aAAa;oBACb,cAAc;oBACd,YAAY;oBACZ,MAAM;gBACR;gBAEA;gBACA,cAAc;gBAEd,IAAI,qBAAqB,YAAY;oBACnC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF;QACF;QAEA,MAAM,IAAI,MAAM;IAClB,GAAG;QAAC;QAAW;QAAY;KAAW;IAEtC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,GAAG;QAClC,cAAc,CAAA,OAAQ,OAAO;QAC7B,OAAO,WAAW;IACpB,GAAG;QAAC;KAAQ;IAEZ,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,SAAS;QACT,cAAc;QACd,YAAY;QACZ,aAAa;IACf,GAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,SAAS;IACX,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,cAAc;IAChB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA,6BAA6B;QAC7B,UAAU;QACV,eAAe;IACjB;AACF;AAYO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC1E,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAEzC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;IAC3E,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,KAAK,CAAC,MAAM,EAAE,EAAE,kBAAkB,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;QAC/D,MAAM,kBAAqC;YACzC;YACA,GAAG,YAAY;QACjB;QAEA,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAgB;QAEnD,6BAA6B;QAC7B,IAAI,aAAa,QAAQ,EAAE;YACzB,WAAW;gBACT,mBAAmB;YACrB,GAAG,aAAa,QAAQ;QAC1B;IACF,GAAG;QAAC;KAAmB;IAEvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,iBAAiB,EAAE;IACrB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/hooks/useValidation.ts"], "sourcesContent": ["/**\n * Custom hooks for validation following SOLID and KISS principles.\n * Provides reusable validation logic across components.\n */\n\nimport { useState, useCallback, useMemo } from 'react';\nimport {\n  ContentValidation,\n  FormValidation,\n  FieldValidationResult,\n  ValidationSchemas\n} from '@/lib/validation';\nimport { UI_CONSTANTS } from '@/lib/constants';\n\nexport interface UseValidationOptions {\n  debounceDelay?: number;\n  validateOnChange?: boolean;\n  validateOnBlur?: boolean;\n}\n\nexport interface ValidationState {\n  errors: Record<string, string>;\n  isValid: boolean;\n  isValidating: boolean;\n  hasValidated: boolean;\n}\n\n/**\n * Hook for form validation with real-time feedback\n */\nexport function useFormValidation<T extends Record<string, unknown>>(\n  initialData: T,\n  validationSchema: Record<keyof T, (value: unknown) => FieldValidationResult>,\n  options: UseValidationOptions = {}\n) {\n  const {\n    debounceDelay = UI_CONSTANTS.DEBOUNCE_DELAY,\n    validateOnChange = true,\n    validateOnBlur = true\n  } = options;\n\n  const [data, setData] = useState<T>(initialData);\n  const [validationState, setValidationState] = useState<ValidationState>({\n    errors: {},\n    isValid: true,\n    isValidating: false,\n    hasValidated: false\n  });\n\n  // Debounced validation function\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  const debouncedValidate = useCallback(\n    (() => {\n      let timeoutId: NodeJS.Timeout;\n      return (dataToValidate: T) => {\n        clearTimeout(timeoutId);\n        timeoutId = setTimeout(() => {\n          setValidationState(prev => ({ ...prev, isValidating: true }));\n\n          const errors = FormValidation.validateFormData(dataToValidate, validationSchema);\n          const hasErrors = Object.values(errors).some(error => error !== undefined);\n\n          setValidationState({\n            errors: errors as Record<string, string>,\n            isValid: !hasErrors,\n            isValidating: false,\n            hasValidated: true\n          });\n        }, debounceDelay);\n      };\n    })(),\n    [validationSchema, debounceDelay]\n  );\n\n  // Validate specific field\n  const validateField = useCallback(\n    (fieldName: keyof T, value: unknown) => {\n      const validator = validationSchema[fieldName];\n      if (!validator) return;\n\n      const result = validator(value);\n\n      setValidationState(prev => ({\n        ...prev,\n        errors: {\n          ...prev.errors,\n          [fieldName]: result.isValid ? '' : (result.error || '')\n        },\n        hasValidated: true\n      }));\n    },\n    [validationSchema]\n  );\n\n  // Update field value\n  const updateField = useCallback(\n    (fieldName: keyof T, value: unknown) => {\n      const newData = { ...data, [fieldName]: value };\n      setData(newData);\n\n      if (validateOnChange) {\n        if (debounceDelay > 0) {\n          debouncedValidate(newData);\n        } else {\n          validateField(fieldName, value);\n        }\n      }\n    },\n    [data, validateOnChange, debounceDelay, debouncedValidate, validateField]\n  );\n\n  // Handle field blur\n  const handleFieldBlur = useCallback(\n    (fieldName: keyof T) => {\n      if (validateOnBlur) {\n        validateField(fieldName, data[fieldName]);\n      }\n    },\n    [validateOnBlur, validateField, data]\n  );\n\n  // Validate all fields\n  const validateAll = useCallback(() => {\n    const errors = FormValidation.validateFormData(data, validationSchema);\n    const hasErrors = Object.values(errors).some(error => error !== undefined);\n\n    setValidationState({\n      errors: errors as Record<string, string>,\n      isValid: !hasErrors,\n      isValidating: false,\n      hasValidated: true\n    });\n\n    return !hasErrors;\n  }, [data, validationSchema]);\n\n  // Reset validation state\n  const resetValidation = useCallback(() => {\n    setValidationState({\n      errors: {},\n      isValid: true,\n      isValidating: false,\n      hasValidated: false\n    });\n  }, []);\n\n  // Reset form data\n  const resetForm = useCallback(() => {\n    setData(initialData);\n    resetValidation();\n  }, [initialData, resetValidation]);\n\n  return {\n    data,\n    validationState,\n    updateField,\n    handleFieldBlur,\n    validateField,\n    validateAll,\n    resetValidation,\n    resetForm,\n    setData\n  };\n}\n\n/**\n * Hook for content validation (tweets, threads, etc.)\n */\nexport function useContentValidation() {\n  const [validationResults, setValidationResults] = useState<Record<string, FieldValidationResult>>({});\n\n  const validateTopic = useCallback((topic: string) => {\n    const result = ContentValidation.validateTopic(topic);\n    setValidationResults(prev => ({ ...prev, topic: result }));\n    return result;\n  }, []);\n\n  const validateContent = useCallback((content: string) => {\n    const result = ContentValidation.validateContent(content);\n    setValidationResults(prev => ({ ...prev, content: result }));\n    return result;\n  }, []);\n\n  const getContentStats = useCallback((content: string) => {\n    return ContentValidation.getContentStats(content);\n  }, []);\n\n  const clearValidation = useCallback((field?: string) => {\n    if (field) {\n      setValidationResults(prev => {\n        const newResults = { ...prev };\n        delete newResults[field];\n        return newResults;\n      });\n    } else {\n      setValidationResults({});\n    }\n  }, []);\n\n  return {\n    validationResults,\n    validateTopic,\n    validateContent,\n    getContentStats,\n    clearValidation\n  };\n}\n\n/**\n * Hook for real-time field validation\n */\nexport function useFieldValidation(\n  validator: (value: unknown) => FieldValidationResult,\n  debounceDelay: number = UI_CONSTANTS.DEBOUNCE_DELAY\n) {\n  const [result, setResult] = useState<FieldValidationResult>({ isValid: true });\n  const [isValidating, setIsValidating] = useState(false);\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  const debouncedValidate = useCallback(\n    (() => {\n      let timeoutId: NodeJS.Timeout;\n      return (value: unknown) => {\n        clearTimeout(timeoutId);\n        timeoutId = setTimeout(() => {\n          setIsValidating(true);\n          const validationResult = validator(value);\n          setResult(validationResult);\n          setIsValidating(false);\n        }, debounceDelay);\n      };\n    })(),\n    [validator, debounceDelay]\n  );\n\n  const validate = useCallback((value: unknown) => {\n    if (debounceDelay > 0) {\n      debouncedValidate(value);\n    } else {\n      const validationResult = validator(value);\n      setResult(validationResult);\n    }\n  }, [validator, debounceDelay, debouncedValidate]);\n\n  const clearValidation = useCallback(() => {\n    setResult({ isValid: true });\n    setIsValidating(false);\n  }, []);\n\n  return {\n    result,\n    isValidating,\n    validate,\n    clearValidation\n  };\n}\n\n/**\n * Hook for common validation schemas\n */\nexport function useValidationSchemas() {\n  return useMemo(() => ({\n    contentGeneration: ValidationSchemas.contentGeneration,\n    userRegistration: ValidationSchemas.userRegistration,\n    userLogin: ValidationSchemas.userLogin\n  }), []);\n}\n\n\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AACA;AAMA;;;;AAkBO,SAAS,kBACd,WAAc,EACd,gBAA4E,EAC5E,UAAgC,CAAC,CAAC;IAElC,MAAM,EACJ,gBAAgB,uHAAA,CAAA,eAAY,CAAC,cAAc,EAC3C,mBAAmB,IAAI,EACvB,iBAAiB,IAAI,EACtB,GAAG;IAEJ,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAK;IACpC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACtE,QAAQ,CAAC;QACT,SAAS;QACT,cAAc;QACd,cAAc;IAChB;IAEA,gCAAgC;IAChC,uDAAuD;IACvD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI;QACJ,OAAO,CAAC;YACN,aAAa;YACb,YAAY,WAAW;gBACrB,mBAAmB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,cAAc;oBAAK,CAAC;gBAE3D,MAAM,SAAS,wHAAA,CAAA,iBAAc,CAAC,gBAAgB,CAAC,gBAAgB;gBAC/D,MAAM,YAAY,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAA,QAAS,UAAU;gBAEhE,mBAAmB;oBACjB,QAAQ;oBACR,SAAS,CAAC;oBACV,cAAc;oBACd,cAAc;gBAChB;YACF,GAAG;QACL;IACF,CAAC,KACD;QAAC;QAAkB;KAAc;IAGnC,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,WAAoB;QACnB,MAAM,YAAY,gBAAgB,CAAC,UAAU;QAC7C,IAAI,CAAC,WAAW;QAEhB,MAAM,SAAS,UAAU;QAEzB,mBAAmB,CAAA,OAAQ,CAAC;gBAC1B,GAAG,IAAI;gBACP,QAAQ;oBACN,GAAG,KAAK,MAAM;oBACd,CAAC,UAAU,EAAE,OAAO,OAAO,GAAG,KAAM,OAAO,KAAK,IAAI;gBACtD;gBACA,cAAc;YAChB,CAAC;IACH,GACA;QAAC;KAAiB;IAGpB,qBAAqB;IACrB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC,WAAoB;QACnB,MAAM,UAAU;YAAE,GAAG,IAAI;YAAE,CAAC,UAAU,EAAE;QAAM;QAC9C,QAAQ;QAER,IAAI,kBAAkB;YACpB,IAAI,gBAAgB,GAAG;gBACrB,kBAAkB;YACpB,OAAO;gBACL,cAAc,WAAW;YAC3B;QACF;IACF,GACA;QAAC;QAAM;QAAkB;QAAe;QAAmB;KAAc;IAG3E,oBAAoB;IACpB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC;QACC,IAAI,gBAAgB;YAClB,cAAc,WAAW,IAAI,CAAC,UAAU;QAC1C;IACF,GACA;QAAC;QAAgB;QAAe;KAAK;IAGvC,sBAAsB;IACtB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,MAAM,SAAS,wHAAA,CAAA,iBAAc,CAAC,gBAAgB,CAAC,MAAM;QACrD,MAAM,YAAY,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAA,QAAS,UAAU;QAEhE,mBAAmB;YACjB,QAAQ;YACR,SAAS,CAAC;YACV,cAAc;YACd,cAAc;QAChB;QAEA,OAAO,CAAC;IACV,GAAG;QAAC;QAAM;KAAiB;IAE3B,yBAAyB;IACzB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,mBAAmB;YACjB,QAAQ,CAAC;YACT,SAAS;YACT,cAAc;YACd,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,QAAQ;QACR;IACF,GAAG;QAAC;QAAa;KAAgB;IAEjC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS;IACd,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyC,CAAC;IAEnG,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,SAAS,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC;QAC/C,qBAAqB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,OAAO;YAAO,CAAC;QACxD,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,SAAS,wHAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC;QACjD,qBAAqB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAO,CAAC;QAC1D,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,OAAO,wHAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC;IAC3C,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,OAAO;YACT,qBAAqB,CAAA;gBACnB,MAAM,aAAa;oBAAE,GAAG,IAAI;gBAAC;gBAC7B,OAAO,UAAU,CAAC,MAAM;gBACxB,OAAO;YACT;QACF,OAAO;YACL,qBAAqB,CAAC;QACxB;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,mBACd,SAAoD,EACpD,gBAAwB,uHAAA,CAAA,eAAY,CAAC,cAAc;IAEnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;QAAE,SAAS;IAAK;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,uDAAuD;IACvD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI;QACJ,OAAO,CAAC;YACN,aAAa;YACb,YAAY,WAAW;gBACrB,gBAAgB;gBAChB,MAAM,mBAAmB,UAAU;gBACnC,UAAU;gBACV,gBAAgB;YAClB,GAAG;QACL;IACF,CAAC,KACD;QAAC;QAAW;KAAc;IAG5B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,IAAI,gBAAgB,GAAG;YACrB,kBAAkB;QACpB,OAAO;YACL,MAAM,mBAAmB,UAAU;YACnC,UAAU;QACZ;IACF,GAAG;QAAC;QAAW;QAAe;KAAkB;IAEhD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,UAAU;YAAE,SAAS;QAAK;QAC1B,gBAAgB;IAClB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS;IACd,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACpB,mBAAmB,wHAAA,CAAA,oBAAiB,CAAC,iBAAiB;YACtD,kBAAkB,wHAAA,CAAA,oBAAiB,CAAC,gBAAgB;YACpD,WAAW,wHAAA,CAAA,oBAAiB,CAAC,SAAS;QACxC,CAAC,GAAG,EAAE;AACR", "debugId": null}}, {"offset": {"line": 1979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/hooks/useContentGeneration.ts"], "sourcesContent": ["/**\n * Enhanced content generation hook following SOLID and KISS principles.\n * Integrates validation and error handling for better user experience.\n */\n\nimport { useState, useCallback } from 'react';\nimport { contentService, ContentRequest, GeneratedContent } from '@/lib/contentService';\nimport { useContentErrorHandling } from './useErrorHandling';\nimport { useContentValidation } from './useValidation';\n\nexport interface UseContentGenerationState {\n  isLoading: boolean;\n  content: GeneratedContent | null;\n  isValidating: boolean;\n  validationErrors: Record<string, string>;\n}\n\nexport interface ContentGenerationOptions {\n  validateBeforeGeneration?: boolean;\n  onSuccess?: (content: GeneratedContent) => void;\n  onError?: (error: unknown) => void;\n}\n\nexport function useContentGeneration(options: ContentGenerationOptions = {}) {\n  const {\n    validateBeforeGeneration = true,\n    onSuccess,\n    onError\n  } = options;\n\n  const [state, setState] = useState<UseContentGenerationState>({\n    isLoading: false,\n    content: null,\n    isValidating: false,\n    validationErrors: {}\n  });\n\n  const {\n    errorState,\n    handleContentError,\n    handleValidationError,\n    clearError\n  } = useContentErrorHandling();\n\n  const {\n    validateTopic,\n    getContentStats,\n    clearValidation\n  } = useContentValidation();\n\n  // Validate content request\n  const validateRequest = useCallback((request: ContentRequest): boolean => {\n    setState(prev => ({ ...prev, isValidating: true, validationErrors: {} }));\n\n    const errors: Record<string, string> = {};\n\n    // Validate topic\n    const topicResult = validateTopic(request.topic);\n    if (!topicResult.isValid && topicResult.error) {\n      errors.topic = topicResult.error;\n    }\n\n    // Validate tone if provided\n    if (request.tone && !['professional', 'casual', 'humorous', 'inspirational'].includes(request.tone)) {\n      errors.tone = 'Invalid content tone';\n    }\n\n    // Validate length if provided\n    if (request.length && !['short', 'medium', 'long'].includes(request.length)) {\n      errors.length = 'Invalid content length';\n    }\n\n    const hasErrors = Object.keys(errors).length > 0;\n\n    setState(prev => ({\n      ...prev,\n      isValidating: false,\n      validationErrors: errors\n    }));\n\n    if (hasErrors) {\n      handleValidationError(errors);\n    }\n\n    return !hasErrors;\n  }, [validateTopic, handleValidationError]);\n\n  // Generate content with validation and error handling\n  const generateContent = useCallback(async (request: ContentRequest): Promise<GeneratedContent | undefined> => {\n    // Clear previous errors\n    clearError();\n    clearValidation();\n\n    // Validate request if enabled\n    if (validateBeforeGeneration && !validateRequest(request)) {\n      return undefined;\n    }\n\n    setState(prev => ({ ...prev, isLoading: true }));\n\n    try {\n      const content = await contentService.generateContent(request);\n\n      setState(prev => ({\n        ...prev,\n        content,\n        isLoading: false\n      }));\n\n      onSuccess?.(content);\n      return content;\n    } catch (error) {\n      setState(prev => ({ ...prev, isLoading: false }));\n\n      const apiError = handleContentError(error, 'generation');\n      onError?.(apiError);\n\n      return undefined;\n    }\n  }, [\n    validateBeforeGeneration,\n    validateRequest,\n    handleContentError,\n    clearError,\n    clearValidation,\n    onSuccess,\n    onError\n  ]);\n\n  // Generate tweet specifically\n  const generateTweet = useCallback(async (\n    topic: string,\n    tone?: 'professional' | 'casual' | 'humorous' | 'inspirational',\n    length?: 'short' | 'medium' | 'long'\n  ): Promise<GeneratedContent | undefined> => {\n    return generateContent({\n      topic,\n      tone: tone || 'professional',\n      length: length || 'medium',\n      includeHashtags: true,\n      includeEmojis: false\n    });\n  }, [generateContent]);\n\n  // Generate thread specifically\n  const generateThread = useCallback(async (\n    topic: string,\n    tone?: 'professional' | 'casual' | 'humorous' | 'inspirational'\n  ): Promise<GeneratedContent | undefined> => {\n    return generateContent({\n      topic,\n      tone: tone || 'professional',\n      length: 'long',\n      includeHashtags: true,\n      includeEmojis: false\n    });\n  }, [generateContent]);\n\n  // Generate reply specifically\n  const generateReply = useCallback(async (\n    topic: string,\n    tone?: 'professional' | 'casual' | 'humorous' | 'inspirational'\n  ): Promise<GeneratedContent | undefined> => {\n    return generateContent({\n      topic,\n      tone: tone || 'professional',\n      length: 'short',\n      includeHashtags: false,\n      includeEmojis: false\n    });\n  }, [generateContent]);\n\n  // Clear all state\n  const clearAll = useCallback(() => {\n    setState({\n      isLoading: false,\n      content: null,\n      isValidating: false,\n      validationErrors: {}\n    });\n    clearError();\n    clearValidation();\n  }, [clearError, clearValidation]);\n\n  // Get content statistics\n  const getContentStatistics = useCallback((content: string) => {\n    return getContentStats(content);\n  }, [getContentStats]);\n\n  return {\n    // State\n    ...state,\n    errorState,\n\n    // Actions\n    generateContent,\n    generateTweet,\n    generateThread,\n    generateReply,\n    validateRequest,\n    clearAll,\n    clearError,\n\n    // Utilities\n    getContentStatistics\n  };\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;AACA;AACA;;;;;AAeO,SAAS,qBAAqB,UAAoC,CAAC,CAAC;IACzE,MAAM,EACJ,2BAA2B,IAAI,EAC/B,SAAS,EACT,OAAO,EACR,GAAG;IAEJ,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;QAC5D,WAAW;QACX,SAAS;QACT,cAAc;QACd,kBAAkB,CAAC;IACrB;IAEA,MAAM,EACJ,UAAU,EACV,kBAAkB,EAClB,qBAAqB,EACrB,UAAU,EACX,GAAG,CAAA,GAAA,gIAAA,CAAA,0BAAuB,AAAD;IAE1B,MAAM,EACJ,aAAa,EACb,eAAe,EACf,eAAe,EAChB,GAAG,CAAA,GAAA,6HAAA,CAAA,uBAAoB,AAAD;IAEvB,2BAA2B;IAC3B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,cAAc;gBAAM,kBAAkB,CAAC;YAAE,CAAC;QAEvE,MAAM,SAAiC,CAAC;QAExC,iBAAiB;QACjB,MAAM,cAAc,cAAc,QAAQ,KAAK;QAC/C,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,KAAK,EAAE;YAC7C,OAAO,KAAK,GAAG,YAAY,KAAK;QAClC;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,IAAI,IAAI,CAAC;YAAC;YAAgB;YAAU;YAAY;SAAgB,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAAG;YACnG,OAAO,IAAI,GAAG;QAChB;QAEA,8BAA8B;QAC9B,IAAI,QAAQ,MAAM,IAAI,CAAC;YAAC;YAAS;YAAU;SAAO,CAAC,QAAQ,CAAC,QAAQ,MAAM,GAAG;YAC3E,OAAO,MAAM,GAAG;QAClB;QAEA,MAAM,YAAY,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG;QAE/C,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,kBAAkB;YACpB,CAAC;QAED,IAAI,WAAW;YACb,sBAAsB;QACxB;QAEA,OAAO,CAAC;IACV,GAAG;QAAC;QAAe;KAAsB;IAEzC,sDAAsD;IACtD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,wBAAwB;QACxB;QACA;QAEA,8BAA8B;QAC9B,IAAI,4BAA4B,CAAC,gBAAgB,UAAU;YACzD,OAAO;QACT;QAEA,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAK,CAAC;QAE9C,IAAI;YACF,MAAM,UAAU,MAAM,4HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC;YAErD,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP;oBACA,WAAW;gBACb,CAAC;YAED,YAAY;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;YAE/C,MAAM,WAAW,mBAAmB,OAAO;YAC3C,UAAU;YAEV,OAAO;QACT;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,8BAA8B;IAC9B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAChC,OACA,MACA;QAEA,OAAO,gBAAgB;YACrB;YACA,MAAM,QAAQ;YACd,QAAQ,UAAU;YAClB,iBAAiB;YACjB,eAAe;QACjB;IACF,GAAG;QAAC;KAAgB;IAEpB,+BAA+B;IAC/B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,OACA;QAEA,OAAO,gBAAgB;YACrB;YACA,MAAM,QAAQ;YACd,QAAQ;YACR,iBAAiB;YACjB,eAAe;QACjB;IACF,GAAG;QAAC;KAAgB;IAEpB,8BAA8B;IAC9B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAChC,OACA;QAEA,OAAO,gBAAgB;YACrB;YACA,MAAM,QAAQ;YACd,QAAQ;YACR,iBAAiB;YACjB,eAAe;QACjB;IACF,GAAG;QAAC;KAAgB;IAEpB,kBAAkB;IAClB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,SAAS;YACP,WAAW;YACX,SAAS;YACT,cAAc;YACd,kBAAkB,CAAC;QACrB;QACA;QACA;IACF,GAAG;QAAC;QAAY;KAAgB;IAEhC,yBAAyB;IACzB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,OAAO,gBAAgB;IACzB,GAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,QAAQ;QACR,GAAG,KAAK;QACR;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,YAAY;QACZ;IACF;AACF", "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 2181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n  asChild?: boolean;\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n\n    const variants = {\n      primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base',\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg className=\"w-4 h-4 mr-2 animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,8OAAC;gBAAI,WAAU;gBAA4B,MAAK;gBAAO,SAAQ;;kCAC7D,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, label, error, helperText, id, ...props }, ref) => {\n    const inputId = id || label?.toLowerCase().replace(/\\s+/g, '-');\n\n    return (\n      <div className=\"space-y-2\">\n        {label && (\n          <label htmlFor={inputId} className=\"block text-sm font-medium text-gray-700\">\n            {label}\n          </label>\n        )}\n        <input\n          ref={ref}\n          id={inputId}\n          className={cn(\n            'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors',\n            error\n              ? 'border-red-300 focus:border-red-500 focus:ring-red-500'\n              : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500',\n            className\n          )}\n          {...props}\n        />\n        {error && (\n          <p className=\"text-sm text-red-600\">{error}</p>\n        )}\n        {helperText && !error && (\n          <p className=\"text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IACtD,MAAM,UAAU,MAAM,OAAO,cAAc,QAAQ,QAAQ;IAE3D,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,SAAS;gBAAS,WAAU;0BAChC;;;;;;0BAGL,8OAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4GACA,QACI,2DACA,mEACJ;gBAED,GAAG,KAAK;;;;;;YAEV,uBACC,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;YAEtC,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAI9C;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/content/ContentForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ContentRequest } from '@/lib/contentService';\nimport {\n  CONTENT_TONES,\n  CONTENT_LENGTHS,\n  VALIDATION_RULES\n} from '@/lib/constants';\nimport { Button } from '@/components/ui/Button';\nimport { Input } from '@/components/ui/Input';\n\ninterface ContentFormProps {\n  onSubmit: (data: ContentRequest) => Promise<void>;\n  isLoading?: boolean;\n  error?: string | null;\n  initialData?: Partial<ContentRequest>;\n}\n\nconst ContentForm = ({ onSubmit, isLoading, error, initialData }: ContentFormProps) => {\n  const [formData, setFormData] = useState<ContentRequest>({\n    topic: initialData?.topic || '',\n    tone: initialData?.tone || 'professional',\n    length: initialData?.length || 'medium',\n    includeHashtags: initialData?.includeHashtags ?? true,\n    includeEmojis: initialData?.includeEmojis ?? false,\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n\n  const validateTopic = (topic: string): string | null => {\n    if (!topic.trim()) {\n      return 'Topic is required';\n    }\n    if (topic.length < VALIDATION_RULES.MIN_TOPIC_LENGTH) {\n      return `Topic must be at least ${VALIDATION_RULES.MIN_TOPIC_LENGTH} characters`;\n    }\n    if (topic.length > VALIDATION_RULES.MAX_TOPIC_LENGTH) {\n      return `Topic cannot exceed ${VALIDATION_RULES.MAX_TOPIC_LENGTH} characters`;\n    }\n    return null;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Validate form\n    const topicError = validateTopic(formData.topic);\n    if (topicError) {\n      setValidationErrors({ topic: topicError });\n      return;\n    }\n\n    setValidationErrors({});\n    await onSubmit(formData);\n  };\n\n  const handleInputChange = (field: keyof ContentRequest, value: string | boolean) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n\n    // Clear validation error for this field\n    if (validationErrors[field]) {\n      setValidationErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[field];\n        return newErrors;\n      });\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-6\">\n      <Input\n        label=\"Topic\"\n        value={formData.topic}\n        onChange={(e) => handleInputChange('topic', e.target.value)}\n        placeholder=\"What would you like to tweet about?\"\n        error={validationErrors.topic}\n        required\n        maxLength={VALIDATION_RULES.MAX_TOPIC_LENGTH}\n      />\n\n      {/* Character count for topic */}\n      <div className=\"text-sm text-gray-500 text-right\">\n        {formData.topic.length}/{VALIDATION_RULES.MAX_TOPIC_LENGTH} characters\n      </div>\n\n      {/* Tone Selection */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">Tone</label>\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n          {CONTENT_TONES.map((tone) => (\n            <Button\n              key={tone.value}\n              type=\"button\"\n              variant={formData.tone === tone.value ? 'primary' : 'outline'}\n              size=\"sm\"\n              onClick={() => handleInputChange('tone', tone.value)}\n            >\n              {tone.label}\n            </Button>\n          ))}\n        </div>\n      </div>\n\n      {/* Length Selection */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">Length</label>\n        <div className=\"grid grid-cols-3 gap-2\">\n          {CONTENT_LENGTHS.map((length) => (\n            <Button\n              key={length.value}\n              type=\"button\"\n              variant={formData.length === length.value ? 'primary' : 'outline'}\n              size=\"sm\"\n              onClick={() => handleInputChange('length', length.value)}\n            >\n              {length.label}\n            </Button>\n          ))}\n        </div>\n      </div>\n\n      {/* Options */}\n      <div className=\"space-y-3\">\n        <div className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            id=\"hashtags\"\n            checked={formData.includeHashtags}\n            onChange={(e) => handleInputChange('includeHashtags', e.target.checked)}\n            className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n          />\n          <label htmlFor=\"hashtags\" className=\"ml-2 text-sm text-gray-700\">\n            Include hashtags\n          </label>\n        </div>\n        <div className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            id=\"emojis\"\n            checked={formData.includeEmojis}\n            onChange={(e) => handleInputChange('includeEmojis', e.target.checked)}\n            className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n          />\n          <label htmlFor=\"emojis\" className=\"ml-2 text-sm text-gray-700\">\n            Include emojis\n          </label>\n        </div>\n      </div>\n\n      <Button\n        type=\"submit\"\n        isLoading={isLoading}\n        disabled={!formData.topic.trim()}\n        className=\"w-full\"\n      >\n        {isLoading ? 'Generating...' : 'Generate Content'}\n      </Button>\n\n      {error && (\n        <div className=\"p-3 bg-red-50 border border-red-200 rounded-md\">\n          <p className=\"text-sm text-red-600\">{error}</p>\n        </div>\n      )}\n    </form>\n  );\n};\n\nexport default ContentForm;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAKA;AACA;AAVA;;;;;;AAmBA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAoB;IAChF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,OAAO,aAAa,SAAS;QAC7B,MAAM,aAAa,QAAQ;QAC3B,QAAQ,aAAa,UAAU;QAC/B,iBAAiB,aAAa,mBAAmB;QACjD,eAAe,aAAa,iBAAiB;IAC/C;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAElF,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,OAAO;QACT;QACA,IAAI,MAAM,MAAM,GAAG,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,EAAE;YACpD,OAAO,CAAC,uBAAuB,EAAE,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC;QACjF;QACA,IAAI,MAAM,MAAM,GAAG,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,EAAE;YACpD,OAAO,CAAC,oBAAoB,EAAE,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC;QAC9E;QACA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,gBAAgB;QAChB,MAAM,aAAa,cAAc,SAAS,KAAK;QAC/C,IAAI,YAAY;YACd,oBAAoB;gBAAE,OAAO;YAAW;YACxC;QACF;QAEA,oBAAoB,CAAC;QACrB,MAAM,SAAS;IACjB;IAEA,MAAM,oBAAoB,CAAC,OAA6B;QACtD,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,wCAAwC;QACxC,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC,iIAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,OAAO,SAAS,KAAK;gBACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gBAC1D,aAAY;gBACZ,OAAO,iBAAiB,KAAK;gBAC7B,QAAQ;gBACR,WAAW,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB;;;;;;0BAI9C,8OAAC;gBAAI,WAAU;;oBACZ,SAAS,KAAK,CAAC,MAAM;oBAAC;oBAAE,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB;oBAAC;;;;;;;0BAI7D,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAChE,8OAAC;wBAAI,WAAU;kCACZ,uHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,kIAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,SAAS,SAAS,IAAI,KAAK,KAAK,KAAK,GAAG,YAAY;gCACpD,MAAK;gCACL,SAAS,IAAM,kBAAkB,QAAQ,KAAK,KAAK;0CAElD,KAAK,KAAK;+BANN,KAAK,KAAK;;;;;;;;;;;;;;;;0BAavB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAChE,8OAAC;wBAAI,WAAU;kCACZ,uHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,uBACpB,8OAAC,kIAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,SAAS,SAAS,MAAM,KAAK,OAAO,KAAK,GAAG,YAAY;gCACxD,MAAK;gCACL,SAAS,IAAM,kBAAkB,UAAU,OAAO,KAAK;0CAEtD,OAAO,KAAK;+BANR,OAAO,KAAK;;;;;;;;;;;;;;;;0BAazB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,SAAS,SAAS,eAAe;gCACjC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,OAAO;gCACtE,WAAU;;;;;;0CAEZ,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA6B;;;;;;;;;;;;kCAInE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,SAAS,SAAS,aAAa;gCAC/B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,OAAO;gCACpE,WAAU;;;;;;0CAEZ,8OAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAMnE,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,WAAW;gBACX,UAAU,CAAC,SAAS,KAAK,CAAC,IAAI;gBAC9B,WAAU;0BAET,YAAY,kBAAkB;;;;;;YAGhC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAK/C;uCAEe", "debugId": null}}, {"offset": {"line": 2591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'outlined' | 'elevated';\n}\n\nconst Card = forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant = 'default', ...props }, ref) => {\n    const variants = {\n      default: 'bg-white border border-gray-200 shadow-sm',\n      outlined: 'bg-white border border-gray-300',\n      elevated: 'bg-white border border-gray-200 shadow-md',\n    };\n\n    return (\n      <div\n        ref={ref}\n        className={cn('rounded-lg', variants[variant], className)}\n        {...props}\n      />\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pb-4', className)} {...props} />\n  )\n);\n\nCardHeader.displayName = 'CardHeader';\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n);\n\nCardContent.displayName = 'CardContent';\n\nconst CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3 ref={ref} className={cn('text-xl font-semibold text-gray-900', className)} {...props} />\n  )\n);\n\nCardTitle.displayName = 'CardTitle';\n\nexport { Card, CardHeader, CardContent, CardTitle };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAMA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC7C,MAAM,WAAW;QACf,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc,QAAQ,CAAC,QAAQ,EAAE;QAC9C,GAAG,KAAK;;;;;;AAGf;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAIlE,WAAW,WAAW,GAAG;AAEzB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAIlE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;QAAa,GAAG,KAAK;;;;;;AAI5F,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/content/ContentDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { GeneratedContent } from '@/lib/contentService';\nimport { Button } from '@/components/ui/Button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\n\ninterface ContentDisplayProps {\n  content: GeneratedContent;\n  onSchedule?: (content: GeneratedContent) => void;\n  onSaveDraft?: (content: GeneratedContent) => void;\n  onEdit?: (content: GeneratedContent) => void;\n  onDelete?: (contentId: string) => void;\n}\n\nconst ContentDisplay = ({ \n  content, \n  onSchedule, \n  onSaveDraft, \n  onEdit, \n  onDelete \n}: ContentDisplayProps) => {\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'published':\n        return 'bg-green-100 text-green-800';\n      case 'scheduled':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'draft':\n        return 'bg-gray-100 text-gray-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex justify-between items-start\">\n          <CardTitle>Generated Content</CardTitle>\n          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(content.status)}`}>\n            {content.status.charAt(0).toUpperCase() + content.status.slice(1)}\n          </span>\n        </div>\n        <p className=\"text-sm text-gray-500\">\n          Created {formatDate(content.createdAt)}\n        </p>\n      </CardHeader>\n      \n      <CardContent>\n        <div className=\"space-y-4\">\n          <div className=\"p-4 bg-gray-50 border border-gray-200 rounded-md\">\n            <p className=\"text-gray-900 whitespace-pre-wrap\">{content.content}</p>\n          </div>\n\n          {content.hashtags && content.hashtags.length > 0 && (\n            <div>\n              <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Hashtags:</h4>\n              <div className=\"flex flex-wrap gap-2\">\n                {content.hashtags.map((hashtag, index) => (\n                  <span\n                    key={index}\n                    className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\"\n                  >\n                    #{hashtag}\n                  </span>\n                ))}\n              </div>\n            </div>\n          )}\n\n          <div className=\"flex flex-wrap gap-2\">\n            {onSchedule && content.status === 'draft' && (\n              <Button\n                size=\"sm\"\n                onClick={() => onSchedule(content)}\n              >\n                Schedule\n              </Button>\n            )}\n            \n            {onSaveDraft && content.status !== 'draft' && (\n              <Button\n                size=\"sm\"\n                variant=\"outline\"\n                onClick={() => onSaveDraft(content)}\n              >\n                Save Draft\n              </Button>\n            )}\n            \n            {onEdit && (\n              <Button\n                size=\"sm\"\n                variant=\"outline\"\n                onClick={() => onEdit(content)}\n              >\n                Edit\n              </Button>\n            )}\n            \n            {onDelete && (\n              <Button\n                size=\"sm\"\n                variant=\"destructive\"\n                onClick={() => onDelete(content.id)}\n              >\n                Delete\n              </Button>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default ContentDisplay;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAcA,MAAM,iBAAiB,CAAC,EACtB,OAAO,EACP,UAAU,EACV,WAAW,EACX,MAAM,EACN,QAAQ,EACY;IACpB,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC;gCAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,QAAQ,MAAM,GAAG;0CACzH,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;kCAGnE,8OAAC;wBAAE,WAAU;;4BAAwB;4BAC1B,WAAW,QAAQ,SAAS;;;;;;;;;;;;;0BAIzC,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAqC,QAAQ,OAAO;;;;;;;;;;;wBAGlE,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBAC7C,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;4CAEC,WAAU;;gDACX;gDACG;;2CAHG;;;;;;;;;;;;;;;;sCAUf,8OAAC;4BAAI,WAAU;;gCACZ,cAAc,QAAQ,MAAM,KAAK,yBAChC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,IAAM,WAAW;8CAC3B;;;;;;gCAKF,eAAe,QAAQ,MAAM,KAAK,yBACjC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,YAAY;8CAC5B;;;;;;gCAKF,wBACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO;8CACvB;;;;;;gCAKF,0BACC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,SAAS,QAAQ,EAAE;8CACnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 2860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ContentGeneration.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useContentGeneration } from '@/hooks/useContentGeneration';\nimport { ContentRequest, GeneratedContent } from '@/lib/contentService';\nimport ContentForm from '@/components/content/ContentForm';\nimport ContentDisplay from '@/components/content/ContentDisplay';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\n\nconst ContentGeneration = () => {\n  const { generateContent, isLoading, errorState } = useContentGeneration();\n  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);\n\n  const handleFormSubmit = async (formData: ContentRequest) => {\n    const content = await generateContent(formData);\n    if (content) {\n      setGeneratedContent(content);\n    }\n  };\n\n  const handleSchedule = (content: GeneratedContent) => {\n    // TODO: Implement scheduling logic\n    console.log('Schedule content:', content);\n  };\n\n  const handleSaveDraft = (content: GeneratedContent) => {\n    // TODO: Implement save draft logic\n    console.log('Save draft:', content);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>Generate Content</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <ContentForm\n            onSubmit={handleFormSubmit}\n            isLoading={isLoading}\n            error={errorState.error?.message}\n          />\n        </CardContent>\n      </Card>\n\n      {generatedContent && (\n        <ContentDisplay\n          content={generatedContent}\n          onSchedule={handleSchedule}\n          onSaveDraft={handleSaveDraft}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ContentGeneration;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AAPA;;;;;;;AASA,MAAM,oBAAoB;IACxB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,uBAAoB,AAAD;IACtE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAElF,MAAM,mBAAmB,OAAO;QAC9B,MAAM,UAAU,MAAM,gBAAgB;QACtC,IAAI,SAAS;YACX,oBAAoB;QACtB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,mCAAmC;QACnC,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,MAAM,kBAAkB,CAAC;QACvB,mCAAmC;QACnC,QAAQ,GAAG,CAAC,eAAe;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,4IAAA,CAAA,UAAW;4BACV,UAAU;4BACV,WAAW;4BACX,OAAO,WAAW,KAAK,EAAE;;;;;;;;;;;;;;;;;YAK9B,kCACC,8OAAC,+IAAA,CAAA,UAAc;gBACb,SAAS;gBACT,YAAY;gBACZ,aAAa;;;;;;;;;;;;AAKvB;uCAEe", "debugId": null}}]}