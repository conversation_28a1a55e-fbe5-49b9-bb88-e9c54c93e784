(()=>{var e={};e.id=801,e.ids=[801],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:s}=r(39844);e.exports=s("C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},9319:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\components\\\\TwitterOAuthTest.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\TwitterOAuthTest.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27526:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),n=r(4536),o=r.n(n),a=r(9319);function i(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Twitter OAuth Test Page"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Test your Twitter OAuth integration for AutoReach"})]}),(0,s.jsx)(a.default,{}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsx)(o(),{href:"/",className:"text-blue-600 hover:text-blue-800 underline",children:"← Back to Home"})})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40483:(e,t,r)=>{Promise.resolve().then(r.bind(r,70269)),Promise.resolve().then(r.t.bind(r,85814,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70269:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687),n=r(43210),o=r(88606),a=r(74265);function i({className:e=""}){let[t,r]=(0,n.useState)(!1),[i,c]=(0,n.useState)({connected:!1}),[l,d]=(0,n.useState)(!1),[u,h]=(0,n.useState)(null),[m,p]=(0,n.useState)(null),w=(0,n.useCallback)(async()=>{try{h(null);let e=await o.b.getTwitterStatus();c(e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";h(`Failed to load Twitter status: ${e}`)}},[]);(0,n.useCallback)(async()=>{let e=a.y.isAuthenticated();r(e),e&&await w()},[w]);let x=async()=>{if(!t)return void h("Please log in first to connect your Twitter account");d(!0),h(null),p(null);try{let e=await o.b.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret),sessionStorage.setItem("twitter_oauth_token",e.oauth_token),p("Redirecting to Twitter for authorization..."),setTimeout(()=>{window.location.href=e.authorization_url},1e3)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";h(`Failed to connect Twitter: ${e}`),d(!1)}},g=async()=>{d(!0),h(null),p(null);try{await o.b.disconnectTwitter(),p("Twitter account disconnected successfully"),await w()}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";h(`Failed to disconnect Twitter: ${e}`)}finally{d(!1)}},b=async()=>{d(!0),h(null),p(null);try{await w(),p("Twitter status refreshed successfully")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";h(`Failed to test connection: ${e}`)}finally{d(!1)}};return(0,s.jsxs)("div",{className:`bg-white rounded-lg shadow-md p-6 ${e}`,children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDC26 Twitter OAuth Test"}),(0,s.jsxs)("div",{className:"mb-4 p-3 rounded-md bg-gray-50",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"Authentication Status"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:`w-3 h-3 rounded-full ${t?"bg-green-500":"bg-red-500"}`}),(0,s.jsx)("span",{className:"text-sm",children:t?"Logged in to AutoReach":"Not logged in"})]})]}),(0,s.jsxs)("div",{className:"mb-4 p-3 rounded-md bg-gray-50",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"Twitter Connection Status"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)("span",{className:`w-3 h-3 rounded-full ${i.connected?"bg-green-500":"bg-red-500"}`}),(0,s.jsx)("span",{className:"text-sm",children:i.connected?"Connected to Twitter":"Not connected to Twitter"})]}),i.connected&&(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,s.jsxs)("p",{children:["Username: @",i.twitter_username]}),(0,s.jsxs)("p",{children:["User ID: ",i.twitter_user_id]})]})]}),u&&(0,s.jsx)("div",{className:"mb-4 p-3 rounded-md bg-red-50 border border-red-200",children:(0,s.jsxs)("p",{className:"text-red-700 text-sm",children:["❌ ",u]})}),m&&(0,s.jsx)("div",{className:"mb-4 p-3 rounded-md bg-green-50 border border-green-200",children:(0,s.jsxs)("p",{className:"text-green-700 text-sm",children:["✅ ",m]})}),(0,s.jsx)("div",{className:"space-y-3",children:t?(0,s.jsxs)(s.Fragment,{children:[i.connected?(0,s.jsx)("button",{onClick:g,disabled:l,className:"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:l?"Disconnecting...":"\uD83D\uDD0C Disconnect Twitter Account"}):(0,s.jsx)("button",{onClick:x,disabled:l,className:"w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:l?"Connecting...":"\uD83D\uDD17 Connect Twitter Account"}),(0,s.jsx)("button",{onClick:b,disabled:l,className:"w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:l?"Testing...":"\uD83D\uDD04 Refresh Status"})]}):(0,s.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-md",children:[(0,s.jsx)("p",{className:"text-yellow-700 mb-2",children:"Please log in to test Twitter OAuth"}),(0,s.jsx)("button",{onClick:()=>window.location.href="/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Go to Login"})]})}),(0,s.jsxs)("div",{className:"mt-6 p-3 bg-blue-50 rounded-md",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Test Instructions:"}),(0,s.jsxs)("ol",{className:"text-sm text-blue-700 space-y-1",children:[(0,s.jsx)("li",{children:"1. Make sure you're logged in to AutoReach"}),(0,s.jsx)("li",{children:'2. Click "Connect Twitter Account"'}),(0,s.jsx)("li",{children:"3. You'll be redirected to Twitter for authorization"}),(0,s.jsx)("li",{children:"4. After authorization, you'll be redirected back"}),(0,s.jsx)("li",{children:'5. Check that the status shows "Connected to Twitter"'})]})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80811:(e,t,r)=>{Promise.resolve().then(r.bind(r,9319)),Promise.resolve().then(r.t.bind(r,4536,23))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86364:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},88606:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var s=r(24731);class n{async initiateTwitterAuth(){try{return await s.uE.get("/auth/twitter/login")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to initiate Twitter auth: ${e}`)}}async handleTwitterCallback(e){try{return await s.uE.post("/auth/twitter/callback",e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to handle Twitter callback: ${e}`)}}async getTwitterStatus(){try{return await s.uE.get("/auth/twitter/status")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to get Twitter status: ${e}`)}}async disconnectTwitter(){try{return await s.uE.delete("/auth/twitter/disconnect")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to disconnect Twitter: ${e}`)}}async startTwitterOAuth(){let e=await this.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret);let t=window.open(e.authorization_url,"twitter_auth","width=600,height=600,scrollbars=yes,resizable=yes");return new Promise((r,s)=>{let n=setInterval(()=>{t?.closed&&(clearInterval(n),s(Error("Twitter authorization was cancelled")))},1e3),o=a=>{a.origin===window.location.origin&&("TWITTER_AUTH_SUCCESS"===a.data.type?(clearInterval(n),window.removeEventListener("message",o),t?.close(),r({oauth_token:e.oauth_token,oauth_token_secret:e.oauth_token_secret})):"TWITTER_AUTH_ERROR"===a.data.type&&(clearInterval(n),window.removeEventListener("message",o),t?.close(),s(Error(a.data.error||"Twitter authorization failed"))))};window.addEventListener("message",o)})}async completeTwitterOAuth(e){let t=sessionStorage.getItem("twitter_oauth_token_secret");if(!t)throw Error("OAuth token secret not found. Please restart the authentication process.");let r=new URLSearchParams(window.location.search).get("oauth_token");if(!r)throw Error("OAuth token not found in callback URL");let s=await this.handleTwitterCallback({oauth_token:r,oauth_verifier:e,oauth_token_secret:t});return sessionStorage.removeItem("twitter_oauth_token_secret"),s}}let o=new n},94735:e=>{"use strict";e.exports=require("events")},99736:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>l});var s=r(65239),n=r(48088),o=r(88170),a=r.n(o),i=r(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l={children:["",{children:["test",{children:["twitter-oauth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27526)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test\\twitter-oauth\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test\\twitter-oauth\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/test/twitter-oauth/page",pathname:"/test/twitter-oauth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,786,658,814,70],()=>r(99736));module.exports=s})();