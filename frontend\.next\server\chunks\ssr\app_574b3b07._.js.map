{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/TwitterOAuth2Login.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { authService } from '@/lib/authService';\n\ninterface TwitterOAuth2LoginProps {\n  onError?: (error: string) => void;\n  className?: string;\n}\n\nexport default function TwitterOAuth2Login({\n  onError,\n  className = ''\n}: TwitterOAuth2LoginProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleTwitterLogin = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      console.log('Starting Twitter OAuth...');\n      // Initialize Twitter OAuth 2.0 flow\n      const authData = await authService.initiateTwitterOAuth2();\n\n      console.log('Auth data received:', { \n      hasAuthUrl: !!authData.authorization_url,\n      state: authData.state \n      });\n\n      // Store OAuth state and code verifier for callback\n      sessionStorage.setItem('twitter_oauth2_state', authData.state);\n      sessionStorage.setItem('twitter_oauth2_code_verifier', authData.code_verifier);\n\n      // Redirect to Twitter authorization\n      window.location.href = authData.authorization_url;\n    } catch (error: unknown) {\n      \n      const errorMessage = error instanceof Error\n        ? `Failed to start Twitter login: ${error.message}`\n        : 'Failed to start Twitter login: Unknown error occurred';\n      setError(errorMessage);\n      onError?.(errorMessage);\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\">\n          <p className=\"text-sm\">{error}</p>\n        </div>\n      )}\n\n      <button\n        onClick={handleTwitterLogin}\n        disabled={isLoading}\n        className={`\n          w-full flex items-center justify-center px-4 py-3 border border-transparent\n          text-base font-medium rounded-md text-white bg-blue-500 hover:bg-blue-600\n          focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\n          disabled:opacity-50 disabled:cursor-not-allowed\n          transition-colors duration-200\n        `}\n      >\n        {isLoading ? (\n          <>\n            <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            Connecting to Twitter...\n          </>\n        ) : (\n          <>\n            <svg className=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n            </svg>\n            Continue with Twitter\n          </>\n        )}\n      </button>\n\n      <div className=\"text-center\">\n        <p className=\"text-sm text-gray-600\">\n          By continuing, you agree to our Terms of Service and Privacy Policy\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,mBAAmB,EACzC,OAAO,EACP,YAAY,EAAE,EACU;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,qBAAqB;QACzB,aAAa;QACb,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,oCAAoC;YACpC,MAAM,WAAW,MAAM,yHAAA,CAAA,cAAW,CAAC,qBAAqB;YAExD,QAAQ,GAAG,CAAC,uBAAuB;gBACnC,YAAY,CAAC,CAAC,SAAS,iBAAiB;gBACxC,OAAO,SAAS,KAAK;YACrB;YAEA,mDAAmD;YACnD,eAAe,OAAO,CAAC,wBAAwB,SAAS,KAAK;YAC7D,eAAe,OAAO,CAAC,gCAAgC,SAAS,aAAa;YAE7E,oCAAoC;YACpC,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAS,iBAAiB;QACnD,EAAE,OAAO,OAAgB;YAEvB,MAAM,eAAe,iBAAiB,QAClC,CAAC,+BAA+B,EAAE,MAAM,OAAO,EAAE,GACjD;YACJ,SAAS;YACT,UAAU;YACV,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YACrC,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAW;;;;;;;;;;;0BAI5B,8OAAC;gBACC,SAAS;gBACT,UAAU;gBACV,WAAW,CAAC;;;;;;QAMZ,CAAC;0BAEA,0BACC;;sCACE,8OAAC;4BAAI,WAAU;4BAA6C,OAAM;4BAA6B,MAAK;4BAAO,SAAQ;;8CACjH,8OAAC;oCAAO,WAAU;oCAAa,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAe,aAAY;;;;;;8CACxF,8OAAC;oCAAK,WAAU;oCAAa,MAAK;oCAAe,GAAE;;;;;;;;;;;;wBAC/C;;iDAIR;;sCACE,8OAAC;4BAAI,WAAU;4BAAe,MAAK;4BAAe,SAAQ;sCACxD,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;wBACJ;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  href?: string;\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n}\n\nconst Logo = ({ href = '/dashboard', size = 'md', showText = true }: LogoProps) => {\n  const sizes = {\n    sm: { icon: 'w-6 h-6', text: 'text-lg' },\n    md: { icon: 'w-8 h-8', text: 'text-xl' },\n    lg: { icon: 'w-10 h-10', text: 'text-2xl' },\n  };\n\n  const logoContent = (\n    <div className=\"flex items-center space-x-2 select-none\">\n      <div className={`${sizes[size].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`}>\n        <span className={`text-white font-bold ${size === 'sm' ? 'text-sm' : 'text-lg'}`}>A</span>\n      </div>\n      {showText && (\n        <span className={`${sizes[size].text} font-bold text-gray-900 whitespace-nowrap`}>AutoReach</span>\n      )}\n    </div>\n  );\n\n  if (href) {\n    return (\n      <Link href={href}>\n        {logoContent}\n      </Link>\n    );\n  }\n\n  return logoContent;\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,OAAO,CAAC,EAAE,OAAO,YAAY,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI,EAAa;IAC5E,MAAM,QAAQ;QACZ,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAa,MAAM;QAAW;IAC5C;IAEA,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,yEAAyE,CAAC;0BAC5G,cAAA,8OAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,SAAS,OAAO,YAAY,WAAW;8BAAE;;;;;;;;;;;YAEnF,0BACC,8OAAC;gBAAK,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC;0BAAE;;;;;;;;;;;;IAKxF,IAAI,MAAM;QACR,qBACE,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;sBACT;;;;;;IAGP;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  message?: string;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ \n  message = 'Loading...', \n  size = 'md',\n  className = '' \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-12 w-12',\n    lg: 'h-16 w-16'\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center bg-gray-50 ${className}`}>\n      <div className=\"text-center\">\n        <div className={`animate-spin rounded-full border-b-2 border-blue-500 mx-auto mb-4 ${sizeClasses[size]}`}></div>\n        <p className=\"text-gray-600\">{message}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAQe,SAAS,eAAe,EACrC,UAAU,YAAY,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACM;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;kBACrF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,CAAC,kEAAkE,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;8BACxG,8OAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;;AAItC", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport TwitterOAuth2Login from '@/components/TwitterOAuth2Login';\nimport Logo from '@/components/ui/Logo';\nimport LoadingSpinner from '@/components/ui/LoadingSpinner';\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const { isLoading: authLoading, isAuthenticated, login } = useAuth();\n  const [showTraditionalLogin, setShowTraditionalLogin] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n\n  // Show loading spinner while auth is being checked\n  if (authLoading) {\n    return <LoadingSpinner message=\"Checking authentication...\" />;\n  }\n\n  // Don't render login form if already authenticated (AuthContext will handle redirect)\n  if (isAuthenticated) {\n    return <LoadingSpinner message=\"Redirecting to dashboard...\" />;\n  }\n\n  const handleTraditionalLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      await login(formData);\n      // AuthContext will handle the redirect to dashboard\n    } catch (error: unknown) {\n      setError(error instanceof Error ? error.message : 'Login failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value\n    }));\n  };\n\n  const handleTwitterError = (error: string) => {\n    setError(error);\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <Logo href=\"/\" size=\"lg\" />\n          <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900\">\n            Sign in to AutoReach\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Grow your Twitter presence with AI-powered automation\n          </p>\n        </div>\n\n        <div className=\"bg-white shadow-md rounded-lg p-6 space-y-6\">\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md\">\n              <p className=\"text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Twitter OAuth 2.0 Login */}\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4 text-center\">\n              Recommended Sign In Method\n            </h3>\n            <TwitterOAuth2Login\n              onError={handleTwitterError}\n            />\n          </div>\n\n          {/* Divider */}\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-gray-300\" />\n            </div>\n            <div className=\"relative flex justify-center text-sm\">\n              <span className=\"px-2 bg-white text-gray-500\">Or</span>\n            </div>\n          </div>\n\n          {/* Traditional Login Toggle */}\n          <div className=\"text-center\">\n            <button\n              type=\"button\"\n              onClick={() => setShowTraditionalLogin(!showTraditionalLogin)}\n              className=\"text-blue-600 hover:text-blue-500 text-sm font-medium\"\n            >\n              {showTraditionalLogin ? 'Hide' : 'Use'} username/password login\n            </button>\n          </div>\n\n          {/* Traditional Login Form */}\n          {showTraditionalLogin && (\n            <form onSubmit={handleTraditionalLogin} className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700\">\n                  Username\n                </label>\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  required\n                  value={formData.username}\n                  onChange={handleInputChange}\n                  className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter your username\"\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                  Password\n                </label>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  required\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter your password\"\n                />\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50\"\n              >\n                {isLoading ? 'Signing in...' : 'Sign in'}\n              </button>\n            </form>\n          )}\n\n          {/* Sign up link */}\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              Don&apos;t have an account?{' '}\n              <Link href=\"/register\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n                Sign up here\n              </Link>\n            </p>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center\">\n          <p className=\"text-xs text-gray-500\">\n            By signing in, you agree to our{' '}\n            <Link href=\"/terms\" className=\"text-blue-600 hover:text-blue-500\">\n              Terms of Service\n            </Link>{' '}\n            and{' '}\n            <Link href=\"/privacy\" className=\"text-blue-600 hover:text-blue-500\">\n              Privacy Policy\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,WAAW,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,UAAU;IACZ;IAEA,mDAAmD;IACnD,IAAI,aAAa;QACf,qBAAO,8OAAC,0IAAA,CAAA,UAAc;YAAC,SAAQ;;;;;;IACjC;IAEA,sFAAsF;IACtF,IAAI,iBAAiB;QACnB,qBAAO,8OAAC,0IAAA,CAAA,UAAc;YAAC,SAAQ;;;;;;IACjC;IAEA,MAAM,yBAAyB,OAAO;QACpC,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,MAAM;QACZ,oDAAoD;QACtD,EAAE,OAAO,OAAgB;YACvB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,MAAM,qBAAqB,CAAC;QAC1B,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,MAAK;;;;;;sCACpB,8OAAC;4BAAG,WAAU;sCAA6C;;;;;;sCAG3D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAW;;;;;;;;;;;sCAK5B,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC,wIAAA,CAAA,UAAkB;oCACjB,SAAS;;;;;;;;;;;;sCAKb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;;;;;;sCAKlD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,wBAAwB,CAAC;gCACxC,WAAU;;oCAET,uBAAuB,SAAS;oCAAM;;;;;;;;;;;;wBAK1C,sCACC,8OAAC;4BAAK,UAAU;4BAAwB,WAAU;;8CAChD,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,YAAY,kBAAkB;;;;;;;;;;;;sCAMrC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCACP;kDAC5B,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;8BAQvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAwB;4BACH;0CAChC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAoC;;;;;;4BAE1D;4BAAI;4BACR;0CACJ,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhF", "debugId": null}}]}