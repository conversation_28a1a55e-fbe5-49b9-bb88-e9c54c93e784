{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  href?: string;\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n}\n\nconst Logo = ({ href = '/dashboard', size = 'md', showText = true }: LogoProps) => {\n  const sizes = {\n    sm: { icon: 'w-6 h-6', text: 'text-lg' },\n    md: { icon: 'w-8 h-8', text: 'text-xl' },\n    lg: { icon: 'w-10 h-10', text: 'text-2xl' },\n  };\n\n  const logoContent = (\n    <div className=\"flex items-center space-x-2 select-none\">\n      <div className={`${sizes[size].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`}>\n        <span className={`text-white font-bold ${size === 'sm' ? 'text-sm' : 'text-lg'}`}>A</span>\n      </div>\n      {showText && (\n        <span className={`${sizes[size].text} font-bold text-gray-900 whitespace-nowrap`}>AutoReach</span>\n      )}\n    </div>\n  );\n\n  if (href) {\n    return (\n      <Link href={href}>\n        {logoContent}\n      </Link>\n    );\n  }\n\n  return logoContent;\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,OAAO,CAAC,EAAE,OAAO,YAAY,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI,EAAa;IAC5E,MAAM,QAAQ;QACZ,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAa,MAAM;QAAW;IAC5C;IAEA,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,yEAAyE,CAAC;0BAC5G,cAAA,8OAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,SAAS,OAAO,YAAY,WAAW;8BAAE;;;;;;;;;;;YAEnF,0BACC,8OAAC;gBAAK,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC;0BAAE;;;;;;;;;;;;IAKxF,IAAI,MAAM;QACR,qBACE,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;sBACT;;;;;;IAGP;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useState } from 'react';\nimport { NAVIGATION_ITEMS } from '@/lib/constants';\nimport Logo from '@/components/ui/Logo';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface HeaderProps {\n  showNavigation?: boolean;\n}\n\nconst Header = ({ showNavigation = true }: HeaderProps) => {\n  const pathname = usePathname();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const { user, logout, isAuthenticated } = useAuth();\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 relative z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n          </div>\n\n          {/* Desktop Navigation */}\n          {showNavigation && (\n            <nav className=\"hidden md:flex space-x-6\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.path}\n                  href={item.path}\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    pathname === item.path\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  } ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100' : ''}`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          )}\n\n          {/* Mobile menu button and User menu */}\n          <div className=\"flex items-center space-x-2\">\n            {/* Mobile menu button */}\n            {showNavigation && (\n              <button\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                aria-label=\"Toggle mobile menu\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  {isMobileMenuOpen ? (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  ) : (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  )}\n                </svg>\n              </button>\n            )}\n\n            {/* User menu */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md\"\n                >\n                  <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-purple-600\">\n                      {user?.full_name?.[0] || user?.username?.[0] || 'U'}\n                    </span>\n                  </div>\n                  <span className=\"hidden sm:block text-sm font-medium\">\n                    {user?.full_name || user?.username}\n                  </span>\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* User dropdown menu */}\n                {isUserMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200\">\n                    <div className=\"px-4 py-2 text-sm text-gray-700 border-b border-gray-100\">\n                      <p className=\"font-medium\">{user?.full_name || user?.username}</p>\n                      {user?.twitter_username && (\n                        <p className=\"text-gray-500\">@{user.twitter_username}</p>\n                      )}\n                    </div>\n                    <Link\n                      href=\"/settings\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Settings\n                    </Link>\n                    <button\n                      onClick={() => {\n                        setIsUserMenuOpen(false);\n                        logout();\n                      }}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <Link\n                href=\"/login\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        </div>\n\n        {/* Mobile Navigation Menu */}\n        {showNavigation && isMobileMenuOpen && (\n          <div className=\"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg\">\n            <nav className=\"px-4 py-4 space-y-2\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.path}\n                  href={item.path}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`block px-4 py-3 rounded-lg text-base font-medium transition-colors ${\n                    pathname === item.path\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  } ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100 border border-green-200' : ''}`}\n                >\n                  <span className=\"flex items-center gap-3\">\n                    <span className=\"text-lg\">\n                      {item.name === 'Dashboard' && '📊'}\n                      {item.name === 'Content' && '✍️'}\n                      {item.name === 'Analytics' && '📈'}\n                      {item.name === 'Settings' && '⚙️'}\n                      {item.name === 'Auth' && '🔐'}\n                      {item.name === 'Test API' && '🧪'}\n                    </span>\n                    {item.name}\n                  </span>\n                </Link>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaA,MAAM,SAAS,CAAC,EAAE,iBAAiB,IAAI,EAAe;IACpD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;;;;;;;;;;wBAIN,gCACC,8OAAC;4BAAI,WAAU;sCACZ,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,IAAI,GAClB,mCACA,qDACL,CAAC,EAAE,KAAK,IAAI,KAAK,aAAa,kDAAkD,IAAI;8CAEpF,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAetB,8OAAC;4BAAI,WAAU;;gCAEZ,gCACC,8OAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChE,iCACC,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;iEAErE,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;gCAO5E,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,MAAM,WAAW,CAAC,EAAE,IAAI,MAAM,UAAU,CAAC,EAAE,IAAI;;;;;;;;;;;8DAGpD,8OAAC;oDAAK,WAAU;8DACb,MAAM,aAAa,MAAM;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAKxE,gCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAe,MAAM,aAAa,MAAM;;;;;;wDACpD,MAAM,kCACL,8OAAC;4DAAE,WAAU;;gEAAgB;gEAAE,KAAK,gBAAgB;;;;;;;;;;;;;8DAGxD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;wDACP,kBAAkB;wDAClB;oDACF;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;yDAOP,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;gBAQN,kBAAkB,kCACjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,oBAAoB;gCACnC,WAAW,CAAC,mEAAmE,EAC7E,aAAa,KAAK,IAAI,GAClB,mCACA,qDACL,CAAC,EAAE,KAAK,IAAI,KAAK,aAAa,0EAA0E,IAAI;0CAE7G,cAAA,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAK,WAAU;;gDACb,KAAK,IAAI,KAAK,eAAe;gDAC7B,KAAK,IAAI,KAAK,aAAa;gDAC3B,KAAK,IAAI,KAAK,eAAe;gDAC7B,KAAK,IAAI,KAAK,cAAc;gDAC5B,KAAK,IAAI,KAAK,UAAU;gDACxB,KAAK,IAAI,KAAK,cAAc;;;;;;;wCAE9B,KAAK,IAAI;;;;;;;+BAlBP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BhC;uCAEe", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  message?: string;\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ \n  message = 'Loading...', \n  size = 'md',\n  className = '' \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-12 w-12',\n    lg: 'h-16 w-16'\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center bg-gray-50 ${className}`}>\n      <div className=\"text-center\">\n        <div className={`animate-spin rounded-full border-b-2 border-blue-500 mx-auto mb-4 ${sizeClasses[size]}`}></div>\n        <p className=\"text-gray-600\">{message}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAQe,SAAS,eAAe,EACrC,UAAU,YAAY,EACtB,OAAO,IAAI,EACX,YAAY,EAAE,EACM;IACpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;kBACrF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,CAAC,kEAAkE,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;8BACxG,8OAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;;;;;;AAItC", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport Header from '@/components/Header';\nimport LoadingSpinner from '@/components/ui/LoadingSpinner';\n\nexport default function Dashboard() {\n  const { user, isLoading } = useAuth();\n\n  if (isLoading) {\n    return <LoadingSpinner message=\"Loading dashboard...\" />;\n  }\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Welcome back, {user?.full_name || user?.username}!\n            {user?.twitter_username && (\n              <span className=\"text-purple-600 font-medium\"> (@{user.twitter_username})</span>\n            )}\n            {' '}Here&apos;s your Twitter growth overview.\n          </p>\n\n          {user?.twitter_username ? (\n            <div className=\"mt-4 bg-green-50 border border-green-200 rounded-md p-4\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"h-5 w-5 text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-green-800\">\n                    Twitter Account Connected\n                  </h3>\n                  <div className=\"mt-2 text-sm text-green-700\">\n                    <p>Your Twitter account is successfully connected and ready for automation!</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"h-5 w-5 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-yellow-800\">\n                    Connect Your Twitter Account\n                  </h3>\n                  <div className=\"mt-2 text-sm text-yellow-700\">\n                    <p>To start using AutoReach features, please connect your Twitter account in Settings.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Followers</p>\n                <p className=\"text-2xl font-bold text-gray-900\">2,847</p>\n                <p className=\"text-sm text-green-600\">+12% from last month</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-twitter-light-blue rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-twitter-blue\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Tweets</p>\n                <p className=\"text-2xl font-bold text-gray-900\">156</p>\n                <p className=\"text-sm text-green-600\">+8% from last month</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Engagement</p>\n                <p className=\"text-2xl font-bold text-gray-900\">4.2%</p>\n                <p className=\"text-sm text-green-600\">+0.5% from last month</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Scheduled</p>\n                <p className=\"text-2xl font-bold text-gray-900\">23</p>\n                <p className=\"text-sm text-gray-500\">Posts in queue</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Quick Actions</h2>\n            <div className=\"space-y-3\">\n              <button className=\"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3\">\n                    <svg className=\"w-4 h-4 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900\">Generate Content</p>\n                    <p className=\"text-sm text-gray-600\">Create AI-powered tweets</p>\n                  </div>\n                </div>\n              </button>\n\n              <button className=\"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-twitter-light-blue rounded-lg flex items-center justify-center mr-3\">\n                    <svg className=\"w-4 h-4 text-twitter-blue\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900\">Schedule Posts</p>\n                    <p className=\"text-sm text-gray-600\">Plan your content calendar</p>\n                  </div>\n                </div>\n              </button>\n\n              <button className=\"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors\">\n                <div className=\"flex items-center\">\n                  <div className=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3\">\n                    <svg className=\"w-4 h-4 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900\">View Analytics</p>\n                    <p className=\"text-sm text-gray-600\">Track your performance</p>\n                  </div>\n                </div>\n              </button>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Recent Activity</h2>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">Tweet published successfully</p>\n                  <p className=\"text-xs text-gray-500\">2 hours ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">Content generated for &quot;AI trends&quot;</p>\n                  <p className=\"text-xs text-gray-500\">4 hours ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-2 h-2 bg-yellow-500 rounded-full mt-2\"></div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">5 posts scheduled for tomorrow</p>\n                  <p className=\"text-xs text-gray-500\">6 hours ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                <div>\n                  <p className=\"text-sm font-medium text-gray-900\">Gained 15 new followers</p>\n                  <p className=\"text-xs text-gray-500\">1 day ago</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAElC,IAAI,WAAW;QACb,qBAAO,8OAAC,0IAAA,CAAA,UAAc;YAAC,SAAQ;;;;;;IACjC;IACA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;;oCAAqB;oCACjB,MAAM,aAAa,MAAM;oCAAS;oCAChD,MAAM,kCACL,8OAAC;wCAAK,WAAU;;4CAA8B;4CAAI,KAAK,gBAAgB;4CAAC;;;;;;;oCAEzE;oCAAI;;;;;;;4BAGN,MAAM,iCACL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAyB,MAAK;gDAAe,SAAQ;0DAClE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAwI,UAAS;;;;;;;;;;;;;;;;sDAGhL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAGnD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAMX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAe,SAAQ;0DACnE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoN,UAAS;;;;;;;;;;;;;;;;sDAG5P,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DAGpD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA2B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAClF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;0CAK5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA4B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACnF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;0CAK5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAuB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC9E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;0CAK5C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAA0B,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjF,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA2B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAClF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAA4B;;;;;;8EACzC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;0DAK3C,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA4B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACnF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAA4B;;;;;;8EACzC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;0DAK3C,8OAAC;gDAAO,WAAU;0DAChB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAChF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAA4B;;;;;;8EACzC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAoC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD", "debugId": null}}]}