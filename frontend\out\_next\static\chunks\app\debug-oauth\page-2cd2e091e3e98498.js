(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[548],{2703:(e,t,a)=>{"use strict";a.d(t,{I:()=>s});class r{getItem(e){try{return localStorage.getItem(e)}catch(e){return null}}setItem(e,t){try{localStorage.setItem(e,t)}catch(e){}}removeItem(e){try{localStorage.removeItem(e)}catch(e){}}clear(){try{localStorage.clear()}catch(e){}}}let s=new r},3851:(e,t,a)=>{"use strict";a.d(t,{y:()=>l});var r=a(4611),s=a(2703),n=a(4205);class i{async login(e){this.validateLoginRequest(e);let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let a=await this.apiClient.instance.post("/auth/token",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return s.I.setItem(r.d5.AUTH_TOKEN,a.data.access_token),a.data}async register(e){return this.validateRegisterRequest(e),this.apiClient.post("/users/",e)}async getCurrentUser(){return this.apiClient.get("/auth/me")}async logout(){s.I.removeItem(r.d5.AUTH_TOKEN),window.location.href="/login"}isAuthenticated(){return!!s.I.getItem(r.d5.AUTH_TOKEN)}getToken(){return s.I.getItem(r.d5.AUTH_TOKEN)}async initiateTwitterOAuth2(){return(await this.apiClient.instance.post("/auth/oauth2/twitter/init")).data}async handleTwitterOAuth2Callback(e){let t=await this.apiClient.instance.post("/auth/oauth2/twitter/callback",e);return s.I.setItem(r.d5.AUTH_TOKEN,t.data.access_token),t.data}validateLoginRequest(e){var t,a;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(a=e.password)?void 0:a.trim()))throw Error("Password is required")}validateRegisterRequest(e){var t,a,r;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(a=e.email)?void 0:a.trim()))throw Error("Email is required");if(!(null==(r=e.password)?void 0:r.trim()))throw Error("Password is required");if(e.password.length<6)throw Error("Password must be at least 6 characters");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email))throw Error("Please enter a valid email address")}constructor(e){this.apiClient=e}}let l=new i(n.uE)},4205:(e,t,a)=>{"use strict";a.d(t,{uE:()=>l});var r=a(3464),s=a(4611),n=a(2703);class i{createClient(){return r.A.create({baseURL:s.i3.BASE_URL,timeout:s.i3.TIMEOUT,headers:{"Content-Type":"application/json"}})}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.storage.getItem(s.d5.AUTH_TOKEN);return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(this.handleError(e))),this.client.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(this.handleError(e))})}handleUnauthorized(){this.storage.removeItem(s.d5.AUTH_TOKEN),window.location.href="/login"}handleError(e){if(!e.response)return Error(s.UU.NETWORK_ERROR);switch(e.response.status){case 401:return Error(s.UU.UNAUTHORIZED);case 400:return Error(s.UU.VALIDATION_ERROR);default:let t=e.response.data;return Error((null==t?void 0:t.message)||s.UU.GENERIC_ERROR)}}get instance(){return this.client}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(e=n.I){this.storage=e,this.client=this.createClient(),this.setupInterceptors()}}let l=new i},4611:(e,t,a)=>{"use strict";a.d(t,{ID:()=>s,Ij:()=>r,KA:()=>o,Ot:()=>l,RW:()=>T,UU:()=>c,WF:()=>h,d5:()=>u,gY:()=>E,gx:()=>d,i3:()=>i,m0:()=>n});let r=[{name:"Dashboard",path:"/dashboard"},{name:"Content",path:"/content"},{name:"Analytics",path:"/analytics"},{name:"Settings",path:"/settings"},{name:"Auth",path:"/auth"},{name:"Test API",path:"/test-connection"}],s=[{value:"engaging",label:"Engaging"},{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"educational",label:"Educational"},{value:"humorous",label:"Humorous"},{value:"informative",label:"Informative"},{value:"helpful",label:"Helpful"}],n=[{value:"short",label:"Short (1-2 sentences)"},{value:"medium",label:"Medium (3-5 sentences)"},{value:"long",label:"Long (6+ sentences)"}],i={BASE_URL:"http://localhost:8000/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},l={MAX_TWEET_LENGTH:280,MAX_THREAD_TWEETS:25,DEFAULT_THREAD_SIZE:3,MAX_HASHTAGS_RECOMMENDED:3,MAX_MENTIONS_RECOMMENDED:5},o={DEFAULT_STYLE:"engaging",DEFAULT_LANGUAGE:"en",SUPPORTED_STYLES:["engaging","professional","casual","educational","humorous","informative","helpful"],SUPPORTED_LANGUAGES:[{code:"en",name:"English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"}]},E={MIN_TOPIC_LENGTH:3,MAX_TOPIC_LENGTH:200,MIN_CONTENT_LENGTH:1,MAX_CONTENT_LENGTH:2e3,MIN_USERNAME_LENGTH:3,MAX_USERNAME_LENGTH:50,MIN_PASSWORD_LENGTH:8,MAX_PASSWORD_LENGTH:128},u={AUTH_TOKEN:"authToken",USER_PREFERENCES:"userPreferences",DRAFT_CONTENT:"draftContent",THEME:"theme"},c={NETWORK_ERROR:"Network error. Please check your connection.",UNAUTHORIZED:"Your session has expired. Please log in again.",FORBIDDEN:"Access denied.",NOT_FOUND:"The requested resource was not found.",VALIDATION_ERROR:"Please check your input and try again.",GENERATION_FAILED:"Content generation failed. Please try again.",RATE_LIMIT_EXCEEDED:"Rate limit exceeded. Please try again later.",GENERIC_ERROR:"Something went wrong. Please try again."},d={OK:200,CREATED:201,NO_CONTENT:204,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,CONFLICT:409,UNPROCESSABLE_ENTITY:422,TOO_MANY_REQUESTS:429,INTERNAL_SERVER_ERROR:500,SERVICE_UNAVAILABLE:503},h={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,USERNAME:/^[a-zA-Z0-9_]+$/,PASSWORD:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,HASHTAG:/#\w+/g,MENTION:/@\w+/g,URL:/https?:\/\/[^\s]+/g},T=e=>{switch(e){case d.UNAUTHORIZED:return c.UNAUTHORIZED;case d.FORBIDDEN:return c.FORBIDDEN;case d.NOT_FOUND:return c.NOT_FOUND;case d.UNPROCESSABLE_ENTITY:return c.VALIDATION_ERROR;case d.TOO_MANY_REQUESTS:return c.RATE_LIMIT_EXCEEDED;case d.INTERNAL_SERVER_ERROR:case d.SERVICE_UNAVAILABLE:return c.GENERATION_FAILED;default:return c.GENERIC_ERROR}}},5948:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var r=a(5155),s=a(2115),n=a(3851);function i(){let[e,t]=(0,s.useState)(null),[a,i]=(0,s.useState)(null),l=async()=>{try{let e=await n.y.initiateTwitterOAuth2();t(e),console.log("OAuth init result:",e)}catch(e){i(e instanceof Error?e.message:"Unknown error"),console.error("OAuth init error:",e)}};return(0,r.jsxs)("div",{className:"p-8 max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"OAuth Debug"}),(0,r.jsx)("button",{onClick:l,className:"bg-blue-500 text-white px-4 py-2 rounded mb-4",children:"Test OAuth Init"}),a&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4",children:a}),e&&(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h2",{className:"font-semibold mb-2",children:"OAuth Init Response:"}),(0,r.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(e,null,2)})]})]})}},8826:(e,t,a)=>{Promise.resolve().then(a.bind(a,5948))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,441,684,358],()=>t(8826)),_N_E=e.O()}]);