(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[800],{1125:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(5155),s=r(6874),n=r.n(s);let l=e=>{let{href:t="/dashboard",size:r="md",showText:s=!0}=e,l={sm:{icon:"w-6 h-6",text:"text-lg"},md:{icon:"w-8 h-8",text:"text-xl"},lg:{icon:"w-10 h-10",text:"text-2xl"}},i=(0,a.jsxs)("div",{className:"flex items-center space-x-2 select-none",children:[(0,a.jsx)("div",{className:"".concat(l[r].icon," bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0"),children:(0,a.jsx)("span",{className:"text-white font-bold ".concat("sm"===r?"text-sm":"text-lg"),children:"A"})}),s&&(0,a.jsx)("span",{className:"".concat(l[r].text," font-bold text-gray-900 whitespace-nowrap"),children:"AutoReach"})]});return t?(0,a.jsx)(n(),{href:t,children:i}):i}},3315:(e,t,r)=>{"use strict";r.d(t,{default:()=>b});var a=r(5155),s=r(2115),n=r(7926),l=r(4989),i=r(4611),o=r(6801),c=r(4768),d=r(4143);let u=(0,s.forwardRef)((e,t)=>{let{className:r,label:s,error:n,helperText:l,id:i,...o}=e,c=i||(null==s?void 0:s.toLowerCase().replace(/\s+/g,"-"));return(0,a.jsxs)("div",{className:"space-y-2",children:[s&&(0,a.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-700",children:s}),(0,a.jsx)("input",{ref:t,id:c,className:(0,d.cn)("w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors",n?"border-red-300 focus:border-red-500 focus:ring-red-500":"border-gray-300 focus:border-primary-500 focus:ring-primary-500",r),...o}),n&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:n}),l&&!n&&(0,a.jsx)("p",{className:"text-sm text-gray-500",children:l})]})});u.displayName="Input";let m=e=>{var t,r;let{onSubmit:n,isLoading:l,error:o,initialData:d}=e,[m,h]=(0,s.useState)({topic:(null==d?void 0:d.topic)||"",tone:(null==d?void 0:d.tone)||"professional",length:(null==d?void 0:d.length)||"medium",includeHashtags:null==(t=null==d?void 0:d.includeHashtags)||t,includeEmojis:null!=(r=null==d?void 0:d.includeEmojis)&&r}),[x,g]=(0,s.useState)({}),p=e=>e.trim()?e.length<i.gY.MIN_TOPIC_LENGTH?"Topic must be at least ".concat(i.gY.MIN_TOPIC_LENGTH," characters"):e.length>i.gY.MAX_TOPIC_LENGTH?"Topic cannot exceed ".concat(i.gY.MAX_TOPIC_LENGTH," characters"):null:"Topic is required",y=async e=>{e.preventDefault();let t=p(m.topic);if(t)return void g({topic:t});g({}),await n(m)},b=(e,t)=>{h(r=>({...r,[e]:t})),x[e]&&g(t=>{let r={...t};return delete r[e],r})};return(0,a.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,a.jsx)(u,{label:"Topic",value:m.topic,onChange:e=>b("topic",e.target.value),placeholder:"What would you like to tweet about?",error:x.topic,required:!0,maxLength:i.gY.MAX_TOPIC_LENGTH}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 text-right",children:[m.topic.length,"/",i.gY.MAX_TOPIC_LENGTH," characters"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Tone"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:i.ID.map(e=>(0,a.jsx)(c.$,{type:"button",variant:m.tone===e.value?"primary":"outline",size:"sm",onClick:()=>b("tone",e.value),children:e.label},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Length"}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-2",children:i.m0.map(e=>(0,a.jsx)(c.$,{type:"button",variant:m.length===e.value?"primary":"outline",size:"sm",onClick:()=>b("length",e.value),children:e.label},e.value))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"hashtags",checked:m.includeHashtags,onChange:e=>b("includeHashtags",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"hashtags",className:"ml-2 text-sm text-gray-700",children:"Include hashtags"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"emojis",checked:m.includeEmojis,onChange:e=>b("includeEmojis",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"emojis",className:"ml-2 text-sm text-gray-700",children:"Include emojis"})]})]}),(0,a.jsx)(c.$,{type:"submit",isLoading:l,disabled:!m.topic.trim(),className:"w-full",children:l?"Generating...":"Generate Content"}),o&&(0,a.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:(0,a.jsx)("p",{className:"text-sm text-red-600",children:o})})]})},h=(0,s.forwardRef)((e,t)=>{let{className:r,variant:s="default",...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,d.cn)("rounded-lg",{default:"bg-white border border-gray-200 shadow-sm",outlined:"bg-white border border-gray-300",elevated:"bg-white border border-gray-200 shadow-md"}[s],r),...n})});h.displayName="Card";let x=(0,s.forwardRef)((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,d.cn)("p-6 pb-4",r),...s})});x.displayName="CardHeader";let g=(0,s.forwardRef)((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,d.cn)("p-6 pt-0",r),...s})});g.displayName="CardContent";let p=(0,s.forwardRef)((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,d.cn)("text-xl font-semibold text-gray-900",r),...s})});p.displayName="CardTitle";let y=e=>{let{content:t,onSchedule:r,onSaveDraft:s,onEdit:n,onDelete:l}=e;return(0,a.jsxs)(h,{children:[(0,a.jsxs)(x,{children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsx)(p,{children:"Generated Content"}),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"published":return"bg-green-100 text-green-800";case"scheduled":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(t.status)),children:t.status.charAt(0).toUpperCase()+t.status.slice(1)})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Created ",new Date(t.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})]})]}),(0,a.jsx)(g,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"p-4 bg-gray-50 border border-gray-200 rounded-md",children:(0,a.jsx)("p",{className:"text-gray-900 whitespace-pre-wrap",children:t.content})}),t.hashtags&&t.hashtags.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Hashtags:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.hashtags.map((e,t)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["#",e]},t))})]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[r&&"draft"===t.status&&(0,a.jsx)(c.$,{size:"sm",onClick:()=>r(t),children:"Schedule"}),s&&"draft"!==t.status&&(0,a.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>s(t),children:"Save Draft"}),n&&(0,a.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>n(t),children:"Edit"}),l&&(0,a.jsx)(c.$,{size:"sm",variant:"destructive",onClick:()=>l(t.id),children:"Delete"})]})]})})]})},b=()=>{var e;let{generateContent:t,isLoading:r,errorState:c}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{validateBeforeGeneration:t=!0,onSuccess:r,onError:a}=e,[c,d]=(0,s.useState)({isLoading:!1,content:null,isValidating:!1,validationErrors:{}}),{errorState:u,handleContentError:m,handleValidationError:h,clearError:x}=function(){let{handleError:e,...t}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i.i3.RETRY_ATTEMPTS,[t,r]=(0,s.useState)({error:null,isError:!1,errorId:null}),[a,n]=(0,s.useState)({isRetrying:!1,retryCount:0,maxRetries:e,canRetry:!0}),o=(0,s.useRef)(0),c=(0,s.useCallback)((t,s)=>{let i=l.zc.handleApiError(t,{logError:!0,fallbackMessage:"Error in ".concat(s||"operation")});r({error:i,isError:!0,errorId:"error_".concat(++o.current,"_").concat(Date.now())});let c=l.Z$.isRetryableError(t)&&a.retryCount<e;return n(e=>({...e,canRetry:c,isRetrying:!1})),i},[e,a.retryCount]),d=(0,s.useCallback)(()=>{r({error:null,isError:!1,errorId:null}),n(e=>({...e,isRetrying:!1,retryCount:0,canRetry:!0}))},[]),u=(0,s.useCallback)(async t=>{if(a.canRetry&&!a.isRetrying){n(e=>({...e,isRetrying:!0,retryCount:e.retryCount+1}));try{let r=await l.Z$.retryOperation(t,e-a.retryCount,i.i3.RETRY_DELAY);return d(),r}catch(e){throw c(e,"retry operation"),e}}},[a,e,c,d]),m=(0,s.useCallback)(async(e,t)=>{try{return d(),await e()}catch(e){c(e,t);return}},[c,d]);return{errorState:t,retryState:a,handleError:c,clearError:d,retry:u,executeWithErrorHandling:m}}(),r=(0,s.useCallback)((t,r)=>e(l.Iz.handleGenerationError(t),"content ".concat(r||"operation")),[e]),a=(0,s.useCallback)(t=>e(l.Iz.handleContentValidationError(t),"content validation"),[e]);return{...t,handleContentError:r,handleValidationError:a}}(),{validateTopic:g,getContentStats:p,clearValidation:y}=function(){let[e,t]=(0,s.useState)({}),r=(0,s.useCallback)(e=>{let r=o.$D.validateTopic(e);return t(e=>({...e,topic:r})),r},[]),a=(0,s.useCallback)(e=>{let r=o.$D.validateContent(e);return t(e=>({...e,content:r})),r},[]);return{validationResults:e,validateTopic:r,validateContent:a,getContentStats:(0,s.useCallback)(e=>o.$D.getContentStats(e),[]),clearValidation:(0,s.useCallback)(e=>{e?t(t=>{let r={...t};return delete r[e],r}):t({})},[])}}(),b=(0,s.useCallback)(e=>{d(e=>({...e,isValidating:!0,validationErrors:{}}));let t={},r=g(e.topic);!r.isValid&&r.error&&(t.topic=r.error),e.tone&&!["professional","casual","humorous","inspirational"].includes(e.tone)&&(t.tone="Invalid content tone"),e.length&&!["short","medium","long"].includes(e.length)&&(t.length="Invalid content length");let a=Object.keys(t).length>0;return d(e=>({...e,isValidating:!1,validationErrors:t})),a&&h(t),!a},[g,h]),f=(0,s.useCallback)(async e=>{if(x(),y(),!t||b(e)){d(e=>({...e,isLoading:!0}));try{let t=await n.l.generateContent(e);return d(e=>({...e,content:t,isLoading:!1})),null==r||r(t),t}catch(t){d(e=>({...e,isLoading:!1}));let e=m(t,"generation");null==a||a(e);return}}},[t,b,m,x,y,r,a]),j=(0,s.useCallback)(async(e,t,r)=>f({topic:e,tone:t||"professional",length:r||"medium",includeHashtags:!0,includeEmojis:!1}),[f]),v=(0,s.useCallback)(async(e,t)=>f({topic:e,tone:t||"professional",length:"long",includeHashtags:!0,includeEmojis:!1}),[f]),N=(0,s.useCallback)(async(e,t)=>f({topic:e,tone:t||"professional",length:"short",includeHashtags:!1,includeEmojis:!1}),[f]),C=(0,s.useCallback)(()=>{d({isLoading:!1,content:null,isValidating:!1,validationErrors:{}}),x(),y()},[x,y]),w=(0,s.useCallback)(e=>p(e),[p]);return{...c,errorState:u,generateContent:f,generateTweet:j,generateThread:v,generateReply:N,validateRequest:b,clearAll:C,clearError:x,getContentStatistics:w}}(),[d,u]=(0,s.useState)(null),b=async e=>{let r=await t(e);r&&u(r)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(h,{children:[(0,a.jsx)(x,{children:(0,a.jsx)(p,{children:"Generate Content"})}),(0,a.jsx)(g,{children:(0,a.jsx)(m,{onSubmit:b,isLoading:r,error:null==(e=c.error)?void 0:e.message})})]}),d&&(0,a.jsx)(y,{content:d,onSchedule:e=>{console.log("Schedule content:",e)},onSaveDraft:e=>{console.log("Save draft:",e)}})]})}},4143:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},4768:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(5155),s=r(2115),n=r(4143);let l=(0,s.forwardRef)((e,t)=>{let{className:r,variant:s="primary",size:l="md",isLoading:i,children:o,disabled:c,...d}=e;return(0,a.jsxs)("button",{ref:t,className:(0,n.cn)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[l],r),disabled:c||i,...d,children:[i&&(0,a.jsxs)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),o]})});l.displayName="Button"},7864:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(5155),s=r(6874),n=r.n(s),l=r(5695),i=r(2115),o=r(4611),c=r(1125),d=r(8794);let u=e=>{var t,r;let{showNavigation:s=!0}=e,u=(0,l.usePathname)(),[m,h]=(0,i.useState)(!1),[x,g]=(0,i.useState)(!1),{user:p,logout:y,isAuthenticated:b}=(0,d.A)();return(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 relative z-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(c.A,{})}),s&&(0,a.jsx)("nav",{className:"hidden md:flex space-x-6",children:o.Ij.map(e=>(0,a.jsx)(n(),{href:e.path,className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(u===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"," ").concat("Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100":""),children:e.name},e.path))}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[s&&(0,a.jsx)("button",{onClick:()=>h(!m),className:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle mobile menu",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:m?(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),b?(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>g(!x),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-purple-600",children:(null==p||null==(t=p.full_name)?void 0:t[0])||(null==p||null==(r=p.username)?void 0:r[0])||"U"})}),(0,a.jsx)("span",{className:"hidden sm:block text-sm font-medium",children:(null==p?void 0:p.full_name)||(null==p?void 0:p.username)}),(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),x&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,a.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 border-b border-gray-100",children:[(0,a.jsx)("p",{className:"font-medium",children:(null==p?void 0:p.full_name)||(null==p?void 0:p.username)}),(null==p?void 0:p.twitter_username)&&(0,a.jsxs)("p",{className:"text-gray-500",children:["@",p.twitter_username]})]}),(0,a.jsx)(n(),{href:"/settings",onClick:()=>g(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Settings"}),(0,a.jsx)("button",{onClick:()=>{g(!1),y()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})]}):(0,a.jsx)(n(),{href:"/login",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})]})]}),s&&m&&(0,a.jsx)("div",{className:"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,a.jsx)("nav",{className:"px-4 py-4 space-y-2",children:o.Ij.map(e=>(0,a.jsx)(n(),{href:e.path,onClick:()=>h(!1),className:"block px-4 py-3 rounded-lg text-base font-medium transition-colors ".concat(u===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"," ").concat("Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100 border border-green-200":""),children:(0,a.jsxs)("span",{className:"flex items-center gap-3",children:[(0,a.jsxs)("span",{className:"text-lg",children:["Dashboard"===e.name&&"\uD83D\uDCCA","Content"===e.name&&"✍️","Analytics"===e.name&&"\uD83D\uDCC8","Settings"===e.name&&"⚙️","Auth"===e.name&&"\uD83D\uDD10","Test API"===e.name&&"\uD83E\uDDEA"]}),e.name]})},e.path))})})]})})}},9203:(e,t,r)=>{Promise.resolve().then(r.bind(r,3315)),Promise.resolve().then(r.bind(r,7864))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,277,794,926,441,684,358],()=>t(9203)),_N_E=e.O()}]);