'use client';

import { useEffect, useState } from 'react';

interface DebugInfo {
  [key: string]: unknown;
}

export default function DebugTwitter() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [currentUrl, setCurrentUrl] = useState<string>('');

  useEffect(() => {
    const fetchDebugInfo = async () => {
      try {
        const response = await fetch('/api/auth/oauth2/twitter/debug');
        const data = await response.json();
        setDebugInfo(data);
      } catch {
        setError('Failed to fetch debug info');
      }
    };

    // Set current URL on client side only
    setCurrentUrl(window.location.href);

    fetchDebugInfo();
  }, []);

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Twitter OAuth Debug</h1>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {debugInfo && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="font-semibold mb-2">Configuration:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-6">
        <h2 className="font-semibold mb-2">Current URL:</h2>
        <p className="text-sm bg-gray-100 p-2 rounded">{currentUrl || 'Loading...'}</p>
      </div>
    </div>
  );
}