"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[794],{2703:(e,t,a)=>{a.d(t,{I:()=>n});class r{getItem(e){try{return localStorage.getItem(e)}catch(e){return null}}setItem(e,t){try{localStorage.setItem(e,t)}catch(e){}}removeItem(e){try{localStorage.removeItem(e)}catch(e){}}clear(){try{localStorage.clear()}catch(e){}}}let n=new r},3851:(e,t,a)=>{a.d(t,{y:()=>i});var r=a(4611),n=a(2703),s=a(4205);class o{async login(e){this.validateLoginRequest(e);let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let a=await this.apiClient.instance.post("/auth/token",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return n.I.setItem(r.d5.AUTH_TOKEN,a.data.access_token),a.data}async register(e){return this.validateRegisterRequest(e),this.apiClient.post("/users/",e)}async getCurrentUser(){return this.apiClient.get("/auth/me")}async logout(){n.I.removeItem(r.d5.AUTH_TOKEN),window.location.href="/login"}isAuthenticated(){return!!n.I.getItem(r.d5.AUTH_TOKEN)}getToken(){return n.I.getItem(r.d5.AUTH_TOKEN)}async initiateTwitterOAuth2(){return(await this.apiClient.instance.post("/auth/oauth2/twitter/init")).data}async handleTwitterOAuth2Callback(e){let t=await this.apiClient.instance.post("/auth/oauth2/twitter/callback",e);return n.I.setItem(r.d5.AUTH_TOKEN,t.data.access_token),t.data}validateLoginRequest(e){var t,a;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(a=e.password)?void 0:a.trim()))throw Error("Password is required")}validateRegisterRequest(e){var t,a,r;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(a=e.email)?void 0:a.trim()))throw Error("Email is required");if(!(null==(r=e.password)?void 0:r.trim()))throw Error("Password is required");if(e.password.length<6)throw Error("Password must be at least 6 characters");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email))throw Error("Please enter a valid email address")}constructor(e){this.apiClient=e}}let i=new o(s.uE)},4205:(e,t,a)=>{a.d(t,{uE:()=>i});var r=a(3464),n=a(4611),s=a(2703);class o{createClient(){return r.A.create({baseURL:n.i3.BASE_URL,timeout:n.i3.TIMEOUT,headers:{"Content-Type":"application/json"}})}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.storage.getItem(n.d5.AUTH_TOKEN);return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(this.handleError(e))),this.client.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(this.handleError(e))})}handleUnauthorized(){this.storage.removeItem(n.d5.AUTH_TOKEN),window.location.href="/login"}handleError(e){if(!e.response)return Error(n.UU.NETWORK_ERROR);switch(e.response.status){case 401:return Error(n.UU.UNAUTHORIZED);case 400:return Error(n.UU.VALIDATION_ERROR);default:let t=e.response.data;return Error((null==t?void 0:t.message)||n.UU.GENERIC_ERROR)}}get instance(){return this.client}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,a){return(await this.client.post(e,t,a)).data}async put(e,t,a){return(await this.client.put(e,t,a)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(e=s.I){this.storage=e,this.client=this.createClient(),this.setupInterceptors()}}let i=new o},4611:(e,t,a)=>{a.d(t,{ID:()=>n,Ij:()=>r,KA:()=>l,Ot:()=>i,RW:()=>T,UU:()=>E,WF:()=>d,d5:()=>c,gY:()=>u,gx:()=>h,i3:()=>o,m0:()=>s});let r=[{name:"Dashboard",path:"/dashboard"},{name:"Content",path:"/content"},{name:"Analytics",path:"/analytics"},{name:"Settings",path:"/settings"},{name:"Auth",path:"/auth"},{name:"Test API",path:"/test-connection"}],n=[{value:"engaging",label:"Engaging"},{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"educational",label:"Educational"},{value:"humorous",label:"Humorous"},{value:"informative",label:"Informative"},{value:"helpful",label:"Helpful"}],s=[{value:"short",label:"Short (1-2 sentences)"},{value:"medium",label:"Medium (3-5 sentences)"},{value:"long",label:"Long (6+ sentences)"}],o={BASE_URL:"http://localhost:8000/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},i={MAX_TWEET_LENGTH:280,MAX_THREAD_TWEETS:25,DEFAULT_THREAD_SIZE:3,MAX_HASHTAGS_RECOMMENDED:3,MAX_MENTIONS_RECOMMENDED:5},l={DEFAULT_STYLE:"engaging",DEFAULT_LANGUAGE:"en",SUPPORTED_STYLES:["engaging","professional","casual","educational","humorous","informative","helpful"],SUPPORTED_LANGUAGES:[{code:"en",name:"English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"}]},u={MIN_TOPIC_LENGTH:3,MAX_TOPIC_LENGTH:200,MIN_CONTENT_LENGTH:1,MAX_CONTENT_LENGTH:2e3,MIN_USERNAME_LENGTH:3,MAX_USERNAME_LENGTH:50,MIN_PASSWORD_LENGTH:8,MAX_PASSWORD_LENGTH:128},c={AUTH_TOKEN:"authToken",USER_PREFERENCES:"userPreferences",DRAFT_CONTENT:"draftContent",THEME:"theme"},E={NETWORK_ERROR:"Network error. Please check your connection.",UNAUTHORIZED:"Your session has expired. Please log in again.",FORBIDDEN:"Access denied.",NOT_FOUND:"The requested resource was not found.",VALIDATION_ERROR:"Please check your input and try again.",GENERATION_FAILED:"Content generation failed. Please try again.",RATE_LIMIT_EXCEEDED:"Rate limit exceeded. Please try again later.",GENERIC_ERROR:"Something went wrong. Please try again."},h={OK:200,CREATED:201,NO_CONTENT:204,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,CONFLICT:409,UNPROCESSABLE_ENTITY:422,TOO_MANY_REQUESTS:429,INTERNAL_SERVER_ERROR:500,SERVICE_UNAVAILABLE:503},d={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,USERNAME:/^[a-zA-Z0-9_]+$/,PASSWORD:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,HASHTAG:/#\w+/g,MENTION:/@\w+/g,URL:/https?:\/\/[^\s]+/g},T=e=>{switch(e){case h.UNAUTHORIZED:return E.UNAUTHORIZED;case h.FORBIDDEN:return E.FORBIDDEN;case h.NOT_FOUND:return E.NOT_FOUND;case h.UNPROCESSABLE_ENTITY:return E.VALIDATION_ERROR;case h.TOO_MANY_REQUESTS:return E.RATE_LIMIT_EXCEEDED;case h.INTERNAL_SERVER_ERROR:case h.SERVICE_UNAVAILABLE:return E.GENERATION_FAILED;default:return E.GENERIC_ERROR}}},5695:(e,t,a)=>{var r=a(8999);a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},8794:(e,t,a)=>{a.d(t,{A:()=>h,AuthProvider:()=>E});var r=a(5155),n=a(2115),s=a(5695),o=a(3851);let i=(0,n.createContext)(void 0),l=["/","/login","/register","/auth/twitter/callback","/auth/twitter/oauth2-callback","/test-twitter-oauth2","/terms","/privacy","/about","/contact"],u=["/auth/twitter/callback","/auth/twitter/oauth2-callback"],c=["/dashboard","/content","/analytics","/settings","/profile"];function E(e){let{children:t}=e,[a,E]=(0,n.useState)(null),[h,d]=(0,n.useState)(!0),T=(0,s.useRouter)(),R=(0,s.usePathname)(),A=!!a&&!!o.y.getToken(),g=l.some(e=>"/"===e?"/"===R:R.startsWith(e)),N=c.some(e=>R.startsWith(e)),m=u.some(e=>R.startsWith(e)),_=async()=>{try{console.log("\uD83D\uDD04 AuthContext: Starting refreshUser...");let e=o.y.getToken();if(console.log("\uD83D\uDD0D AuthContext: Token check:",{hasToken:!!e,tokenLength:null==e?void 0:e.length}),!e){console.log("❌ AuthContext: No token found, setting user to null"),E(null);return}console.log("\uD83D\uDD04 AuthContext: Fetching current user...");let t=await o.y.getCurrentUser();console.log("✅ AuthContext: User fetched successfully:",{userId:t.id,username:t.username,twitterUsername:t.twitter_username}),E(t)}catch(e){console.error("❌ AuthContext: Failed to refresh user:",e),E(null),o.y.logout()}},I=async e=>{try{await o.y.login(e),await _()}catch(e){throw e}};return(0,n.useEffect)(()=>{(async()=>{d(!0);try{o.y.getToken()?await _():E(null)}catch(e){console.error("Auth initialization error:",e),o.y.logout(),E(null)}finally{setTimeout(()=>{d(!1)},200)}})()},[]),(0,n.useEffect)(()=>{if(h||m)return;let e=setTimeout(()=>{if(!A&&N){console.log("Redirecting to login - protected route accessed without auth"),T.push("/login");return}if(A&&"/login"===R){console.log("Redirecting to dashboard - already authenticated"),T.push("/dashboard");return}if(!A&&!g){console.log("Redirecting to login - non-public route accessed without auth"),T.push("/login");return}},100);return()=>clearTimeout(e)},[A,h,R,N,g,m,T]),(0,r.jsx)(i.Provider,{value:{user:a,isLoading:h,isAuthenticated:A,login:I,logout:()=>{o.y.logout(),E(null),T.push("/")},refreshUser:_},children:t})}function h(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}}]);