(()=>{var e={};e.id=548,e.ids=[548],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5086:(e,t,r)=>{Promise.resolve().then(r.bind(r,36873))},9814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>c,tree:()=>d});var s=r(65239),o=r(48088),n=r(88170),i=r.n(n),a=r(30893),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let d={children:["",{children:["debug-oauth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,78191)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\debug-oauth\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\debug-oauth\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/debug-oauth/page",pathname:"/debug-oauth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36873:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(60687),o=r(43210),n=r(74265);function i(){let[e,t]=(0,o.useState)(null),[r,i]=(0,o.useState)(null),a=async()=>{try{let e=await n.y.initiateTwitterOAuth2();t(e),console.log("OAuth init result:",e)}catch(e){i(e instanceof Error?e.message:"Unknown error"),console.error("OAuth init error:",e)}};return(0,s.jsxs)("div",{className:"p-8 max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"OAuth Debug"}),(0,s.jsx)("button",{onClick:a,className:"bg-blue-500 text-white px-4 py-2 rounded mb-4",children:"Test OAuth Init"}),r&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4",children:r}),e&&(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsx)("h2",{className:"font-semibold mb-2",children:"OAuth Init Response:"}),(0,s.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(e,null,2)})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58230:(e,t,r)=>{Promise.resolve().then(r.bind(r,78191))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78191:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\debug-oauth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\debug-oauth\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86364:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,786,658,70],()=>r(9814));module.exports=s})();