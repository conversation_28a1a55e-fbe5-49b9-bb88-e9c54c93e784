(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3437:(e,s,t)=>{Promise.resolve().then(t.bind(t,46836))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},18169:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({message:e="Loading...",size:s="md",className:t=""}){return(0,r.jsx)("div",{className:`min-h-screen flex items-center justify-center bg-gray-50 ${t}`,children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:`animate-spin rounded-full border-b-2 border-blue-500 mx-auto mb-4 ${{sm:"h-6 w-6",md:"h-12 w-12",lg:"h-16 w-16"}[s]}`}),(0,r.jsx)("p",{className:"text-gray-600",children:e})]})})}t(43210)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23200:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let o={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,64118)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\dashboard\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46836:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(60687);t(43210);var a=t(63772),n=t(95188),l=t(18169);function i(){let{user:e,isLoading:s}=(0,a.A)();return s?(0,r.jsx)(l.A,{message:"Loading dashboard..."}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,r.jsxs)("p",{className:"text-gray-600 mt-2",children:["Welcome back, ",e?.full_name||e?.username,"!",e?.twitter_username&&(0,r.jsxs)("span",{className:"text-purple-600 font-medium",children:[" (@",e.twitter_username,")"]})," ","Here's your Twitter growth overview."]}),e?.twitter_username?(0,r.jsx)("div",{className:"mt-4 bg-green-50 border border-green-200 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800",children:"Twitter Account Connected"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,r.jsx)("p",{children:"Your Twitter account is successfully connected and ready for automation!"})})]})]})}):(0,r.jsx)("div",{className:"mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Connect Your Twitter Account"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:(0,r.jsx)("p",{children:"To start using AutoReach features, please connect your Twitter account in Settings."})})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Followers"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"2,847"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"+12% from last month"})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-twitter-light-blue rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-twitter-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tweets"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"156"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"+8% from last month"})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Engagement"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"4.2%"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"+0.5% from last month"})]})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Scheduled"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"23"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Posts in queue"})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Generate Content"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Create AI-powered tweets"})]})]})}),(0,r.jsx)("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-twitter-light-blue rounded-lg flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-twitter-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"Schedule Posts"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Plan your content calendar"})]})]})}),(0,r.jsx)("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"View Analytics"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Track your performance"})]})]})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Recent Activity"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Tweet published successfully"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"2 hours ago"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:'Content generated for "AI trends"'}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"4 hours ago"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mt-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"5 posts scheduled for tomorrow"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"6 hours ago"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mt-2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Gained 15 new followers"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"1 day ago"})]})]})]})]})]})]})]})}},51159:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var r=t(60687),a=t(85814),n=t.n(a);let l=({href:e="/dashboard",size:s="md",showText:t=!0})=>{let a={sm:{icon:"w-6 h-6",text:"text-lg"},md:{icon:"w-8 h-8",text:"text-xl"},lg:{icon:"w-10 h-10",text:"text-2xl"}},l=(0,r.jsxs)("div",{className:"flex items-center space-x-2 select-none",children:[(0,r.jsx)("div",{className:`${a[s].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`,children:(0,r.jsx)("span",{className:`text-white font-bold ${"sm"===s?"text-sm":"text-lg"}`,children:"A"})}),t&&(0,r.jsx)("span",{className:`${a[s].text} font-bold text-gray-900 whitespace-nowrap`,children:"AutoReach"})]});return e?(0,r.jsx)(n(),{href:e,children:l}):l}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64118:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\dashboard\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79469:(e,s,t)=>{Promise.resolve().then(t.bind(t,64118))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86364:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},94735:e=>{"use strict";e.exports=require("events")},95188:(e,s,t)=>{"use strict";t.d(s,{default:()=>x});var r=t(60687),a=t(85814),n=t.n(a),l=t(16189),i=t(43210),d=t(51785),o=t(51159),c=t(63772);let x=({showNavigation:e=!0})=>{let s=(0,l.usePathname)(),[t,a]=(0,i.useState)(!1),[x,m]=(0,i.useState)(!1),{user:h,logout:u,isAuthenticated:p}=(0,c.A)();return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 relative z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(o.A,{})}),e&&(0,r.jsx)("nav",{className:"hidden md:flex space-x-6",children:d.Ij.map(e=>(0,r.jsx)(n(),{href:e.path,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${s===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"} ${"Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100":""}`,children:e.name},e.path))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e&&(0,r.jsx)("button",{onClick:()=>a(!t),className:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle mobile menu",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),p?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>m(!x),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-purple-600",children:h?.full_name?.[0]||h?.username?.[0]||"U"})}),(0,r.jsx)("span",{className:"hidden sm:block text-sm font-medium",children:h?.full_name||h?.username}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),x&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,r.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 border-b border-gray-100",children:[(0,r.jsx)("p",{className:"font-medium",children:h?.full_name||h?.username}),h?.twitter_username&&(0,r.jsxs)("p",{className:"text-gray-500",children:["@",h.twitter_username]})]}),(0,r.jsx)(n(),{href:"/settings",onClick:()=>m(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Settings"}),(0,r.jsx)("button",{onClick:()=>{m(!1),u()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})]}):(0,r.jsx)(n(),{href:"/login",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})]})]}),e&&t&&(0,r.jsx)("div",{className:"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,r.jsx)("nav",{className:"px-4 py-4 space-y-2",children:d.Ij.map(e=>(0,r.jsx)(n(),{href:e.path,onClick:()=>a(!1),className:`block px-4 py-3 rounded-lg text-base font-medium transition-colors ${s===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"} ${"Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100 border border-green-200":""}`,children:(0,r.jsxs)("span",{className:"flex items-center gap-3",children:[(0,r.jsxs)("span",{className:"text-lg",children:["Dashboard"===e.name&&"\uD83D\uDCCA","Content"===e.name&&"✍️","Analytics"===e.name&&"\uD83D\uDCC8","Settings"===e.name&&"⚙️","Auth"===e.name&&"\uD83D\uDD10","Test API"===e.name&&"\uD83E\uDDEA"]}),e.name]})},e.path))})})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,786,658,814,70],()=>t(23200));module.exports=r})();