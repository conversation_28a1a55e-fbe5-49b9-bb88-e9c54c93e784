{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Logo.tsx"], "sourcesContent": ["import Link from 'next/link';\n\ninterface LogoProps {\n  href?: string;\n  size?: 'sm' | 'md' | 'lg';\n  showText?: boolean;\n}\n\nconst Logo = ({ href = '/dashboard', size = 'md', showText = true }: LogoProps) => {\n  const sizes = {\n    sm: { icon: 'w-6 h-6', text: 'text-lg' },\n    md: { icon: 'w-8 h-8', text: 'text-xl' },\n    lg: { icon: 'w-10 h-10', text: 'text-2xl' },\n  };\n\n  const logoContent = (\n    <div className=\"flex items-center space-x-2 select-none\">\n      <div className={`${sizes[size].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`}>\n        <span className={`text-white font-bold ${size === 'sm' ? 'text-sm' : 'text-lg'}`}>A</span>\n      </div>\n      {showText && (\n        <span className={`${sizes[size].text} font-bold text-gray-900 whitespace-nowrap`}>AutoReach</span>\n      )}\n    </div>\n  );\n\n  if (href) {\n    return (\n      <Link href={href}>\n        {logoContent}\n      </Link>\n    );\n  }\n\n  return logoContent;\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAAA;;;AAQA,MAAM,OAAO,CAAC,EAAE,OAAO,YAAY,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI,EAAa;IAC5E,MAAM,QAAQ;QACZ,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAW,MAAM;QAAU;QACvC,IAAI;YAAE,MAAM;YAAa,MAAM;QAAW;IAC5C;IAEA,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,yEAAyE,CAAC;0BAC5G,cAAA,8OAAC;oBAAK,WAAW,CAAC,qBAAqB,EAAE,SAAS,OAAO,YAAY,WAAW;8BAAE;;;;;;;;;;;YAEnF,0BACC,8OAAC;gBAAK,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC;0BAAE;;;;;;;;;;;;IAKxF,IAAI,MAAM;QACR,qBACE,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM;sBACT;;;;;;IAGP;IAEA,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useState } from 'react';\nimport { NAVIGATION_ITEMS } from '@/lib/constants';\nimport Logo from '@/components/ui/Logo';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface HeaderProps {\n  showNavigation?: boolean;\n}\n\nconst Header = ({ showNavigation = true }: HeaderProps) => {\n  const pathname = usePathname();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const { user, logout, isAuthenticated } = useAuth();\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 relative z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Logo />\n          </div>\n\n          {/* Desktop Navigation */}\n          {showNavigation && (\n            <nav className=\"hidden md:flex space-x-6\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.path}\n                  href={item.path}\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    pathname === item.path\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  } ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100' : ''}`}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          )}\n\n          {/* Mobile menu button and User menu */}\n          <div className=\"flex items-center space-x-2\">\n            {/* Mobile menu button */}\n            {showNavigation && (\n              <button\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                aria-label=\"Toggle mobile menu\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  {isMobileMenuOpen ? (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  ) : (\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                  )}\n                </svg>\n              </button>\n            )}\n\n            {/* User menu */}\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md\"\n                >\n                  <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-sm font-medium text-purple-600\">\n                      {user?.full_name?.[0] || user?.username?.[0] || 'U'}\n                    </span>\n                  </div>\n                  <span className=\"hidden sm:block text-sm font-medium\">\n                    {user?.full_name || user?.username}\n                  </span>\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* User dropdown menu */}\n                {isUserMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200\">\n                    <div className=\"px-4 py-2 text-sm text-gray-700 border-b border-gray-100\">\n                      <p className=\"font-medium\">{user?.full_name || user?.username}</p>\n                      {user?.twitter_username && (\n                        <p className=\"text-gray-500\">@{user.twitter_username}</p>\n                      )}\n                    </div>\n                    <Link\n                      href=\"/settings\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Settings\n                    </Link>\n                    <button\n                      onClick={() => {\n                        setIsUserMenuOpen(false);\n                        logout();\n                      }}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <Link\n                href=\"/login\"\n                className=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        </div>\n\n        {/* Mobile Navigation Menu */}\n        {showNavigation && isMobileMenuOpen && (\n          <div className=\"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg\">\n            <nav className=\"px-4 py-4 space-y-2\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.path}\n                  href={item.path}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`block px-4 py-3 rounded-lg text-base font-medium transition-colors ${\n                    pathname === item.path\n                      ? 'text-primary-600 bg-primary-50'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                  } ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100 border border-green-200' : ''}`}\n                >\n                  <span className=\"flex items-center gap-3\">\n                    <span className=\"text-lg\">\n                      {item.name === 'Dashboard' && '📊'}\n                      {item.name === 'Content' && '✍️'}\n                      {item.name === 'Analytics' && '📈'}\n                      {item.name === 'Settings' && '⚙️'}\n                      {item.name === 'Auth' && '🔐'}\n                      {item.name === 'Test API' && '🧪'}\n                    </span>\n                    {item.name}\n                  </span>\n                </Link>\n              ))}\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaA,MAAM,SAAS,CAAC,EAAE,iBAAiB,IAAI,EAAe;IACpD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEhD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;;;;;;;;;;wBAIN,gCACC,8OAAC;4BAAI,WAAU;sCACZ,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,2DAA2D,EACrE,aAAa,KAAK,IAAI,GAClB,mCACA,qDACL,CAAC,EAAE,KAAK,IAAI,KAAK,aAAa,kDAAkD,IAAI;8CAEpF,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;;;;;;sCAetB,8OAAC;4BAAI,WAAU;;gCAEZ,gCACC,8OAAC;oCACC,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChE,iCACC,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;iEAErE,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;gCAO5E,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,MAAM,WAAW,CAAC,EAAE,IAAI,MAAM,UAAU,CAAC,EAAE,IAAI;;;;;;;;;;;8DAGpD,8OAAC;oDAAK,WAAU;8DACb,MAAM,aAAa,MAAM;;;;;;8DAE5B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAKxE,gCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAe,MAAM,aAAa,MAAM;;;;;;wDACpD,MAAM,kCACL,8OAAC;4DAAE,WAAU;;gEAAgB;gEAAE,KAAK,gBAAgB;;;;;;;;;;;;;8DAGxD,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,SAAS,IAAM,kBAAkB;oDACjC,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;wDACP,kBAAkB;wDAClB;oDACF;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;yDAOP,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;gBAQN,kBAAkB,kCACjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,uHAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,oBAAoB;gCACnC,WAAW,CAAC,mEAAmE,EAC7E,aAAa,KAAK,IAAI,GAClB,mCACA,qDACL,CAAC,EAAE,KAAK,IAAI,KAAK,aAAa,0EAA0E,IAAI;0CAE7G,cAAA,8OAAC;oCAAK,WAAU;;sDACd,8OAAC;4CAAK,WAAU;;gDACb,KAAK,IAAI,KAAK,eAAe;gDAC7B,KAAK,IAAI,KAAK,aAAa;gDAC3B,KAAK,IAAI,KAAK,eAAe;gDAC7B,KAAK,IAAI,KAAK,cAAc;gDAC5B,KAAK,IAAI,KAAK,UAAU;gDACxB,KAAK,IAAI,KAAK,cAAc;;;;;;;wCAE9B,KAAK,IAAI;;;;;;;+BAlBP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BhC;uCAEe", "debugId": null}}]}