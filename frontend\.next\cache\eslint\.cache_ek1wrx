[{"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\analytics\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\callback\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\oauth2-callback\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\content\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\login\\page.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\page.tsx": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\settings\\page.tsx": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test\\twitter-oauth\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-connection\\page.tsx": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-twitter-oauth2\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ConnectionTest.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\content\\ContentDisplay.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\content\\ContentForm.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ContentGeneration.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\Header.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\TwitterAuth.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\TwitterOAuth2Login.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\TwitterOAuthTest.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\Button.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\Card.tsx": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\Input.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\Logo.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\contexts\\AuthContext.tsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\hooks\\useContentGeneration.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\hooks\\useErrorHandling.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\hooks\\useValidation.ts": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\apiClient.ts": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\authService.ts": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\constants.ts": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\contentService.ts": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\errorHandling.ts": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\storage.ts": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\twitterAuthService.ts": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\utils.ts": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\validation.ts": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\types\\api.ts": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\LoadingSpinner.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\debug-oauth\\page.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\debug-twitter\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-simple\\page.tsx": "42"}, {"size": 9990, "mtime": 1748471652288, "results": "43", "hashOfConfig": "44"}, {"size": 8002, "mtime": 1748558744705, "results": "45", "hashOfConfig": "44"}, {"size": 8800, "mtime": 1753297456259, "results": "46", "hashOfConfig": "44"}, {"size": 5136, "mtime": 1748471602805, "results": "47", "hashOfConfig": "44"}, {"size": 11832, "mtime": 1753296264421, "results": "48", "hashOfConfig": "44"}, {"size": 789, "mtime": 1748557807081, "results": "49", "hashOfConfig": "44"}, {"size": 6390, "mtime": 1753303677242, "results": "50", "hashOfConfig": "44"}, {"size": 32813, "mtime": 1748554394184, "results": "51", "hashOfConfig": "44"}, {"size": 10504, "mtime": 1748472903657, "results": "52", "hashOfConfig": "44"}, {"size": 828, "mtime": 1748543967717, "results": "53", "hashOfConfig": "44"}, {"size": 2468, "mtime": 1748544005202, "results": "54", "hashOfConfig": "44"}, {"size": 5723, "mtime": 1748547166017, "results": "55", "hashOfConfig": "44"}, {"size": 7840, "mtime": 1748558776913, "results": "56", "hashOfConfig": "44"}, {"size": 3738, "mtime": 1748463590192, "results": "57", "hashOfConfig": "44"}, {"size": 5337, "mtime": 1748469489934, "results": "58", "hashOfConfig": "44"}, {"size": 1691, "mtime": 1748547280668, "results": "59", "hashOfConfig": "44"}, {"size": 6897, "mtime": 1748536998729, "results": "60", "hashOfConfig": "44"}, {"size": 6918, "mtime": 1748558788437, "results": "61", "hashOfConfig": "44"}, {"size": 3623, "mtime": 1753037474972, "results": "62", "hashOfConfig": "44"}, {"size": 7625, "mtime": 1748545289467, "results": "63", "hashOfConfig": "44"}, {"size": 1989, "mtime": 1748463750712, "results": "64", "hashOfConfig": "44"}, {"size": 1496, "mtime": 1748463417572, "results": "65", "hashOfConfig": "44"}, {"size": 1348, "mtime": 1748463403840, "results": "66", "hashOfConfig": "44"}, {"size": 992, "mtime": 1748473410925, "results": "67", "hashOfConfig": "44"}, {"size": 6534, "mtime": 1753297395752, "results": "68", "hashOfConfig": "44"}, {"size": 5333, "mtime": 1748547515599, "results": "69", "hashOfConfig": "44"}, {"size": 12233, "mtime": 1748546254555, "results": "70", "hashOfConfig": "44"}, {"size": 7000, "mtime": 1748546706385, "results": "71", "hashOfConfig": "44"}, {"size": 3074, "mtime": 1748469354551, "results": "72", "hashOfConfig": "44"}, {"size": 4277, "mtime": 1753287447602, "results": "73", "hashOfConfig": "44"}, {"size": 5464, "mtime": 1748569858132, "results": "74", "hashOfConfig": "44"}, {"size": 6115, "mtime": 1748558800199, "results": "75", "hashOfConfig": "44"}, {"size": 14618, "mtime": 1748551584208, "results": "76", "hashOfConfig": "44"}, {"size": 1155, "mtime": 1748463493802, "results": "77", "hashOfConfig": "44"}, {"size": 5292, "mtime": 1748551860214, "results": "78", "hashOfConfig": "44"}, {"size": 169, "mtime": 1748463424679, "results": "79", "hashOfConfig": "44"}, {"size": 12384, "mtime": 1748541499453, "results": "80", "hashOfConfig": "44"}, {"size": 4630, "mtime": 1748530581046, "results": "81", "hashOfConfig": "44"}, {"size": 696, "mtime": 1753296222173, "results": "82", "hashOfConfig": "44"}, {"size": 1394, "mtime": 1753301071343, "results": "83", "hashOfConfig": "44"}, {"size": 1534, "mtime": 1753301085664, "results": "84", "hashOfConfig": "44"}, {"size": 429, "mtime": 1748571442361, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1axwa5h", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\callback\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\oauth2-callback\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\content\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test\\twitter-oauth\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-connection\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-twitter-oauth2\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ConnectionTest.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\content\\ContentDisplay.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\content\\ContentForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ContentGeneration.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\TwitterAuth.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\TwitterOAuth2Login.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\TwitterOAuthTest.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\Logo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\hooks\\useContentGeneration.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\hooks\\useErrorHandling.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\hooks\\useValidation.ts", [], ["212", "213"], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\apiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\constants.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\contentService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\errorHandling.ts", [], ["214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235"], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\storage.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\twitterAuthService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\lib\\validation.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\types\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ui\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\debug-oauth\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\debug-twitter\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-simple\\page.tsx", [], [], {"ruleId": "236", "severity": 1, "message": "237", "line": 52, "column": 29, "nodeType": "238", "endLine": 52, "endColumn": 40, "suppressions": "239"}, {"ruleId": "236", "severity": 1, "message": "237", "line": 220, "column": 29, "nodeType": "238", "endLine": 220, "endColumn": 40, "suppressions": "240"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 26, "column": 13, "nodeType": "243", "messageId": "244", "endLine": 26, "endColumn": 16, "suggestions": "245", "suppressions": "246"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 49, "column": 48, "nodeType": "243", "messageId": "244", "endLine": 49, "endColumn": 51, "suggestions": "247", "suppressions": "248"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 78, "column": 31, "nodeType": "243", "messageId": "244", "endLine": 78, "endColumn": 34, "suggestions": "249", "suppressions": "250"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 172, "column": 45, "nodeType": "243", "messageId": "244", "endLine": 172, "endColumn": 48, "suggestions": "251", "suppressions": "252"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 305, "column": 38, "nodeType": "243", "messageId": "244", "endLine": 305, "endColumn": 41, "suggestions": "253", "suppressions": "254"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 323, "column": 39, "nodeType": "243", "messageId": "244", "endLine": 323, "endColumn": 42, "suggestions": "255", "suppressions": "256"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 333, "column": 47, "nodeType": "243", "messageId": "244", "endLine": 333, "endColumn": 50, "suggestions": "257", "suppressions": "258"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 347, "column": 33, "nodeType": "243", "messageId": "244", "endLine": 347, "endColumn": 36, "suggestions": "259", "suppressions": "260"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 383, "column": 36, "nodeType": "243", "messageId": "244", "endLine": 383, "endColumn": 39, "suggestions": "261", "suppressions": "262"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 393, "column": 38, "nodeType": "243", "messageId": "244", "endLine": 393, "endColumn": 41, "suggestions": "263", "suppressions": "264"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 413, "column": 20, "nodeType": "243", "messageId": "244", "endLine": 413, "endColumn": 23, "suggestions": "265", "suppressions": "266"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 437, "column": 34, "nodeType": "243", "messageId": "244", "endLine": 437, "endColumn": 37, "suggestions": "267", "suppressions": "268"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 454, "column": 40, "nodeType": "243", "messageId": "244", "endLine": 454, "endColumn": 43, "suggestions": "269", "suppressions": "270"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 468, "column": 42, "nodeType": "243", "messageId": "244", "endLine": 468, "endColumn": 45, "suggestions": "271", "suppressions": "272"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 481, "column": 42, "nodeType": "243", "messageId": "244", "endLine": 481, "endColumn": 45, "suggestions": "273", "suppressions": "274"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 494, "column": 43, "nodeType": "243", "messageId": "244", "endLine": 494, "endColumn": 46, "suggestions": "275", "suppressions": "276"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 510, "column": 12, "nodeType": "243", "messageId": "244", "endLine": 510, "endColumn": 15, "suggestions": "277", "suppressions": "278"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 512, "column": 22, "nodeType": "243", "messageId": "244", "endLine": 512, "endColumn": 25, "suggestions": "279", "suppressions": "280"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 529, "column": 22, "nodeType": "243", "messageId": "244", "endLine": 529, "endColumn": 25, "suggestions": "281", "suppressions": "282"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 543, "column": 45, "nodeType": "243", "messageId": "244", "endLine": 543, "endColumn": 48, "suggestions": "283", "suppressions": "284"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 545, "column": 26, "nodeType": "243", "messageId": "244", "endLine": 545, "endColumn": 29, "suggestions": "285", "suppressions": "286"}, {"ruleId": "241", "severity": 2, "message": "242", "line": 564, "column": 18, "nodeType": "243", "messageId": "244", "endLine": 564, "endColumn": 21, "suggestions": "287", "suppressions": "288"}, "react-hooks/exhaustive-deps", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", ["289"], ["290"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["291", "292"], ["293"], ["294", "295"], ["296"], ["297", "298"], ["299"], ["300", "301"], ["302"], ["303", "304"], ["305"], ["306", "307"], ["308"], ["309", "310"], ["311"], ["312", "313"], ["314"], ["315", "316"], ["317"], ["318", "319"], ["320"], ["321", "322"], ["323"], ["324", "325"], ["326"], ["327", "328"], ["329"], ["330", "331"], ["332"], ["333", "334"], ["335"], ["336", "337"], ["338"], ["339", "340"], ["341"], ["342", "343"], ["344"], ["345", "346"], ["347"], ["348", "349"], ["350"], ["351", "352"], ["353"], ["354", "355"], ["356"], {"kind": "357", "justification": "358"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "360", "desc": "361"}, {"messageId": "362", "fix": "363", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "365", "desc": "361"}, {"messageId": "362", "fix": "366", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "367", "desc": "361"}, {"messageId": "362", "fix": "368", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "369", "desc": "361"}, {"messageId": "362", "fix": "370", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "371", "desc": "361"}, {"messageId": "362", "fix": "372", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "373", "desc": "361"}, {"messageId": "362", "fix": "374", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "375", "desc": "361"}, {"messageId": "362", "fix": "376", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "377", "desc": "361"}, {"messageId": "362", "fix": "378", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "379", "desc": "361"}, {"messageId": "362", "fix": "380", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "381", "desc": "361"}, {"messageId": "362", "fix": "382", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "383", "desc": "361"}, {"messageId": "362", "fix": "384", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "385", "desc": "361"}, {"messageId": "362", "fix": "386", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "387", "desc": "361"}, {"messageId": "362", "fix": "388", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "389", "desc": "361"}, {"messageId": "362", "fix": "390", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "391", "desc": "361"}, {"messageId": "362", "fix": "392", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "393", "desc": "361"}, {"messageId": "362", "fix": "394", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "395", "desc": "361"}, {"messageId": "362", "fix": "396", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "397", "desc": "361"}, {"messageId": "362", "fix": "398", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "399", "desc": "361"}, {"messageId": "362", "fix": "400", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "401", "desc": "361"}, {"messageId": "362", "fix": "402", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "403", "desc": "361"}, {"messageId": "362", "fix": "404", "desc": "364"}, {"kind": "357", "justification": "358"}, {"messageId": "359", "fix": "405", "desc": "361"}, {"messageId": "362", "fix": "406", "desc": "364"}, {"kind": "357", "justification": "358"}, "directive", "", "suggestUnknown", {"range": "407", "text": "408"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "409", "text": "410"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "411", "text": "408"}, {"range": "412", "text": "410"}, {"range": "413", "text": "408"}, {"range": "414", "text": "410"}, {"range": "415", "text": "408"}, {"range": "416", "text": "410"}, {"range": "417", "text": "408"}, {"range": "418", "text": "410"}, {"range": "419", "text": "408"}, {"range": "420", "text": "410"}, {"range": "421", "text": "408"}, {"range": "422", "text": "410"}, {"range": "423", "text": "408"}, {"range": "424", "text": "410"}, {"range": "425", "text": "408"}, {"range": "426", "text": "410"}, {"range": "427", "text": "408"}, {"range": "428", "text": "410"}, {"range": "429", "text": "408"}, {"range": "430", "text": "410"}, {"range": "431", "text": "408"}, {"range": "432", "text": "410"}, {"range": "433", "text": "408"}, {"range": "434", "text": "410"}, {"range": "435", "text": "408"}, {"range": "436", "text": "410"}, {"range": "437", "text": "408"}, {"range": "438", "text": "410"}, {"range": "439", "text": "408"}, {"range": "440", "text": "410"}, {"range": "441", "text": "408"}, {"range": "442", "text": "410"}, {"range": "443", "text": "408"}, {"range": "444", "text": "410"}, {"range": "445", "text": "408"}, {"range": "446", "text": "410"}, {"range": "447", "text": "408"}, {"range": "448", "text": "410"}, {"range": "449", "text": "408"}, {"range": "450", "text": "410"}, {"range": "451", "text": "408"}, {"range": "452", "text": "410"}, [628, 631], "unknown", [628, 631], "never", [1135, 1138], [1135, 1138], [1838, 1841], [1838, 1841], [4630, 4633], [4630, 4633], [8199, 8202], [8199, 8202], [8620, 8623], [8620, 8623], [8872, 8875], [8872, 8875], [9132, 9135], [9132, 9135], [9930, 9933], [9930, 9933], [10157, 10160], [10157, 10160], [10605, 10608], [10605, 10608], [11115, 11118], [11115, 11118], [11490, 11493], [11490, 11493], [11897, 11900], [11897, 11900], [12202, 12205], [12202, 12205], [12529, 12532], [12529, 12532], [12924, 12927], [12924, 12927], [12971, 12974], [12971, 12974], [13370, 13373], [13370, 13373], [13699, 13702], [13699, 13702], [13769, 13772], [13769, 13772], [14217, 14220], [14217, 14220]]