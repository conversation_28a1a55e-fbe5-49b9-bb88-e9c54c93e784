from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, status
from fastapi.security import <PERSON>A<PERSON>2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import timed<PERSON><PERSON>
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel


from app.core.database import get_db
from app.core.config import settings
from app.core.auth_utils import create_access_token
from app.models.user import User
from app.services.twitter_service import TwitterService
from app.services.twitter_oauth_service import TwitterOAuthService

router = APIRouter()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token")


def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    return pwd_context.hash(password)





def get_user(db: Session, username: str):
    return db.query(User).filter(User.username == username).first()


def authenticate_user(db: Session, username: str, password: str):
    user = get_user(db, username)
    if not user:
        return False
    if not verify_password(password, user.hashed_password):
        return False
    return user


async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = get_user(db, username=username)
    if user is None:
        raise credentials_exception
    return user


@router.post("/token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/me")
async def read_users_me(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "twitter_username": current_user.twitter_username,
        "twitter_user_id": current_user.twitter_user_id,
        "is_active": current_user.is_active,
        "created_at": current_user.created_at.isoformat() if current_user.created_at else None,
        "updated_at": current_user.updated_at.isoformat() if current_user.updated_at else None
    }

# Twitter OAuth Models


class TwitterAuthResponse(BaseModel):
    authorization_url: str
    oauth_token: str
    oauth_token_secret: str


class TwitterCallbackRequest(BaseModel):
    oauth_token: str
    oauth_verifier: str
    oauth_token_secret: str  # Add this field

# Twitter OAuth 2.0 Models for User Authentication





class TwitterOAuth2InitResponse(BaseModel):
    authorization_url: str
    state: str
    code_verifier: str


class TwitterOAuth2CallbackRequest(BaseModel):
    code: str
    state: str
    code_verifier: str


class TwitterOAuth2CallbackResponse(BaseModel):
    access_token: str
    token_type: str
    user: dict

# Twitter OAuth Endpoints


@router.get("/twitter/login")
async def twitter_login(current_user: User = Depends(get_current_user)):
    """Initiate Twitter OAuth flow"""
    twitter_service = TwitterService(
        api_key=settings.TWITTER_API_KEY,
        api_secret=settings.TWITTER_API_SECRET
    )

    callback_url = f"{settings.FRONTEND_URL}/auth/twitter/callback"
    result = twitter_service.get_oauth_url(callback_url)

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Twitter authorization URL: {result['error']}"
        )

    # Store oauth_token_secret in session or database for later use
    # For now, we'll return it to the frontend to handle
    return TwitterAuthResponse(
        authorization_url=result["authorization_url"],
        oauth_token=result["oauth_token"],
        oauth_token_secret=result["oauth_token_secret"]
    )


@router.post("/twitter/callback")
async def twitter_callback(
    callback_data: TwitterCallbackRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Handle Twitter OAuth callback"""
    twitter_service = TwitterService(
        api_key=settings.TWITTER_API_KEY,
        api_secret=settings.TWITTER_API_SECRET
    )

    # Exchange OAuth verifier for access tokens
    result = twitter_service.get_access_tokens(
        oauth_token=callback_data.oauth_token,
        oauth_token_secret=callback_data.oauth_token_secret,
        oauth_verifier=callback_data.oauth_verifier
    )

    if not result["success"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Darn, Failed to get Twitter access tokens: {result['error']}"
        )

    # Update user with Twitter credentials
    current_user.twitter_access_token = result["access_token"]
    current_user.twitter_refresh_token = result["access_token_secret"]  # Note: OAuth 1.0a uses access_token_secret

    # Get Twitter user info
    try:
        user_client = TwitterService(
            api_key=settings.TWITTER_API_KEY,
            api_secret=settings.TWITTER_API_SECRET,
            access_token=result["access_token"],
            access_token_secret=result["access_token_secret"]
        )
        user_info = user_client.get_current_user_info()
        if user_info and user_info.get("success"):
            current_user.twitter_user_id = str(user_info["data"]["id"])
            current_user.twitter_username = user_info["data"]["username"]
    except Exception as e:
        # Log error but don't fail the authentication
        print(f"Failed to get Twitter user info: {e}")

    db.commit()

    return {
        "message": "Twitter account connected successfully",
        "twitter_username": current_user.twitter_username,
        "twitter_user_id": current_user.twitter_user_id
    }


@router.get("/twitter/status")
async def get_twitter_status(current_user: User = Depends(get_current_user)):
    """Get Twitter connection status"""
    return {
        "connected": bool(current_user.twitter_access_token),
        "twitter_username": current_user.twitter_username,
        "twitter_user_id": current_user.twitter_user_id
    }


@router.delete("/twitter/disconnect")
async def disconnect_twitter(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Disconnect Twitter account"""
    current_user.twitter_access_token = None
    current_user.twitter_refresh_token = None
    current_user.twitter_user_id = None
    current_user.twitter_username = None
    db.commit()

    return {"message": "Twitter account disconnected successfully"}

# Twitter OAuth 2.0 User Authentication Endpoints


@router.get("/oauth2/twitter/debug")
async def twitter_oauth2_debug():
    """Debug endpoint to check Twitter OAuth 2.0 configuration"""
    return {
        "client_id": settings.TWITTER_CLIENT_ID[:10] + "..." if settings.TWITTER_CLIENT_ID else "NOT_SET",
        "client_secret": "SET" if settings.TWITTER_CLIENT_SECRET else "NOT_SET",
        "bearer_token": "SET" if settings.TWITTER_BEARER_TOKEN else "NOT_SET",
        "redirect_uri": settings.TWITTER_OAUTH_REDIRECT_URI,
        "frontend_url": settings.FRONTEND_URL,
        "oauth_scope": TwitterOAuthService.OAUTH_SCOPE
    }


@router.post("/oauth2/twitter/init")
async def init_twitter_oauth2():
    """Initialize Twitter OAuth 2.0 flow with PKCE."""
    oauth_data = TwitterOAuthService.init_oauth_flow()

    return TwitterOAuth2InitResponse(
        authorization_url=oauth_data["authorization_url"],
        state=oauth_data["state"],
        code_verifier=oauth_data["code_verifier"]
    )


@router.post("/oauth2/twitter/callback")
async def twitter_oauth2_callback(
    callback_data: TwitterOAuth2CallbackRequest,
    db: Session = Depends(get_db)
):
    """Handle Twitter OAuth 2.0 callback and authenticate/create user"""
    try:
        jwt_token, user_data = await TwitterOAuthService.handle_oauth_callback(
            db=db,
            code=callback_data.code,
            state=callback_data.state,
            code_verifier=callback_data.code_verifier
        )

        return TwitterOAuth2CallbackResponse(
            access_token=jwt_token,
            token_type="bearer",
            user=user_data
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Authentication failed: {str(e)}"
        )


