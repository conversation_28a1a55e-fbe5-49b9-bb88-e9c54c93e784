(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[662],{1125:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(5155),n=r(6874),a=r.n(n);let o=e=>{let{href:t="/dashboard",size:r="md",showText:n=!0}=e,o={sm:{icon:"w-6 h-6",text:"text-lg"},md:{icon:"w-8 h-8",text:"text-xl"},lg:{icon:"w-10 h-10",text:"text-2xl"}},i=(0,s.jsxs)("div",{className:"flex items-center space-x-2 select-none",children:[(0,s.jsx)("div",{className:"".concat(o[r].icon," bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0"),children:(0,s.jsx)("span",{className:"text-white font-bold ".concat("sm"===r?"text-sm":"text-lg"),children:"A"})}),n&&(0,s.jsx)("span",{className:"".concat(o[r].text," font-bold text-gray-900 whitespace-nowrap"),children:"AutoReach"})]});return t?(0,s.jsx)(a(),{href:t,children:i}):i}},2178:(e,t,r)=>{"use strict";r.d(t,{b:()=>a});var s=r(4205);class n{async initiateTwitterAuth(){try{return await s.uE.get("/auth/twitter/login")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to initiate Twitter auth: ".concat(e))}}async handleTwitterCallback(e){try{return await s.uE.post("/auth/twitter/callback",e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to handle Twitter callback: ".concat(e))}}async getTwitterStatus(){try{return await s.uE.get("/auth/twitter/status")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to get Twitter status: ".concat(e))}}async disconnectTwitter(){try{return await s.uE.delete("/auth/twitter/disconnect")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to disconnect Twitter: ".concat(e))}}async startTwitterOAuth(){let e=await this.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret);let t=window.open(e.authorization_url,"twitter_auth","width=600,height=600,scrollbars=yes,resizable=yes");return new Promise((r,s)=>{let n=setInterval(()=>{(null==t?void 0:t.closed)&&(clearInterval(n),s(Error("Twitter authorization was cancelled")))},1e3),a=o=>{o.origin===window.location.origin&&("TWITTER_AUTH_SUCCESS"===o.data.type?(clearInterval(n),window.removeEventListener("message",a),null==t||t.close(),r({oauth_token:e.oauth_token,oauth_token_secret:e.oauth_token_secret})):"TWITTER_AUTH_ERROR"===o.data.type&&(clearInterval(n),window.removeEventListener("message",a),null==t||t.close(),s(Error(o.data.error||"Twitter authorization failed"))))};window.addEventListener("message",a)})}async completeTwitterOAuth(e){let t=sessionStorage.getItem("twitter_oauth_token_secret");if(!t)throw Error("OAuth token secret not found. Please restart the authentication process.");let r=new URLSearchParams(window.location.search).get("oauth_token");if(!r)throw Error("OAuth token not found in callback URL");let s=await this.handleTwitterCallback({oauth_token:r,oauth_verifier:e,oauth_token_secret:t});return sessionStorage.removeItem("twitter_oauth_token_secret"),s}}let a=new n},3371:(e,t,r)=>{Promise.resolve().then(r.bind(r,7864)),Promise.resolve().then(r.bind(r,4590))},4590:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(5155),n=r(2115),a=r(2178),o=r(3851);function i(e){let{onStatusChange:t}=e,[r,i]=(0,n.useState)({connected:!1}),[c,l]=(0,n.useState)(!1),[d,h]=(0,n.useState)(null),[u,x]=(0,n.useState)(!1),m=(0,n.useCallback)(async()=>{try{h(null);let e=await a.b.getTwitterStatus();i(e),null==t||t(e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";h("Failed to load Twitter status: ".concat(e))}},[t]);(0,n.useEffect)(()=>{x(o.y.isAuthenticated()),o.y.isAuthenticated()&&m()},[m]);let g=async()=>{if(!u)return void h("Please log in first to connect your Twitter account");l(!0),h(null);try{let e=await a.b.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret),sessionStorage.setItem("twitter_oauth_token",e.oauth_token),window.location.href=e.authorization_url}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";h("Failed to connect Twitter: ".concat(e)),l(!1)}},w=async()=>{l(!0),h(null);try{await a.b.disconnectTwitter();let e={connected:!1};i(e),null==t||t(e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";h("Failed to disconnect Twitter: ".concat(e))}finally{l(!1)}};return u?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-twitter-blue rounded-lg flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:"Twitter"}),r.connected?(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["@",r.twitter_username]}):(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Not connected"})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-3",children:r.connected?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Connected"}),(0,s.jsx)("button",{onClick:w,disabled:c,className:"px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50 disabled:opacity-50",children:c?"Disconnecting...":"Disconnect"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"Not connected"}),(0,s.jsx)("button",{onClick:g,disabled:c,className:"px-3 py-1 text-sm text-white bg-twitter-blue rounded hover:bg-blue-600 disabled:opacity-50",children:c?"Connecting...":"Connect"})]})})]}),d&&(0,s.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{className:"text-sm text-red-800",children:d})]})}),r.connected&&(0,s.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("span",{className:"text-sm text-green-800",children:"Twitter account connected successfully! You can now create and schedule tweets."})]})})]}):(0,s.jsx)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(0,s.jsx)("span",{className:"text-yellow-800",children:"Please log in to connect your Twitter account"})]})})}},7864:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(5155),n=r(6874),a=r.n(n),o=r(5695),i=r(2115),c=r(4611),l=r(1125),d=r(8794);let h=e=>{var t,r;let{showNavigation:n=!0}=e,h=(0,o.usePathname)(),[u,x]=(0,i.useState)(!1),[m,g]=(0,i.useState)(!1),{user:w,logout:p,isAuthenticated:b}=(0,d.A)();return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 relative z-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(l.A,{})}),n&&(0,s.jsx)("nav",{className:"hidden md:flex space-x-6",children:c.Ij.map(e=>(0,s.jsx)(a(),{href:e.path,className:"px-3 py-2 rounded-md text-sm font-medium transition-colors ".concat(h===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"," ").concat("Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100":""),children:e.name},e.path))}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[n&&(0,s.jsx)("button",{onClick:()=>x(!u),className:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle mobile menu",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:u?(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),b?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>g(!m),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-medium text-purple-600",children:(null==w||null==(t=w.full_name)?void 0:t[0])||(null==w||null==(r=w.username)?void 0:r[0])||"U"})}),(0,s.jsx)("span",{className:"hidden sm:block text-sm font-medium",children:(null==w?void 0:w.full_name)||(null==w?void 0:w.username)}),(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),m&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,s.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 border-b border-gray-100",children:[(0,s.jsx)("p",{className:"font-medium",children:(null==w?void 0:w.full_name)||(null==w?void 0:w.username)}),(null==w?void 0:w.twitter_username)&&(0,s.jsxs)("p",{className:"text-gray-500",children:["@",w.twitter_username]})]}),(0,s.jsx)(a(),{href:"/settings",onClick:()=>g(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Settings"}),(0,s.jsx)("button",{onClick:()=>{g(!1),p()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})]}):(0,s.jsx)(a(),{href:"/login",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})]})]}),n&&u&&(0,s.jsx)("div",{className:"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,s.jsx)("nav",{className:"px-4 py-4 space-y-2",children:c.Ij.map(e=>(0,s.jsx)(a(),{href:e.path,onClick:()=>x(!1),className:"block px-4 py-3 rounded-lg text-base font-medium transition-colors ".concat(h===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"," ").concat("Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100 border border-green-200":""),children:(0,s.jsxs)("span",{className:"flex items-center gap-3",children:[(0,s.jsxs)("span",{className:"text-lg",children:["Dashboard"===e.name&&"\uD83D\uDCCA","Content"===e.name&&"✍️","Analytics"===e.name&&"\uD83D\uDCC8","Settings"===e.name&&"⚙️","Auth"===e.name&&"\uD83D\uDD10","Test API"===e.name&&"\uD83E\uDDEA"]}),e.name]})},e.path))})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,794,441,684,358],()=>t(3371)),_N_E=e.O()}]);