{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';\n  size?: 'sm' | 'md' | 'lg';\n  isLoading?: boolean;\n  asChild?: boolean;\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n\n    const variants = {\n      primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',\n      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-sm',\n      lg: 'px-6 py-3 text-base',\n    };\n\n    return (\n      <button\n        ref={ref}\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading && (\n          <svg className=\"w-4 h-4 mr-2 animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n          </svg>\n        )}\n        {children}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AASA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,2BACC,6LAAC;gBAAI,WAAU;gBAA4B,MAAK;gBAAO,SAAQ;;kCAC7D,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/Button';\n\nexport default function Home() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    setIsVisible(true);\n  }, []);\n\n\n\n  const features = [\n    {\n      icon: \"🤖\",\n      title: \"AI-Powered Content\",\n      description: \"Generate viral tweets that match your voice and engage your audience\",\n      gradient: \"from-purple-500 to-pink-500\"\n    },\n    {\n      icon: \"⚡\",\n      title: \"Smart Automation\",\n      description: \"Schedule, engage, and grow your audience while you sleep\",\n      gradient: \"from-blue-500 to-cyan-500\"\n    },\n    {\n      icon: \"📊\",\n      title: \"Growth Analytics\",\n      description: \"Track what works and optimize your strategy with detailed insights\",\n      gradient: \"from-green-500 to-emerald-500\"\n    },\n    {\n      icon: \"🎯\",\n      title: \"Targeted Engagement\",\n      description: \"Find and connect with your ideal audience automatically\",\n      gradient: \"from-orange-500 to-red-500\"\n    },\n    {\n      icon: \"💎\",\n      title: \"Token Economy\",\n      description: \"Pay only for what you use - no wasteful monthly subscriptions\",\n      gradient: \"from-indigo-500 to-purple-500\"\n    },\n    {\n      icon: \"🚀\",\n      title: \"Viral Optimization\",\n      description: \"AI analyzes trending patterns to maximize your reach\",\n      gradient: \"from-pink-500 to-rose-500\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse\"></div>\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-2000\"></div>\n      </div>\n\n      {/* Custom Dark Header */}\n      <header className=\"relative z-50 bg-transparent\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16 sm:h-20\">\n            {/* Logo */}\n            <div className=\"flex items-center\">\n              <div className=\"text-white font-bold text-xl sm:text-2xl\">\n                <span className=\"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\">\n                  Reachly\n                </span>\n              </div>\n            </div>\n\n            {/* Desktop Navigation - Hidden for landing page focus */}\n            <nav className=\"hidden md:flex items-center space-x-4 lg:space-x-6\">\n              {/* Only show login button on landing page */}\n              <Link\n                href=\"/login\"\n                className=\"px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg text-sm font-medium transition-all duration-300 hover:transform hover:scale-105 shadow-lg hover:shadow-purple-500/25\"\n              >\n                🚀 Sign In with Twitter\n              </Link>\n            </nav>\n\n            {/* Mobile login button - replaces hamburger menu */}\n            <div className=\"md:hidden\">\n              <Link\n                href=\"/login\"\n                className=\"px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg text-sm font-medium transition-all duration-300 shadow-lg\"\n              >\n                🚀 Sign In\n              </Link>\n            </div>\n          </div>\n\n          {/* Mobile Navigation Menu - Hidden for landing page focus */}\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <main className=\"relative z-10\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16\">\n          <div className={`text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\n\n            {/* Brand Name */}\n            <div className=\"mb-8\">\n              <h1 className=\"text-6xl sm:text-7xl md:text-8xl lg:text-9xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent leading-tight\">\n                Reachly\n              </h1>\n            </div>\n\n            {/* Main Headline */}\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white leading-tight px-4 sm:px-0\">\n              AI-Powered Twitter Growth\n            </h2>\n\n            {/* Simple Value Proposition */}\n            <p className=\"text-lg sm:text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed px-4 sm:px-0\">\n              Create viral content, automate engagement, and grow your audience while you focus on what matters.\n            </p>\n\n            {/* Key Features */}\n            <div className=\"flex flex-col sm:flex-row justify-center gap-6 sm:gap-8 mb-8 text-sm\">\n              <div className=\"flex items-center gap-2 text-green-400\">\n                <span className=\"text-lg\">💰</span>\n                <span>Pay-per-use pricing</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-blue-400\">\n                <span className=\"text-lg\">🤖</span>\n                <span>AI learns your voice</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-purple-400\">\n                <span className=\"text-lg\">⚡</span>\n                <span>Automated growth</span>\n              </div>\n            </div>\n\n            {/* Free Tier Highlight */}\n            <div className=\"bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-2xl p-6 mb-8 border border-green-500/30 max-w-2xl mx-auto\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-2\">🎁</div>\n                <h3 className=\"text-xl font-bold text-green-300 mb-2\">Start Free</h3>\n                <p className=\"text-green-100 mb-3\">Get 100 free tokens when you join</p>\n                <div className=\"text-sm text-green-200\">\n                  ✓ Generate ~10 AI tweets • ✓ Auto-schedule posts • ✓ Basic analytics\n                </div>\n              </div>\n            </div>\n\n            {/* Primary CTA */}\n            <div className=\"mb-12\">\n              <Button\n                size=\"lg\"\n                className=\"bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 sm:px-12 py-4 sm:py-6 text-lg sm:text-xl font-bold rounded-xl shadow-2xl hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105\"\n              >\n                <Link href=\"/login\" className=\"flex items-center gap-3\">\n                  🚀 Get 100 Free Tokens\n                </Link>\n              </Button>\n              <p className=\"text-sm text-gray-400 mt-3\">Connect with Twitter • No credit card required • Start creating in 30 seconds</p>\n            </div>\n\n\n\n\n          </div>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\n          <div className=\"text-center mb-12 sm:mb-16\">\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent px-4 sm:px-0\">\n              <span className=\"block sm:inline\">Everything You Need to</span>\n              <span className=\"block sm:inline bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\"> Dominate Twitter</span>\n            </h2>\n            <p className=\"text-base sm:text-lg lg:text-xl text-gray-400 max-w-3xl mx-auto px-4 sm:px-0\">\n              Stop using 5 different tools. AutoReach is the only Twitter growth platform you&apos;ll ever need.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8\">\n            {features.map((feature, index) => (\n              <div\n                key={index}\n                className={`group relative bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-500 hover:transform hover:scale-105 ${\n                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n                }`}\n                style={{ transitionDelay: `${index * 100}ms` }}\n              >\n                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                  {feature.icon}\n                </div>\n                <h3 className=\"text-xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors\">\n                  {feature.title}\n                </h3>\n                <p className=\"text-gray-400 leading-relaxed group-hover:text-gray-300 transition-colors\">\n                  {feature.description}\n                </p>\n\n                {/* Hover effect overlay */}\n                <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Token Pricing Section */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\n          <div className=\"text-center mb-12 sm:mb-16\">\n            <div className=\"inline-flex items-center px-3 sm:px-4 py-2 rounded-full bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-500/30 backdrop-blur-sm mb-6 sm:mb-8\">\n              <span className=\"text-xs sm:text-sm font-medium text-green-200\">💎 Revolutionary Token-Based Pricing</span>\n            </div>\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent px-4 sm:px-0\">\n              <span className=\"block sm:inline\">Pay for Results,</span>\n              <span className=\"block sm:inline bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent\"> Not Time</span>\n            </h2>\n            <p className=\"text-base sm:text-lg lg:text-xl text-gray-400 max-w-3xl mx-auto mb-8 sm:mb-12 px-4 sm:px-0\">\n              No more wasted monthly fees. Buy tokens, use them when you need them.\n              Scale up or down based on your actual usage.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 max-w-7xl mx-auto\">\n            {/* Free Tier */}\n            <div className=\"bg-gradient-to-b from-green-500/10 to-emerald-500/10 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border-2 border-green-500/50 relative hover:border-green-400 transition-all duration-300\">\n              <div className=\"absolute -top-3 sm:-top-4 left-1/2 transform -translate-x-1/2\">\n                <div className=\"bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 sm:px-4 py-1 sm:py-2 rounded-full text-xs sm:text-sm font-bold\">\n                  🎁 FREE\n                </div>\n              </div>\n              <div className=\"text-center pt-4 sm:pt-0\">\n                <div className=\"text-3xl sm:text-4xl mb-3 sm:mb-4\">🚀</div>\n                <h3 className=\"text-xl sm:text-2xl font-bold text-white mb-2\">Free Starter</h3>\n                <div className=\"text-3xl sm:text-4xl font-bold text-green-400 mb-2\">$0</div>\n                <div className=\"text-sm sm:text-base text-gray-400 mb-4 sm:mb-6\">100 free tokens</div>\n                <div className=\"space-y-2 sm:space-y-3 text-left mb-6 sm:mb-8\">\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>~10 AI-generated tweets</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Basic scheduling</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Basic analytics</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Try all features</span>\n                  </div>\n                </div>\n                <Button className=\"w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl py-2 sm:py-3 text-sm sm:text-base\">\n                  Start Free\n                </Button>\n              </div>\n            </div>\n\n            {/* Starter Pack */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-white/10 hover:border-purple-500/50 transition-all duration-300\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl sm:text-4xl mb-3 sm:mb-4\">🌱</div>\n                <h3 className=\"text-xl sm:text-2xl font-bold text-white mb-2\">Starter Pack</h3>\n                <div className=\"text-3xl sm:text-4xl font-bold text-purple-400 mb-2\">$29</div>\n                <div className=\"text-sm sm:text-base text-gray-400 mb-4 sm:mb-6\">1,000 tokens</div>\n                <div className=\"space-y-2 sm:space-y-3 text-left mb-6 sm:mb-8\">\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>~100 AI-generated tweets</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Smart scheduling</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Basic analytics</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Auto-engagement</span>\n                  </div>\n                </div>\n                <Button className=\"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl py-2 sm:py-3 text-sm sm:text-base\">\n                  Get Started\n                </Button>\n              </div>\n            </div>\n\n            {/* Growth Pack - Popular */}\n            <div className=\"bg-gradient-to-b from-purple-500/10 to-pink-500/10 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border-2 border-purple-500/50 relative hover:border-purple-400 transition-all duration-300 md:transform md:scale-105\">\n              <div className=\"absolute -top-3 sm:-top-4 left-1/2 transform -translate-x-1/2\">\n                <div className=\"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 sm:px-4 py-1 sm:py-2 rounded-full text-xs sm:text-sm font-bold\">\n                  🔥 MOST POPULAR\n                </div>\n              </div>\n              <div className=\"text-center pt-4 sm:pt-0\">\n                <div className=\"text-3xl sm:text-4xl mb-3 sm:mb-4\">🚀</div>\n                <h3 className=\"text-xl sm:text-2xl font-bold text-white mb-2\">Growth Pack</h3>\n                <div className=\"text-3xl sm:text-4xl font-bold text-purple-400 mb-2\">$79</div>\n                <div className=\"text-sm sm:text-base text-gray-400 mb-4 sm:mb-6\">3,000 tokens</div>\n                <div className=\"space-y-2 sm:space-y-3 text-left mb-6 sm:mb-8\">\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>~300 AI-generated tweets</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Advanced scheduling</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Detailed analytics</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Smart auto-engagement</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Viral optimization</span>\n                  </div>\n                </div>\n                <Button className=\"w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl py-2 sm:py-3 text-sm sm:text-base\">\n                  Start Growing\n                </Button>\n              </div>\n            </div>\n\n            {/* Pro Pack */}\n            <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-white/10 hover:border-yellow-500/50 transition-all duration-300\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl sm:text-4xl mb-3 sm:mb-4\">👑</div>\n                <h3 className=\"text-xl sm:text-2xl font-bold text-white mb-2\">Pro Pack</h3>\n                <div className=\"text-3xl sm:text-4xl font-bold text-yellow-400 mb-2\">$199</div>\n                <div className=\"text-sm sm:text-base text-gray-400 mb-4 sm:mb-6\">10,000 tokens</div>\n                <div className=\"space-y-2 sm:space-y-3 text-left mb-6 sm:mb-8\">\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>~1,000 AI-generated tweets</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Everything in Growth</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Priority support</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Custom AI training</span>\n                  </div>\n                  <div className=\"flex items-center gap-2 sm:gap-3 text-gray-300 text-sm sm:text-base\">\n                    <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                    <span>Advanced targeting</span>\n                  </div>\n                </div>\n                <Button className=\"w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white rounded-xl py-2 sm:py-3 text-sm sm:text-base\">\n                  Go Pro\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Token Benefits */}\n          <div className=\"mt-12 sm:mt-16 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-2xl p-6 sm:p-8 border border-green-500/20\">\n            <div className=\"text-center mb-6 sm:mb-8\">\n              <h3 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-white mb-2 sm:mb-4 px-4 sm:px-0\">Why Tokens Beat Monthly Subscriptions</h3>\n            </div>\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl sm:text-3xl mb-3 sm:mb-4\">💰</div>\n                <h4 className=\"text-base sm:text-lg font-semibold text-white mb-2\">Pay Only for Usage</h4>\n                <p className=\"text-sm sm:text-base text-gray-400\">No wasted money on unused features. Tokens never expire.</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl sm:text-3xl mb-3 sm:mb-4\">📈</div>\n                <h4 className=\"text-base sm:text-lg font-semibold text-white mb-2\">Scale Flexibly</h4>\n                <p className=\"text-sm sm:text-base text-gray-400\">Ramp up during campaigns, scale down during breaks.</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl sm:text-3xl mb-3 sm:mb-4\">🎯</div>\n                <h4 className=\"text-base sm:text-lg font-semibold text-white mb-2\">Better ROI</h4>\n                <p className=\"text-sm sm:text-base text-gray-400\">Invest in growth when you need it, not on a schedule.</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Comparison Section */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\n          <div className=\"text-center mb-12 sm:mb-16\">\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent px-4 sm:px-0\">\n              <span className=\"block sm:inline\">Why Choose Reachly Over</span>\n              <span className=\"block sm:inline bg-gradient-to-r from-red-400 to-orange-400 bg-clip-text text-transparent\"> The Competition?</span>\n            </h2>\n          </div>\n\n          <div className=\"bg-white/5 backdrop-blur-sm rounded-2xl p-4 sm:p-6 lg:p-8 border border-white/10\">\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full min-w-[600px]\">\n                <thead>\n                  <tr className=\"border-b border-white/10\">\n                    <th className=\"text-left py-3 sm:py-4 px-3 sm:px-6 text-white font-semibold text-sm sm:text-base\">Feature</th>\n                    <th className=\"text-center py-3 sm:py-4 px-2 sm:px-6 text-purple-400 font-bold text-sm sm:text-base\">Reachly</th>\n                    <th className=\"text-center py-3 sm:py-4 px-2 sm:px-6 text-gray-400 text-sm sm:text-base\">TweetHunter</th>\n                    <th className=\"text-center py-3 sm:py-4 px-2 sm:px-6 text-gray-400 text-sm sm:text-base\">Buffer</th>\n                    <th className=\"text-center py-3 sm:py-4 px-2 sm:px-6 text-gray-400 text-sm sm:text-base\">Hootsuite</th>\n                  </tr>\n                </thead>\n                <tbody className=\"text-xs sm:text-sm\">\n                  <tr className=\"border-b border-white/5\">\n                    <td className=\"py-3 sm:py-4 px-3 sm:px-6 text-gray-300\">Token-based pricing</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-green-400\">✓</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                  </tr>\n                  <tr className=\"border-b border-white/5\">\n                    <td className=\"py-3 sm:py-4 px-3 sm:px-6 text-gray-300\">AI content generation</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-green-400\">✓</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-green-400\">✓</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                  </tr>\n                  <tr className=\"border-b border-white/5\">\n                    <td className=\"py-3 sm:py-4 px-3 sm:px-6 text-gray-300\">Smart auto-engagement</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-green-400\">✓</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-yellow-400\">Limited</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                  </tr>\n                  <tr className=\"border-b border-white/5\">\n                    <td className=\"py-3 sm:py-4 px-3 sm:px-6 text-gray-300\">Viral optimization</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-green-400\">✓</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-red-400\">✗</td>\n                  </tr>\n                  <tr className=\"border-b border-white/5\">\n                    <td className=\"py-3 sm:py-4 px-3 sm:px-6 text-gray-300\">Starting price</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-green-400 font-bold\">FREE</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-gray-400\">$49/mo</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-gray-400\">$15/mo</td>\n                    <td className=\"py-3 sm:py-4 px-2 sm:px-6 text-center text-gray-400\">$99/mo</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n\n        {/* Final CTA Section */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20\">\n          <div className=\"bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 border border-purple-500/30 text-center\">\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent px-4 sm:px-0\">\n              <span className=\"block sm:inline\">Ready to Build Your</span>\n              <span className=\"block sm:inline bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\"> Twitter Empire?</span>\n            </h2>\n            <p className=\"text-base sm:text-lg lg:text-xl text-gray-300 mb-6 sm:mb-8 max-w-3xl mx-auto px-4 sm:px-0\">\n              Join the creators building their Twitter presence with Reachly.\n              Start completely free and watch your audience grow.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-6 sm:mb-8\">\n              <Button\n                size=\"lg\"\n                className=\"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 sm:px-12 py-4 sm:py-6 text-lg sm:text-xl font-bold rounded-xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-105\"\n              >\n                <Link href=\"/login\" className=\"flex items-center gap-2 sm:gap-3\">\n                  🚀 Start Your Journey\n                </Link>\n              </Button>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row flex-wrap justify-center gap-4 sm:gap-8 text-xs sm:text-sm text-gray-400\">\n              <div className=\"flex items-center justify-center gap-2\">\n                <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                <span>No credit card required</span>\n              </div>\n              <div className=\"flex items-center justify-center gap-2\">\n                <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                <span>Start with free tokens</span>\n              </div>\n              <div className=\"flex items-center justify-center gap-2\">\n                <span className=\"text-green-400 flex-shrink-0\">✓</span>\n                <span>Cancel anytime</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <footer className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12 border-t border-white/10\">\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8\">\n            <div className=\"sm:col-span-2 lg:col-span-1\">\n              <h3 className=\"text-white font-bold text-lg mb-3 sm:mb-4\">Reachly</h3>\n              <p className=\"text-gray-400 text-sm mb-3 sm:mb-4\">\n                The AI-powered Twitter growth platform that helps creators build their empire.\n              </p>\n              <div className=\"flex gap-3 sm:gap-4\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 transition-colors\">\n                  <span className=\"text-lg sm:text-xl\">🐦</span>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 transition-colors\">\n                  <span className=\"text-lg sm:text-xl\">💼</span>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-purple-400 transition-colors\">\n                  <span className=\"text-lg sm:text-xl\">📧</span>\n                </a>\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"text-white font-semibold mb-3 sm:mb-4 text-sm sm:text-base\">Product</h4>\n              <ul className=\"space-y-1 sm:space-y-2 text-xs sm:text-sm\">\n                <li><Link href=\"/dashboard\" className=\"text-gray-400 hover:text-white transition-colors\">Dashboard</Link></li>\n                <li><Link href=\"/content\" className=\"text-gray-400 hover:text-white transition-colors\">Content</Link></li>\n                <li><Link href=\"/analytics\" className=\"text-gray-400 hover:text-white transition-colors\">Analytics</Link></li>\n                <li><Link href=\"/settings\" className=\"text-gray-400 hover:text-white transition-colors\">Settings</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-white font-semibold mb-3 sm:mb-4 text-sm sm:text-base\">Support</h4>\n              <ul className=\"space-y-1 sm:space-y-2 text-xs sm:text-sm\">\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Help Center</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Contact Us</a></li>\n                <li><Link href=\"/test-connection\" className=\"text-gray-400 hover:text-white transition-colors\">System Status</Link></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">API Docs</a></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"text-white font-semibold mb-3 sm:mb-4 text-sm sm:text-base\">Company</h4>\n              <ul className=\"space-y-1 sm:space-y-2 text-xs sm:text-sm\">\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">About</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Privacy</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Terms</a></li>\n                <li><a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">Blog</a></li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"border-t border-white/10 mt-8 sm:mt-12 pt-6 sm:pt-8 text-center\">\n            <p className=\"text-gray-400 text-xs sm:text-sm\">\n              © 2024 Reachly. All rights reserved. Built with ❤️ for Twitter creators.\n            </p>\n          </div>\n        </footer>\n      </main>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,aAAa;QACf;yBAAG,EAAE;IAIL,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;QACZ;QACA;YACE,MAAM;YACN,OAAO;YAC<PERSON>,aAAa;YAC<PERSON>,UAAU;QACZ;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;QACZ;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;;;;;0CAOjG,6LAAC;gCAAI,WAAU;0CAEb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWT,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAW,CAAC,yCAAyC,EAAE,YAAY,8BAA8B,4BAA4B;;8CAGhI,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAA+J;;;;;;;;;;;8CAM/K,6LAAC;oCAAG,WAAU;8CAAoG;;;;;;8CAKlH,6LAAC;oCAAE,WAAU;8CAAuF;;;;;;8CAKpG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;8CAKV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAI,WAAU;0DAAyB;;;;;;;;;;;;;;;;;8CAO5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA0B;;;;;;;;;;;sDAI1D,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAUhD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;0DAAkB;;;;;;0DAClC,6LAAC;gDAAK,WAAU;0DAA6F;;;;;;;;;;;;kDAE/G,6LAAC;wCAAE,WAAU;kDAA+E;;;;;;;;;;;;0CAK9F,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;wCAEC,WAAW,CAAC,oKAAoK,EAC9K,YAAY,8BAA8B,4BAC1C;wCACF,OAAO;4CAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;wCAAC;;0DAE7C,6LAAC;gDAAI,WAAW,CAAC,uCAAuC,EAAE,QAAQ,QAAQ,CAAC,uGAAuG,CAAC;0DAChL,QAAQ,IAAI;;;;;;0DAEf,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,6LAAC;gDAAI,WAAW,CAAC,8CAA8C,EAAE,QAAQ,QAAQ,CAAC,gEAAgE,CAAC;;;;;;;uCAjB9I;;;;;;;;;;;;;;;;kCAwBb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAgD;;;;;;;;;;;kDAElE,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;0DAAkB;;;;;;0DAClC,6LAAC;gDAAK,WAAU;0DAA+F;;;;;;;;;;;;kDAEjH,6LAAC;wCAAE,WAAU;kDAA6F;;;;;;;;;;;;0CAM5G,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAAgI;;;;;;;;;;;0DAIjJ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAG,WAAU;kEAAgD;;;;;;kEAC9D,6LAAC;wDAAI,WAAU;kEAAqD;;;;;;kEACpE,6LAAC;wDAAI,WAAU;kEAAkD;;;;;;kEACjE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAGV,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;kEAA0J;;;;;;;;;;;;;;;;;;kDAOhL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAAsD;;;;;;8DACrE,6LAAC;oDAAI,WAAU;8DAAkD;;;;;;8DACjE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;8DAGV,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;8DAAsJ;;;;;;;;;;;;;;;;;kDAO5K,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAA8H;;;;;;;;;;;0DAI/I,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAG,WAAU;kEAAgD;;;;;;kEAC9D,6LAAC;wDAAI,WAAU;kEAAsD;;;;;;kEACrE,6LAAC;wDAAI,WAAU;kEAAkD;;;;;;kEACjE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAA+B;;;;;;kFAC/C,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;kEAGV,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;kEAAsJ;;;;;;;;;;;;;;;;;;kDAO5K,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAAsD;;;;;;8DACrE,6LAAC;oDAAI,WAAU;8DAAkD;;;;;;8DACjE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAA+B;;;;;;8EAC/C,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;8DAGV,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;8DAA0J;;;;;;;;;;;;;;;;;;;;;;;0CAQlL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAgF;;;;;;;;;;;kDAEhG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAG,WAAU;kEAAqD;;;;;;kEACnE,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAG,WAAU;kEAAqD;;;;;;kEACnE,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;0DAEpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAG,WAAU;kEAAqD;;;;;;kEACnE,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;sDAClC,6LAAC;4CAAK,WAAU;sDAA4F;;;;;;;;;;;;;;;;;0CAIhH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAM,WAAU;;0DACf,6LAAC;0DACC,cAAA,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;sEAAoF;;;;;;sEAClG,6LAAC;4DAAG,WAAU;sEAAuF;;;;;;sEACrG,6LAAC;4DAAG,WAAU;sEAA2E;;;;;;sEACzF,6LAAC;4DAAG,WAAU;sEAA2E;;;;;;sEACzF,6LAAC;4DAAG,WAAU;sEAA2E;;;;;;;;;;;;;;;;;0DAG7F,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;0EAA0C;;;;;;0EACxD,6LAAC;gEAAG,WAAU;0EAAuD;;;;;;0EACrE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;0EACnE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;0EACnE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;;;;;;;kEAErE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;0EAA0C;;;;;;0EACxD,6LAAC;gEAAG,WAAU;0EAAuD;;;;;;0EACrE,6LAAC;gEAAG,WAAU;0EAAuD;;;;;;0EACrE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;0EACnE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;;;;;;;kEAErE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;0EAA0C;;;;;;0EACxD,6LAAC;gEAAG,WAAU;0EAAuD;;;;;;0EACrE,6LAAC;gEAAG,WAAU;0EAAwD;;;;;;0EACtE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;0EACnE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;;;;;;;kEAErE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;0EAA0C;;;;;;0EACxD,6LAAC;gEAAG,WAAU;0EAAuD;;;;;;0EACrE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;0EACnE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;0EACnE,6LAAC;gEAAG,WAAU;0EAAqD;;;;;;;;;;;;kEAErE,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;0EAA0C;;;;;;0EACxD,6LAAC;gEAAG,WAAU;0EAAiE;;;;;;0EAC/E,6LAAC;gEAAG,WAAU;0EAAsD;;;;;;0EACpE,6LAAC;gEAAG,WAAU;0EAAsD;;;;;;0EACpE,6LAAC;gEAAG,WAAU;0EAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;sDAClC,6LAAC;4CAAK,WAAU;sDAA6F;;;;;;;;;;;;8CAE/G,6LAAC;oCAAE,WAAU;8CAA4F;;;;;;8CAKzG,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAmC;;;;;;;;;;;;;;;;8CAMrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;8DAC/C,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;8DAC/C,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;8DAC/C,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAC1D,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEACpB,cAAA,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;kEAEvC,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEACpB,cAAA,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;kEAEvC,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEACpB,cAAA,6LAAC;4DAAK,WAAU;sEAAqB;;;;;;;;;;;;;;;;;;;;;;;kDAK3C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA6D;;;;;;0DAC3E,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;sEAAmD;;;;;;;;;;;kEACzF,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAW,WAAU;sEAAmD;;;;;;;;;;;kEACvF,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAa,WAAU;sEAAmD;;;;;;;;;;;kEACzF,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAY,WAAU;sEAAmD;;;;;;;;;;;;;;;;;;;;;;;kDAI5F,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA6D;;;;;;0DAC3E,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG,cAAA,6LAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAmD;;;;;;;;;;;kEAC7E,6LAAC;kEAAG,cAAA,6LAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAmD;;;;;;;;;;;kEAC7E,6LAAC;kEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAmB,WAAU;sEAAmD;;;;;;;;;;;kEAC/F,6LAAC;kEAAG,cAAA,6LAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAmD;;;;;;;;;;;;;;;;;;;;;;;kDAIjF,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA6D;;;;;;0DAC3E,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG,cAAA,6LAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAmD;;;;;;;;;;;kEAC7E,6LAAC;kEAAG,cAAA,6LAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAmD;;;;;;;;;;;kEAC7E,6LAAC;kEAAG,cAAA,6LAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAmD;;;;;;;;;;;kEAC7E,6LAAC;kEAAG,cAAA,6LAAC;4DAAE,MAAK;4DAAI,WAAU;sEAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKnF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;GAxiBwB;KAAA", "debugId": null}}]}