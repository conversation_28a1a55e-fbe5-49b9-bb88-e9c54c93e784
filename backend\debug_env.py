import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("=== ENVIRONMENT VARIABLES DEBUG ===")
print(f"TWITTER_CLIENT_ID: {os.getenv('TWITTER_CLIENT_ID', 'NOT SET')}")
print(f"TWITTER_CLIENT_SECRET: {os.getenv('TWITTER_CLIENT_SECRET', 'NOT SET')}")
print(f"TWITTER_OAUTH_REDIRECT_URI: {os.getenv('TWITTER_OAUTH_REDIRECT_URI', 'NOT SET')}")
print(f"Current working directory: {os.getcwd()}")
print(f".env file exists: {os.path.exists('.env')}")

if os.path.exists('.env'):
    with open('.env', 'r') as f:
        lines = f.readlines()
        print(f".env file has {len(lines)} lines")
        for i, line in enumerate(lines[:5]):  # Show first 5 lines
            if 'TWITTER' in line:
                print(f"Line {i+1}: {line.strip()}")