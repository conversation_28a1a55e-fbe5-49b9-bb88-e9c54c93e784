'use client';

import { useState } from 'react';
import { authService, TwitterOAuth2InitResponse } from '../lib/authService';

export default function DebugOAuth() {
  const [debugInfo, setDebugInfo] = useState<TwitterOAuth2InitResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const testOAuthInit = async () => {
    try {
      const result = await authService.initiateTwitterOAuth2();
      setDebugInfo(result);
      console.log('OAuth init result:', result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('OAuth init error:', err);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">OAuth Debug</h1>
      
      <button 
        onClick={testOAuthInit}
        className="bg-blue-500 text-white px-4 py-2 rounded mb-4"
      >
        Test OAuth Init
      </button>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {debugInfo && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="font-semibold mb-2">OAuth Init Response:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}