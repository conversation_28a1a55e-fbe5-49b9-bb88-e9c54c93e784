module.exports = {

"[project]/app/lib/validation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Frontend validation utilities following DRY and KISS principles.
 * Centralizes validation logic to eliminate code duplication.
 */ __turbopack_context__.s({
    "ContentValidation": (()=>ContentValidation),
    "FormValidation": (()=>FormValidation),
    "ValidationHooks": (()=>ValidationHooks),
    "ValidationSchemas": (()=>ValidationSchemas),
    "ValidationUtils": (()=>ValidationUtils),
    "validateContentGenerationForm": (()=>validateContentGenerationForm),
    "validateUserLoginForm": (()=>validateUserLoginForm),
    "validateUserRegistrationForm": (()=>validateUserRegistrationForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-ssr] (ecmascript)");
;
class ValidationUtils {
    /**
   * Validate required field
   */ static validateRequired(value, fieldName = 'This field') {
        if (value === null || value === undefined) {
            // If fieldName contains "is required" or similar, use it as is
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        if (typeof value === 'string' && !value.trim()) {
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        if (Array.isArray(value) && value.length === 0) {
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate string length
   */ static validateLength(value, minLength, maxLength) {
        if (typeof value !== 'string') {
            return {
                isValid: false,
                error: 'Value must be a string'
            };
        }
        const length = value.length;
        if (minLength !== undefined && length < minLength) {
            return {
                isValid: false,
                error: `Must be at least ${minLength} characters long`
            };
        }
        if (maxLength !== undefined && length > maxLength) {
            return {
                isValid: false,
                error: `Must be no more than ${maxLength} characters long`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate email format
   */ static validateEmail(email) {
        if (!email) {
            return {
                isValid: false,
                error: 'Email is required'
            };
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].EMAIL.test(email)) {
            return {
                isValid: false,
                error: 'Please enter a valid email address'
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate pattern match
   */ static validatePattern(value, pattern, errorMessage = 'Invalid format') {
        if (!pattern.test(value)) {
            return {
                isValid: false,
                error: errorMessage
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate numeric range
   */ static validateRange(value, min, max) {
        if (value < min || value > max) {
            return {
                isValid: false,
                error: `Must be between ${min} and ${max}`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate choice from options
   */ static validateChoice(value, choices, errorMessage = 'Please select a valid option') {
        if (!choices.includes(value)) {
            return {
                isValid: false,
                error: errorMessage
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate username
   */ static validateUsername(username) {
        const requiredResult = this.validateRequired(username, 'Username');
        if (!requiredResult.isValid) return requiredResult;
        const lengthResult = this.validateLength(username, 3, 20);
        if (!lengthResult.isValid) return lengthResult;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].USERNAME.test(username)) {
            return {
                isValid: false,
                error: 'Username can only contain letters, numbers, and underscores'
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate password
   */ static validatePassword(password) {
        const requiredResult = this.validateRequired(password, 'Password');
        if (!requiredResult.isValid) return requiredResult;
        if (password.length < __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_PASSWORD_LENGTH) {
            return {
                isValid: false,
                error: `Password must be at least ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_PASSWORD_LENGTH} characters long`
            };
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].PASSWORD.test(password)) {
            return {
                isValid: false,
                error: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
            };
        }
        return {
            isValid: true
        };
    }
}
class ContentValidation {
    /**
   * Validate topic for content generation
   */ static validateTopic(topic) {
        const requiredResult = ValidationUtils.validateRequired(topic, 'Topic');
        if (!requiredResult.isValid) return requiredResult;
        const length = topic.length;
        if (length < __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_TOPIC_LENGTH) {
            return {
                isValid: false,
                error: `Topic must be at least ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_TOPIC_LENGTH} characters long`
            };
        }
        if (length > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH) {
            return {
                isValid: false,
                error: `Topic must be no more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH} characters long`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate content for tweets
   */ static validateContent(content) {
        const requiredResult = ValidationUtils.validateRequired(content, 'Content');
        if (!requiredResult.isValid) return requiredResult;
        const length = content.length;
        if (length > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH) {
            return {
                isValid: false,
                error: `Content must be no more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH} characters long`
            };
        }
        const warnings = [];
        // Check for too many hashtags
        const hashtagCount = ContentValidation.countHashtags(content);
        if (hashtagCount > 3) {
            warnings.push('Consider using fewer hashtags for better engagement');
        }
        // Check for too many mentions
        const mentionCount = ContentValidation.countMentions(content);
        if (mentionCount > 5) {
            warnings.push('Too many mentions may reduce visibility');
        }
        return {
            isValid: true,
            warnings: warnings.length > 0 ? warnings : undefined
        };
    }
    /**
   * Validate content style
   */ static validateStyle(style) {
        return ValidationUtils.validateChoice(style, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CONTENT_CONSTANTS"].SUPPORTED_STYLES, 'Please select a valid style');
    }
    /**
   * Validate language
   */ static validateLanguage(language) {
        const supportedLanguages = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CONTENT_CONSTANTS"].SUPPORTED_LANGUAGES.map((lang)=>lang.code);
        return ValidationUtils.validateChoice(language, supportedLanguages, 'Please select a valid language');
    }
    /**
   * Validate thread size
   */ static validateThreadSize(size) {
        if (size < 2) {
            return {
                isValid: false,
                error: 'Thread must have at least 2 tweets'
            };
        }
        if (size > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS) {
            return {
                isValid: false,
                error: `Thread cannot have more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS} tweets`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Count hashtags in content
   */ static countHashtags(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].HASHTAG);
        return matches ? matches.length : 0;
    }
    /**
   * Count mentions in content
   */ static countMentions(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].MENTION);
        return matches ? matches.length : 0;
    }
    /**
   * Count URLs in content
   */ static countUrls(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].URL);
        return matches ? matches.length : 0;
    }
    /**
   * Get content statistics
   */ static getContentStats(content) {
        const characterCount = content.length;
        const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
        return {
            characterCount,
            wordCount,
            hashtagCount: ContentValidation.countHashtags(content),
            mentionCount: ContentValidation.countMentions(content),
            urlCount: ContentValidation.countUrls(content),
            // Add properties expected by tests
            length: characterCount,
            remaining: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH - characterCount
        };
    }
}
class FormValidation {
    /**
   * Validate multiple fields and return combined result
   */ static validateFields(validations) {
        const errors = [];
        for (const validation of validations){
            const result = validation();
            if (!result.isValid && result.error) {
                errors.push(result.error);
            }
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    /**
   * Validate form data against schema
   */ static validateFormData(data, schema) {
        const errors = {};
        for (const [field, validator] of Object.entries(schema)){
            const result = validator(data[field]);
            if (!result.isValid && result.error) {
                errors[field] = result.error;
            }
        }
        return errors;
    }
    /**
   * Validate single field
   */ static validateField(value, validator) {
        return validator(value);
    }
}
class ValidationHooks {
    /**
   * Debounced validation for real-time feedback
   */ static createDebouncedValidator(validator, delay = 300) {
        let timeoutId;
        return (value, callback)=>{
            clearTimeout(timeoutId);
            timeoutId = setTimeout(()=>{
                const result = validator(value);
                callback(result);
            }, delay);
        };
    }
}
const ValidationSchemas = {
    contentGeneration: {
        topic: (value)=>ContentValidation.validateTopic(value),
        style: (value)=>ContentValidation.validateStyle(value),
        language: (value)=>ContentValidation.validateLanguage(value)
    },
    userRegistration: {
        username: (value)=>ValidationUtils.validateUsername(value),
        email: (value)=>ValidationUtils.validateEmail(value),
        password: (value)=>ValidationUtils.validatePassword(value)
    },
    userLogin: {
        email: (value)=>ValidationUtils.validateEmail(value),
        password: (value)=>ValidationUtils.validateRequired(value, 'Password')
    }
};
const validateContentGenerationForm = (data)=>{
    const errors = {};
    const topicValidation = ValidationSchemas.contentGeneration.topic(data.topic);
    if (!topicValidation.isValid && topicValidation.error) {
        errors.topic = topicValidation.error;
    }
    return errors;
};
const validateUserRegistrationForm = (data)=>{
    return FormValidation.validateFormData(data, ValidationSchemas.userRegistration);
};
const validateUserLoginForm = (data)=>{
    return FormValidation.validateFormData(data, ValidationSchemas.userLogin);
};
}}),
"[project]/app/lib/errorHandling.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ /**
 * Frontend error handling utilities following DRY and KISS principles.
 * Centralizes error handling logic to eliminate code duplication.
 */ __turbopack_context__.s({
    "AuthErrorHandler": (()=>AuthErrorHandler),
    "ContentErrorHandler": (()=>ContentErrorHandler),
    "ErrorHandler": (()=>ErrorHandler),
    "ErrorLogger": (()=>ErrorLogger),
    "ErrorTypes": (()=>ErrorTypes),
    "NetworkErrorHandler": (()=>NetworkErrorHandler),
    "RetryHandler": (()=>RetryHandler),
    "createAsyncErrorHandler": (()=>createAsyncErrorHandler),
    "handleAuthError": (()=>handleAuthError),
    "handleContentError": (()=>handleContentError),
    "handleNetworkError": (()=>handleNetworkError),
    "withErrorHandling": (()=>withErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-ssr] (ecmascript)");
;
const ErrorTypes = {
    NETWORK: 'NETWORK',
    VALIDATION: 'VALIDATION',
    AUTHENTICATION: 'AUTHENTICATION',
    AUTHORIZATION: 'AUTHORIZATION',
    RATE_LIMIT: 'RATE_LIMIT',
    SERVER: 'SERVER',
    CLIENT: 'CLIENT',
    UNKNOWN: 'UNKNOWN'
};
class ErrorHandler {
    /**
   * Main error handler that processes errors and returns structured result
   */ static handleError(error, context) {
        const message = this.getErrorMessage(error);
        const type = this.getErrorType(error);
        const userMessage = this.getUserFriendlyMessage(type);
        const result = {
            type,
            message,
            userMessage,
            context,
            timestamp: Date.now()
        };
        this.logError(error, context);
        return result;
    }
    /**
   * Get error message from various error types
   */ static getErrorMessage(error) {
        if (error instanceof Error) {
            return error.message;
        }
        if (typeof error === 'string') {
            return error;
        }
        if (error && typeof error === 'object' && 'message' in error) {
            return String(error.message);
        }
        return 'An unknown error occurred';
    }
    /**
   * Determine error type based on error content
   */ static getErrorType(error) {
        const message = this.getErrorMessage(error).toLowerCase();
        if (message.includes('network') || message.includes('fetch')) {
            return ErrorTypes.NETWORK;
        }
        if (message.includes('validation') || message.includes('invalid')) {
            return ErrorTypes.VALIDATION;
        }
        if (message.includes('unauthorized') || message.includes('auth')) {
            return ErrorTypes.AUTHENTICATION;
        }
        if (message.includes('forbidden')) {
            return ErrorTypes.AUTHORIZATION;
        }
        if (message.includes('too many requests') || message.includes('rate limit')) {
            return ErrorTypes.RATE_LIMIT;
        }
        if (message.includes('server error') || message.includes('internal')) {
            return ErrorTypes.SERVER;
        }
        if (message.includes('bad request') || message.includes('client')) {
            return ErrorTypes.CLIENT;
        }
        return ErrorTypes.UNKNOWN;
    }
    /**
   * Get user-friendly message for error type
   */ static getUserFriendlyMessage(errorType) {
        switch(errorType){
            case ErrorTypes.NETWORK:
                return 'Network error. Please check your connection.';
            case ErrorTypes.VALIDATION:
                return 'Please check your input and try again.';
            case ErrorTypes.AUTHENTICATION:
                return 'Please log in to continue.';
            case ErrorTypes.AUTHORIZATION:
                return 'You do not have permission to perform this action.';
            case ErrorTypes.RATE_LIMIT:
                return 'Too many requests. Please wait a moment and try again.';
            case ErrorTypes.SERVER:
                return 'Server error. Please try again later.';
            case ErrorTypes.CLIENT:
                return 'Invalid request. Please check your input.';
            default:
                return 'An unexpected error occurred. Please try again.';
        }
    }
    /**
   * Check if error is retryable
   */ static isRetryableError(error) {
        const type = this.getErrorType(error);
        const message = this.getErrorMessage(error).toLowerCase();
        // Network errors are retryable
        if (type === ErrorTypes.NETWORK) {
            return true;
        }
        // Timeout errors are retryable
        if (message.includes('timeout')) {
            return true;
        }
        // Server errors (5xx) are retryable
        if (type === ErrorTypes.SERVER || message.includes('server error')) {
            return true;
        }
        // Rate limit errors are retryable (after delay)
        if (type === ErrorTypes.RATE_LIMIT) {
            return true;
        }
        // Authentication, validation, and client errors are not retryable
        return false;
    }
    /**
   * Log error with context
   */ static logError(error, context) {
        const message = this.getErrorMessage(error);
        const logData = {
            message,
            context,
            timestamp: new Date().toISOString(),
            stack: error instanceof Error ? error.stack : undefined
        };
        console.error('Error occurred:', logData);
    }
    /**
   * Handle API errors with consistent formatting
   */ static handleApiError(error, options = {}) {
        const { showToast = false, logError = true, fallbackMessage = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERIC_ERROR } = options;
        let apiError;
        // Type guard for error objects
        const isErrorWithResponse = (err)=>{
            return typeof err === 'object' && err !== null && 'response' in err;
        };
        const isErrorWithRequest = (err)=>{
            return typeof err === 'object' && err !== null && 'request' in err;
        };
        const isErrorWithMessage = (err)=>{
            return typeof err === 'object' && err !== null && 'message' in err;
        };
        if (isErrorWithResponse(error)) {
            // HTTP error response
            const status = error.response.status;
            const data = error.response.data;
            apiError = {
                message: data?.message || (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getErrorMessageByStatus"])(status),
                status,
                code: data?.code,
                details: data?.details
            };
        } else if (isErrorWithRequest(error)) {
            // Network error
            apiError = {
                message: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].NETWORK_ERROR,
                status: 0,
                code: 'NETWORK_ERROR'
            };
        } else {
            // Other error
            apiError = {
                message: isErrorWithMessage(error) ? error.message : fallbackMessage,
                code: 'UNKNOWN_ERROR'
            };
        }
        if (logError) {
            console.error('API Error:', apiError, error);
        }
        if (showToast) {
        // This would integrate with your toast system
        // toast.error(apiError.message);
        }
        return apiError;
    }
    /**
   * Handle validation errors
   */ static handleValidationError(errors, options = {}) {
        const { logError = true } = options;
        let message;
        if (Array.isArray(errors)) {
            message = errors.join(', ');
        } else if (typeof errors === 'object' && errors !== null) {
            message = Object.values(errors).join(', ');
        } else {
            message = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].VALIDATION_ERROR;
        }
        const apiError = {
            message,
            status: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HTTP_STATUS"].UNPROCESSABLE_ENTITY,
            code: 'VALIDATION_ERROR',
            details: typeof errors === 'object' && errors !== null ? errors : undefined
        };
        if (logError) {
            console.warn('Validation Error:', apiError);
        }
        return apiError;
    }
    /**
   * Handle async operation errors
   */ static async handleAsyncError(operation, options = {}) {
        try {
            const data = await operation();
            return {
                data
            };
        } catch (error) {
            const apiError = this.handleApiError(error, options);
            return {
                error: apiError
            };
        }
    }
    /**
   * Create error boundary handler
   */ static createErrorBoundaryHandler(fallbackComponent) {
        return (error, errorInfo)=>{
            console.error('Error Boundary caught an error:', error, errorInfo);
            // Log to error reporting service
            // errorReportingService.captureException(error, errorInfo);
            return fallbackComponent;
        };
    }
}
class ContentErrorHandler {
    /**
   * Handle content generation errors
   */ static handleGenerationError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERATION_FAILED,
            logError: true
        });
    }
    /**
   * Handle content validation errors
   */ static handleContentValidationError(errors) {
        return ErrorHandler.handleValidationError(errors, {
            logError: true
        });
    }
}
class AuthErrorHandler {
    /**
   * Handle authentication errors
   */ static handleAuthError(error) {
        const apiError = ErrorHandler.handleApiError(error, {
            logError: true
        });
        // Handle specific auth scenarios
        if (apiError.status === __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HTTP_STATUS"].UNAUTHORIZED) {
            // Clear auth tokens
            localStorage.removeItem('authToken');
        // Redirect to login if needed
        // router.push('/login');
        }
        return apiError;
    }
    /**
   * Handle token expiration
   */ static handleTokenExpiration() {
        localStorage.removeItem('authToken');
    // Show token expired message
    // toast.error(ERROR_MESSAGES.UNAUTHORIZED);
    // Redirect to login
    // router.push('/login');
    }
}
class NetworkErrorHandler {
    /**
   * Handle network connectivity issues
   */ static handleNetworkError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].NETWORK_ERROR,
            logError: true
        });
    }
    /**
   * Handle rate limiting
   */ static handleRateLimitError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].RATE_LIMIT_EXCEEDED,
            logError: true
        });
    }
}
class RetryHandler {
    /**
   * Retry failed operations with exponential backoff
   */ static async retryOperation(operation, maxRetries = 3, baseDelay = 1000) {
        let lastError;
        for(let attempt = 0; attempt <= maxRetries; attempt++){
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw error;
                }
                // Exponential backoff
                const delay = baseDelay * Math.pow(2, attempt);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            }
        }
        throw lastError;
    }
    /**
   * Check if error is retryable
   */ static isRetryableError(error) {
        if (!error.response) {
            return true; // Network errors are retryable
        }
        const status = error.response.status;
        return status >= 500 || status === __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HTTP_STATUS"].TOO_MANY_REQUESTS;
    }
}
class ErrorLogger {
    /**
   * Log error with timestamp and context
   */ static log(error, context) {
        const timestamp = new Date().toISOString();
        const message = error instanceof Error ? error.message : String(error);
        if (context) {
            console.error(`[ERROR] ${timestamp}`, message, 'Context:', context);
        } else {
            console.error(`[ERROR] ${timestamp}`, message);
        }
    }
    /**
   * Log warning with timestamp and context
   */ static warn(message, context) {
        const timestamp = new Date().toISOString();
        if (context) {
            console.warn(`[WARN] ${timestamp}`, message, 'Context:', context);
        } else {
            console.warn(`[WARN] ${timestamp}`, message);
        }
    }
    /**
   * Log info message
   */ static info(message, context) {
        const timestamp = new Date().toISOString();
        if (context) {
            console.log(`[INFO] ${timestamp}`, message, 'Context:', context);
        } else {
            console.log(`[INFO] ${timestamp}`, message);
        }
    }
    /**
   * Log debug message (only in development)
   */ static debug(message, context) {
        if ("TURBOPACK compile-time truthy", 1) {
            const timestamp = new Date().toISOString();
            if (context) {
                console.log(`[DEBUG] ${timestamp}`, message, 'Context:', context);
            } else {
                console.log(`[DEBUG] ${timestamp}`, message);
            }
        }
    }
    /**
   * Log error to console with context (legacy method)
   */ static logError(error, context, additionalData) {
        console.group(`🚨 Error in ${context}`);
        console.error('Error:', error);
        if (additionalData) {
            console.error('Additional Data:', additionalData);
        }
        console.error('Stack:', error.stack);
        console.groupEnd();
    }
    /**
   * Log warning with context (legacy method)
   */ static logWarning(message, context, additionalData) {
        console.group(`⚠️ Warning in ${context}`);
        console.warn('Message:', message);
        if (additionalData) {
            console.warn('Additional Data:', additionalData);
        }
        console.groupEnd();
    }
}
const withErrorHandling = (fn, errorHandler)=>{
    return async (...args)=>{
        try {
            return await fn(...args);
        } catch (error) {
            if (errorHandler) {
                errorHandler(error);
            } else {
                ErrorHandler.handleApiError(error, {
                    logError: true
                });
            }
            return undefined;
        }
    };
};
const createAsyncErrorHandler = (defaultErrorMessage = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERIC_ERROR)=>{
    return (error)=>{
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: defaultErrorMessage,
            logError: true
        });
    };
};
const handleContentError = ContentErrorHandler.handleGenerationError;
const handleAuthError = AuthErrorHandler.handleAuthError;
const handleNetworkError = NetworkErrorHandler.handleNetworkError;
}}),
"[project]/app/lib/contentService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ContentService": (()=>ContentService),
    "contentService": (()=>contentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/validation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/errorHandling.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-ssr] (ecmascript)");
// Export singleton instance
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/apiClient.ts [app-ssr] (ecmascript)");
;
;
;
class ContentService {
    apiClient;
    constructor(apiClient){
        this.apiClient = apiClient;
    }
    // New backend API methods with error handling
    async generateTweet(request) {
        try {
            this.validateTweetRequest(request);
            return await this.apiClient.post('/content/generate-tweet', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async generateThread(request) {
        try {
            this.validateThreadRequest(request);
            return await this.apiClient.post('/content/generate-thread', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async generateReply(request) {
        try {
            this.validateReplyRequest(request);
            return await this.apiClient.post('/content/generate-reply', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async getContentHistory(skip = 0, limit = 50) {
        const params = new URLSearchParams({
            skip: skip.toString(),
            limit: limit.toString()
        });
        return this.apiClient.get(`/content/history?${params}`);
    }
    // Legacy methods for backward compatibility
    async generateContent(request) {
        this.validateContentRequest(request);
        // Convert legacy request to new format
        const newRequest = {
            topic: request.topic,
            style: request.tone,
            user_context: `Length: ${request.length}, Include hashtags: ${request.includeHashtags}, Include emojis: ${request.includeEmojis}`
        };
        const response = await this.generateTweet(newRequest);
        // Convert response to legacy format
        return {
            id: Date.now().toString(),
            content: response.content || '',
            hashtags: [],
            createdAt: new Date().toISOString(),
            status: 'draft'
        };
    }
    // TODO: Implement these methods when backend endpoints are ready
    // For now, these are removed to eliminate dead code
    //
    // Future implementations:
    // - getContent(): Use history endpoint with filtering
    // - getContentById(): Implement backend endpoint for single content retrieval
    // - updateContent(): Implement backend endpoint for content updates
    // - scheduleContent(): Use scheduled posts API
    // - deleteContent(): Implement backend endpoint for content deletion
    // - publishContent(): Use Twitter posting API
    // Private validation methods using centralized validation utilities
    validateTweetRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
    }
    validateThreadRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
        if (request.num_tweets) {
            if (request.num_tweets < 2) {
                throw new Error('Thread must contain at least 2 tweets');
            }
            if (request.num_tweets > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS) {
                throw new Error(`Thread cannot exceed ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS} tweets`);
            }
        }
    }
    validateReplyRequest(request) {
        const requiredValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationUtils"].validateRequired(request.original_tweet, 'Original tweet');
        if (!requiredValidation.isValid) {
            throw new Error(requiredValidation.error);
        }
        const lengthValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationUtils"].validateLength(request.original_tweet, 1, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH);
        if (!lengthValidation.isValid) {
            throw new Error(lengthValidation.error);
        }
    }
    validateContentRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
    }
    // Removed unused validation methods to eliminate dead code
    buildQueryParams(filters) {
        if (!filters) return '';
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value])=>{
            if (value !== undefined && value !== null) {
                params.append(key, String(value));
            }
        });
        const queryString = params.toString();
        return queryString ? `?${queryString}` : '';
    }
}
;
const contentService = new ContentService(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"]);
}}),
"[project]/app/components/ConnectionTest.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ConnectionTest)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/authService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$contentService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/contentService.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function ConnectionTest() {
    const [tests, setTests] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([
        {
            name: 'Backend Health Check',
            status: 'pending'
        },
        {
            name: 'API Configuration',
            status: 'pending'
        },
        {
            name: 'Authentication Test',
            status: 'pending'
        },
        {
            name: 'Content Service Test',
            status: 'pending'
        }
    ]);
    const [isRunning, setIsRunning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [authToken, setAuthToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const updateTest = (index, status, message)=>{
        setTests((prev)=>prev.map((test, i)=>i === index ? {
                    ...test,
                    status,
                    message
                } : test));
    };
    const runTests = async ()=>{
        setIsRunning(true);
        // Reset all tests
        setTests((prev)=>prev.map((test)=>({
                    ...test,
                    status: 'pending'
                })));
        try {
            // Test 1: Backend Health Check
            try {
                // Use the API health endpoint
                const health = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].get('/health');
                if (health && health.status === 'healthy') {
                    updateTest(0, 'success', `Backend is healthy (API: ${health.api || 'ready'})`);
                } else {
                    updateTest(0, 'error', 'Backend health check failed');
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                updateTest(0, 'error', `Backend not accessible: ${errorMessage}`);
            }
            // Test 2: API Configuration
            try {
                const baseURL = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiClient"].instance.defaults.baseURL;
                if (baseURL?.includes('8000')) {
                    updateTest(1, 'success', `API URL: ${baseURL}`);
                } else {
                    updateTest(1, 'error', `Incorrect API URL: ${baseURL}`);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                updateTest(1, 'error', `API configuration error: ${errorMessage}`);
            }
            // Test 3: Authentication Test
            try {
                const testUser = {
                    username: `test_${Date.now()}`,
                    email: `test_${Date.now()}@example.com`,
                    password: 'testpass123',
                    full_name: 'Test User'
                };
                // Try to register (might fail if user exists)
                try {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].register(testUser);
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                    if (!errorMessage.includes('already registered')) {
                        throw error;
                    }
                }
                // Try to login
                const loginResult = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].login({
                    username: testUser.username,
                    password: testUser.password
                });
                if (loginResult.access_token) {
                    setAuthToken(loginResult.access_token);
                    updateTest(2, 'success', 'Authentication successful');
                } else {
                    updateTest(2, 'error', 'Login failed');
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                updateTest(2, 'error', `Authentication failed: ${errorMessage}`);
            }
            // Test 4: Content Service Test
            try {
                if (__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].isAuthenticated()) {
                    const history = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$contentService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["contentService"].getContentHistory(0, 5);
                    updateTest(3, 'success', `Content history retrieved (${history.total || 0} items)`);
                } else {
                    updateTest(3, 'error', 'Not authenticated for content test');
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                updateTest(3, 'error', `Content service failed: ${errorMessage}`);
            }
        } catch (error) {
            console.error('Test suite error:', error);
        } finally{
            setIsRunning(false);
        }
    };
    const testContentGeneration = async ()=>{
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].isAuthenticated()) {
            alert('Please run authentication test first');
            return;
        }
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$contentService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["contentService"].generateTweet({
                topic: 'Testing AutoReach connection',
                style: 'professional',
                language: 'en'
            });
            if (result.success && result.content) {
                alert(`Content generated successfully:\n\n${result.content}`);
            } else {
                alert(`Content generation failed: ${result.error || 'Unknown error'}`);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            alert(`Content generation error: ${errorMessage}`);
        }
    };
    const getStatusIcon = (status)=>{
        switch(status){
            case 'pending':
                return '⏳';
            case 'success':
                return '✅';
            case 'error':
                return '❌';
        }
    };
    const getStatusColor = (status)=>{
        switch(status){
            case 'pending':
                return 'text-yellow-600';
            case 'success':
                return 'text-green-600';
            case 'error':
                return 'text-red-600';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "text-2xl font-bold mb-6 text-gray-800",
                children: "Frontend-Backend Connection Test"
            }, void 0, false, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 164,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4 mb-6",
                children: tests.map((test, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-3 p-3 border rounded-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-2xl",
                                children: getStatusIcon(test.status)
                            }, void 0, false, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 171,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `font-medium ${getStatusColor(test.status)}`,
                                        children: test.name
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/ConnectionTest.tsx",
                                        lineNumber: 173,
                                        columnNumber: 15
                                    }, this),
                                    test.message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-gray-600 mt-1",
                                        children: test.message
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/ConnectionTest.tsx",
                                        lineNumber: 177,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 172,
                                columnNumber: 13
                            }, this)
                        ]
                    }, index, true, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 170,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 168,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex space-x-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: runTests,
                        disabled: isRunning,
                        className: "px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",
                        children: isRunning ? 'Running Tests...' : 'Run Connection Tests'
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 187,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: testContentGeneration,
                        disabled: isRunning || !__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].isAuthenticated(),
                        className: "px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",
                        children: "Test Content Generation"
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 195,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 186,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-6 p-4 bg-gray-50 rounded-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "font-medium text-gray-800 mb-2",
                        children: "Connection Status"
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 205,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-sm text-gray-600 space-y-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    "Backend URL: ",
                                    ("TURBOPACK compile-time value", "http://localhost:8000/api") || 'http://localhost:8000/api'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 207,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    "Authentication: ",
                                    __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["authService"].isAuthenticated() ? '✅ Authenticated' : '❌ Not authenticated'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 208,
                                columnNumber: 11
                            }, this),
                            authToken && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "break-all",
                                children: [
                                    "Token: ",
                                    authToken.substring(0, 20),
                                    "..."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 210,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 206,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 204,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 text-xs text-gray-500",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "This component tests the connection between the Next.js frontend and FastAPI backend."
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 216,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Make sure the backend is running on port 8000 before testing."
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 217,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 215,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/ConnectionTest.tsx",
        lineNumber: 163,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=app_3a57ce3d._.js.map