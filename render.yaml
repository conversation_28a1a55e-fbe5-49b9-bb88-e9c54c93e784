# Render Blueprint for Reachly Platform
# This file defines all services needed for the Reachly deployment
#
# SECURITY WARNING: This is a template file.
# Update all placeholder values before deploying to production.
# Never deploy directly from public repositories.

databases:
  - name: reachly-db
    databaseName: reachly
    user: reachly_user
    plan: free

services:
  # Backend API Service
  - type: web
    name: reachly-backend
    runtime: python
    plan: free
    rootDir: backend
    buildCommand: pip install -r requirements.txt
    startCommand: python start_server.py
    healthCheckPath: /health
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: reachly-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          type: redis
          name: reachly-redis
          property: connectionString
      - key: SECRET_KEY
        generateValue: true
      - key: DEBUG
        value: "false"
      - key: FRONTEND_URL
        fromService:
          type: web
          name: reachly-frontend
          property: host
      # Twitter API credentials - Add these manually in Render dashboard
      - key: TWITTER_CLIENT_ID
        sync: false
      - key: TWITTER_CLIENT_SECRET
        sync: false
      - key: TWITTER_BEARER_TOKEN
        sync: false
      - key: TWITTER_API_KEY
        sync: false
      - key: TWITTER_API_SECRET
        sync: false
      - key: TWITTER_ACCESS_TOKEN
        sync: false
      - key: TWITTER_ACCESS_TOKEN_SECRET
        sync: false
      - key: TWITTER_OAUTH_REDIRECT_URI
        sync: false
      # OpenAI API key - Add manually in Render dashboard
      - key: OPENAI_API_KEY
        sync: false

  # Frontend Service
  - type: web
    name: reachly-frontend
    runtime: node
    plan: free
    rootDir: frontend
    buildCommand: npm ci && npm run build
    startCommand: npx serve@latest out
    envVars:
      - key: NEXT_PUBLIC_API_URL
        fromService:
          type: web
          name: reachly-backend
          property: host

  # Redis Service
  - type: redis
    name: reachly-redis
    plan: free
    maxmemoryPolicy: allkeys-lru
    ipAllowList: []  # Allow access from all Render services
