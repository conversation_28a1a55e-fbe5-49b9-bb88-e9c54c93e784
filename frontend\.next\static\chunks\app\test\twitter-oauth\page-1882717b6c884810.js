(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[801],{2178:(e,t,r)=>{"use strict";r.d(t,{b:()=>s});var a=r(4205);class n{async initiateTwitterAuth(){try{return await a.uE.get("/auth/twitter/login")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to initiate Twitter auth: ".concat(e))}}async handleTwitterCallback(e){try{return await a.uE.post("/auth/twitter/callback",e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to handle Twitter callback: ".concat(e))}}async getTwitterStatus(){try{return await a.uE.get("/auth/twitter/status")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to get Twitter status: ".concat(e))}}async disconnectTwitter(){try{return await a.uE.delete("/auth/twitter/disconnect")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to disconnect Twitter: ".concat(e))}}async startTwitterOAuth(){let e=await this.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret);let t=window.open(e.authorization_url,"twitter_auth","width=600,height=600,scrollbars=yes,resizable=yes");return new Promise((r,a)=>{let n=setInterval(()=>{(null==t?void 0:t.closed)&&(clearInterval(n),a(Error("Twitter authorization was cancelled")))},1e3),s=o=>{o.origin===window.location.origin&&("TWITTER_AUTH_SUCCESS"===o.data.type?(clearInterval(n),window.removeEventListener("message",s),null==t||t.close(),r({oauth_token:e.oauth_token,oauth_token_secret:e.oauth_token_secret})):"TWITTER_AUTH_ERROR"===o.data.type&&(clearInterval(n),window.removeEventListener("message",s),null==t||t.close(),a(Error(o.data.error||"Twitter authorization failed"))))};window.addEventListener("message",s)})}async completeTwitterOAuth(e){let t=sessionStorage.getItem("twitter_oauth_token_secret");if(!t)throw Error("OAuth token secret not found. Please restart the authentication process.");let r=new URLSearchParams(window.location.search).get("oauth_token");if(!r)throw Error("OAuth token not found in callback URL");let a=await this.handleTwitterCallback({oauth_token:r,oauth_verifier:e,oauth_token_secret:t});return sessionStorage.removeItem("twitter_oauth_token_secret"),a}}let s=new n},2703:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});class a{getItem(e){try{return localStorage.getItem(e)}catch(e){return null}}setItem(e,t){try{localStorage.setItem(e,t)}catch(e){}}removeItem(e){try{localStorage.removeItem(e)}catch(e){}}clear(){try{localStorage.clear()}catch(e){}}}let n=new a},3605:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(5155),n=r(2115),s=r(2178),o=r(3851);function i(e){let{className:t=""}=e,[r,i]=(0,n.useState)(!1),[l,c]=(0,n.useState)({connected:!1}),[d,u]=(0,n.useState)(!1),[h,E]=(0,n.useState)(null),[w,m]=(0,n.useState)(null),T=(0,n.useCallback)(async()=>{try{E(null);let e=await s.b.getTwitterStatus();c(e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";E("Failed to load Twitter status: ".concat(e))}},[]),g=(0,n.useCallback)(async()=>{let e=o.y.isAuthenticated();i(e),e&&await T()},[T]);(0,n.useEffect)(()=>{g()},[g]);let _=async()=>{if(!r)return void E("Please log in first to connect your Twitter account");u(!0),E(null),m(null);try{let e=await s.b.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret),sessionStorage.setItem("twitter_oauth_token",e.oauth_token),m("Redirecting to Twitter for authorization..."),setTimeout(()=>{window.location.href=e.authorization_url},1e3)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";E("Failed to connect Twitter: ".concat(e)),u(!1)}},N=async()=>{u(!0),E(null),m(null);try{await s.b.disconnectTwitter(),m("Twitter account disconnected successfully"),await T()}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";E("Failed to disconnect Twitter: ".concat(e))}finally{u(!1)}},A=async()=>{u(!0),E(null),m(null);try{await T(),m("Twitter status refreshed successfully")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";E("Failed to test connection: ".concat(e))}finally{u(!1)}};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 ".concat(t),children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDC26 Twitter OAuth Test"}),(0,a.jsxs)("div",{className:"mb-4 p-3 rounded-md bg-gray-50",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"Authentication Status"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"w-3 h-3 rounded-full ".concat(r?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-sm",children:r?"Logged in to AutoReach":"Not logged in"})]})]}),(0,a.jsxs)("div",{className:"mb-4 p-3 rounded-md bg-gray-50",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"Twitter Connection Status"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("span",{className:"w-3 h-3 rounded-full ".concat(l.connected?"bg-green-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-sm",children:l.connected?"Connected to Twitter":"Not connected to Twitter"})]}),l.connected&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:["Username: @",l.twitter_username]}),(0,a.jsxs)("p",{children:["User ID: ",l.twitter_user_id]})]})]}),h&&(0,a.jsx)("div",{className:"mb-4 p-3 rounded-md bg-red-50 border border-red-200",children:(0,a.jsxs)("p",{className:"text-red-700 text-sm",children:["❌ ",h]})}),w&&(0,a.jsx)("div",{className:"mb-4 p-3 rounded-md bg-green-50 border border-green-200",children:(0,a.jsxs)("p",{className:"text-green-700 text-sm",children:["✅ ",w]})}),(0,a.jsx)("div",{className:"space-y-3",children:r?(0,a.jsxs)(a.Fragment,{children:[l.connected?(0,a.jsx)("button",{onClick:N,disabled:d,className:"w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d?"Disconnecting...":"\uD83D\uDD0C Disconnect Twitter Account"}):(0,a.jsx)("button",{onClick:_,disabled:d,className:"w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d?"Connecting...":"\uD83D\uDD17 Connect Twitter Account"}),(0,a.jsx)("button",{onClick:A,disabled:d,className:"w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:d?"Testing...":"\uD83D\uDD04 Refresh Status"})]}):(0,a.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-md",children:[(0,a.jsx)("p",{className:"text-yellow-700 mb-2",children:"Please log in to test Twitter OAuth"}),(0,a.jsx)("button",{onClick:()=>window.location.href="/login",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Go to Login"})]})}),(0,a.jsxs)("div",{className:"mt-6 p-3 bg-blue-50 rounded-md",children:[(0,a.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Test Instructions:"}),(0,a.jsxs)("ol",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"1. Make sure you're logged in to AutoReach"}),(0,a.jsx)("li",{children:'2. Click "Connect Twitter Account"'}),(0,a.jsx)("li",{children:"3. You'll be redirected to Twitter for authorization"}),(0,a.jsx)("li",{children:"4. After authorization, you'll be redirected back"}),(0,a.jsx)("li",{children:'5. Check that the status shows "Connected to Twitter"'})]})]})]})}},3851:(e,t,r)=>{"use strict";r.d(t,{y:()=>i});var a=r(4611),n=r(2703),s=r(4205);class o{async login(e){this.validateLoginRequest(e);let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let r=await this.apiClient.instance.post("/auth/token",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return n.I.setItem(a.d5.AUTH_TOKEN,r.data.access_token),r.data}async register(e){return this.validateRegisterRequest(e),this.apiClient.post("/users/",e)}async getCurrentUser(){return this.apiClient.get("/auth/me")}async logout(){n.I.removeItem(a.d5.AUTH_TOKEN),window.location.href="/login"}isAuthenticated(){return!!n.I.getItem(a.d5.AUTH_TOKEN)}getToken(){return n.I.getItem(a.d5.AUTH_TOKEN)}async initiateTwitterOAuth2(){return(await this.apiClient.instance.post("/auth/oauth2/twitter/init")).data}async handleTwitterOAuth2Callback(e){let t=await this.apiClient.instance.post("/auth/oauth2/twitter/callback",e);return n.I.setItem(a.d5.AUTH_TOKEN,t.data.access_token),t.data}validateLoginRequest(e){var t,r;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(r=e.password)?void 0:r.trim()))throw Error("Password is required")}validateRegisterRequest(e){var t,r,a;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(r=e.email)?void 0:r.trim()))throw Error("Email is required");if(!(null==(a=e.password)?void 0:a.trim()))throw Error("Password is required");if(e.password.length<6)throw Error("Password must be at least 6 characters");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email))throw Error("Please enter a valid email address")}constructor(e){this.apiClient=e}}let i=new o(s.uE)},4205:(e,t,r)=>{"use strict";r.d(t,{uE:()=>i});var a=r(3464),n=r(4611),s=r(2703);class o{createClient(){return a.A.create({baseURL:n.i3.BASE_URL,timeout:n.i3.TIMEOUT,headers:{"Content-Type":"application/json"}})}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.storage.getItem(n.d5.AUTH_TOKEN);return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(this.handleError(e))),this.client.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(this.handleError(e))})}handleUnauthorized(){this.storage.removeItem(n.d5.AUTH_TOKEN),window.location.href="/login"}handleError(e){if(!e.response)return Error(n.UU.NETWORK_ERROR);switch(e.response.status){case 401:return Error(n.UU.UNAUTHORIZED);case 400:return Error(n.UU.VALIDATION_ERROR);default:let t=e.response.data;return Error((null==t?void 0:t.message)||n.UU.GENERIC_ERROR)}}get instance(){return this.client}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(e=s.I){this.storage=e,this.client=this.createClient(),this.setupInterceptors()}}let i=new o},4525:(e,t,r)=>{Promise.resolve().then(r.bind(r,3605)),Promise.resolve().then(r.t.bind(r,6874,23))},4611:(e,t,r)=>{"use strict";r.d(t,{ID:()=>n,Ij:()=>a,KA:()=>l,Ot:()=>i,RW:()=>w,UU:()=>u,WF:()=>E,d5:()=>d,gY:()=>c,gx:()=>h,i3:()=>o,m0:()=>s});let a=[{name:"Dashboard",path:"/dashboard"},{name:"Content",path:"/content"},{name:"Analytics",path:"/analytics"},{name:"Settings",path:"/settings"},{name:"Auth",path:"/auth"},{name:"Test API",path:"/test-connection"}],n=[{value:"engaging",label:"Engaging"},{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"educational",label:"Educational"},{value:"humorous",label:"Humorous"},{value:"informative",label:"Informative"},{value:"helpful",label:"Helpful"}],s=[{value:"short",label:"Short (1-2 sentences)"},{value:"medium",label:"Medium (3-5 sentences)"},{value:"long",label:"Long (6+ sentences)"}],o={BASE_URL:"http://localhost:8000/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},i={MAX_TWEET_LENGTH:280,MAX_THREAD_TWEETS:25,DEFAULT_THREAD_SIZE:3,MAX_HASHTAGS_RECOMMENDED:3,MAX_MENTIONS_RECOMMENDED:5},l={DEFAULT_STYLE:"engaging",DEFAULT_LANGUAGE:"en",SUPPORTED_STYLES:["engaging","professional","casual","educational","humorous","informative","helpful"],SUPPORTED_LANGUAGES:[{code:"en",name:"English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"}]},c={MIN_TOPIC_LENGTH:3,MAX_TOPIC_LENGTH:200,MIN_CONTENT_LENGTH:1,MAX_CONTENT_LENGTH:2e3,MIN_USERNAME_LENGTH:3,MAX_USERNAME_LENGTH:50,MIN_PASSWORD_LENGTH:8,MAX_PASSWORD_LENGTH:128},d={AUTH_TOKEN:"authToken",USER_PREFERENCES:"userPreferences",DRAFT_CONTENT:"draftContent",THEME:"theme"},u={NETWORK_ERROR:"Network error. Please check your connection.",UNAUTHORIZED:"Your session has expired. Please log in again.",FORBIDDEN:"Access denied.",NOT_FOUND:"The requested resource was not found.",VALIDATION_ERROR:"Please check your input and try again.",GENERATION_FAILED:"Content generation failed. Please try again.",RATE_LIMIT_EXCEEDED:"Rate limit exceeded. Please try again later.",GENERIC_ERROR:"Something went wrong. Please try again."},h={OK:200,CREATED:201,NO_CONTENT:204,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,CONFLICT:409,UNPROCESSABLE_ENTITY:422,TOO_MANY_REQUESTS:429,INTERNAL_SERVER_ERROR:500,SERVICE_UNAVAILABLE:503},E={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,USERNAME:/^[a-zA-Z0-9_]+$/,PASSWORD:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,HASHTAG:/#\w+/g,MENTION:/@\w+/g,URL:/https?:\/\/[^\s]+/g},w=e=>{switch(e){case h.UNAUTHORIZED:return u.UNAUTHORIZED;case h.FORBIDDEN:return u.FORBIDDEN;case h.NOT_FOUND:return u.NOT_FOUND;case h.UNPROCESSABLE_ENTITY:return u.VALIDATION_ERROR;case h.TOO_MANY_REQUESTS:return u.RATE_LIMIT_EXCEEDED;case h.INTERNAL_SERVER_ERROR:case h.SERVICE_UNAVAILABLE:return u.GENERATION_FAILED;default:return u.GENERIC_ERROR}}}},e=>{var t=t=>e(e.s=t);e.O(0,[464,874,441,684,358],()=>t(4525)),_N_E=e.O()}]);