(()=>{var e={};e.id=999,e.ids=[999],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5056:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\auth\\\\twitter\\\\oauth2-callback\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\oauth2-callback\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21955:(e,t,r)=>{Promise.resolve().then(r.bind(r,5056))},26390:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["auth",{children:["twitter",{children:["oauth2-callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5056)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\oauth2-callback\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\auth\\twitter\\oauth2-callback\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/twitter/oauth2-callback/page",pathname:"/auth/twitter/oauth2-callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36027:(e,t,r)=>{Promise.resolve().then(r.bind(r,65442))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65442:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),a=r(43210),o=r(16189),i=r(63772),n=r(74265);function l(){let e=(0,o.useRouter)(),t=(0,o.useSearchParams)(),{refreshUser:r}=(0,i.A)(),[l,c]=(0,a.useState)("loading"),[u,d]=(0,a.useState)(""),[h,m]=(0,a.useState)(!1),[p,x]=(0,a.useState)(null);return(0,a.useCallback)(async()=>{if(h)return void console.log("⚠️ Callback already processing, skipping...");m(!0);try{if(localStorage.getItem("authToken")){console.log("⚠️ User already has a token, redirecting to dashboard..."),c("success"),d("Already authenticated, redirecting..."),setTimeout(()=>e.push("/dashboard"),1e3);return}let s=new URLSearchParams(window.location.search);console.log("=== TWITTER CALLBACK DEBUG ==="),console.log("Full URL:",window.location.href),console.log("All URL params:",Object.fromEntries(s.entries()));let a=t.get("code"),o=t.get("state"),i=t.get("error"),l=t.get("error_description");if(console.log("OAuth params:",{code:a,state:o,error:i,error_description:l}),i){console.error("Twitter OAuth error:",{error:i,error_description:l}),c("error"),d(`Twitter authorization failed: ${i} - ${l||"No description"}`),setTimeout(()=>e.push("/"),3e3);return}if(!a||!o){c("error"),d("Missing OAuth parameters in callback URL"),setTimeout(()=>e.push("/"),3e3);return}let u=sessionStorage.getItem("twitter_oauth2_state"),h=sessionStorage.getItem("twitter_oauth2_code_verifier");if(!u||!h){c("error"),d("OAuth state not found. Please restart the authentication process."),setTimeout(()=>e.push("/"),3e3);return}if(o!==u){c("error"),d("Invalid OAuth state. Possible security issue."),setTimeout(()=>e.push("/"),3e3);return}console.log("\uD83D\uDD04 Calling backend OAuth callback...");let m=await n.y.handleTwitterOAuth2Callback({code:a,state:o,code_verifier:h});console.log("✅ Backend OAuth callback successful:",{hasAccessToken:!!m.access_token,hasUser:!!m.user,userId:m.user?.id,username:m.user?.username}),sessionStorage.removeItem("twitter_oauth2_state"),sessionStorage.removeItem("twitter_oauth2_code_verifier"),c("success"),d("Successfully logged in with Twitter!"),x(m.user),console.log("\uD83D\uDD04 Refreshing AuthContext..."),await r(),console.log("✅ AuthContext refreshed successfully"),setTimeout(()=>e.push("/dashboard"),1e3)}catch(t){console.error("Callback handler error:",t),sessionStorage.removeItem("twitter_oauth2_state"),sessionStorage.removeItem("twitter_oauth2_code_verifier"),localStorage.removeItem("authToken"),c("error"),d(t instanceof Error?t.message:"Failed to authenticate with Twitter"),t instanceof Error&&console.error("Error details:",{message:t.message,stack:t.stack,name:t.name}),setTimeout(()=>e.push("/login"),3e3)}finally{m(!1)}},[t,e,r,h]),(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Twitter Authentication"})}),(0,s.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:["loading"===l&&(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Processing Twitter authentication..."})]}),"success"===l&&(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Success!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:u}),p&&(0,s.jsxs)("div",{className:"bg-gray-50 rounded-md p-3 text-left",children:[(0,s.jsx)("p",{className:"text-sm text-gray-700",children:(0,s.jsxs)("strong",{children:["Welcome, ",p.full_name||p.username,"!"]})}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["@",p.twitter_username]})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:"Redirecting to dashboard..."})]}),"error"===l&&(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Authentication Failed"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:u}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting to home page..."})]})]})]})})}function c(){return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Twitter Authentication"}),(0,s.jsx)("div",{className:"bg-white shadow-md rounded-lg p-6 mt-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})})]})})})}function u(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(c,{}),children:(0,s.jsx)(l,{})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86364:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,786,658,70],()=>r(26390));module.exports=s})();