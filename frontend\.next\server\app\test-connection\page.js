(()=>{var e={};e.id=894,e.ids=[894],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,s)=>{let{createProxy:r}=s(39844);e.exports=r("C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4763:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(60687),n=s(43210),a=s(24731),o=s(74265),i=s(39046);function c(){let[e,t]=(0,n.useState)([{name:"Backend Health Check",status:"pending"},{name:"API Configuration",status:"pending"},{name:"Authentication Test",status:"pending"},{name:"Content Service Test",status:"pending"}]),[s,c]=(0,n.useState)(!1),[d,l]=(0,n.useState)(null),u=(e,s,r)=>{t(t=>t.map((t,n)=>n===e?{...t,status:s,message:r}:t))},p=async()=>{c(!0),t(e=>e.map(e=>({...e,status:"pending"})));try{try{let e=await a.uE.get("/health");e&&"healthy"===e.status?u(0,"success",`Backend is healthy (API: ${e.api||"ready"})`):u(0,"error","Backend health check failed")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";u(0,"error",`Backend not accessible: ${e}`)}try{let e=a.uE.instance.defaults.baseURL;e?.includes("8000")?u(1,"success",`API URL: ${e}`):u(1,"error",`Incorrect API URL: ${e}`)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";u(1,"error",`API configuration error: ${e}`)}try{let e={username:`test_${Date.now()}`,email:`test_${Date.now()}@example.com`,password:"testpass123",full_name:"Test User"};try{await o.y.register(e)}catch(e){if(!(e instanceof Error?e.message:"Unknown error occurred").includes("already registered"))throw e}let t=await o.y.login({username:e.username,password:e.password});t.access_token?(l(t.access_token),u(2,"success","Authentication successful")):u(2,"error","Login failed")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";u(2,"error",`Authentication failed: ${e}`)}try{if(o.y.isAuthenticated()){let e=await i.l.getContentHistory(0,5);u(3,"success",`Content history retrieved (${e.total||0} items)`)}else u(3,"error","Not authenticated for content test")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";u(3,"error",`Content service failed: ${e}`)}}catch(e){console.error("Test suite error:",e)}finally{c(!1)}},h=async()=>{if(!o.y.isAuthenticated())return void alert("Please run authentication test first");try{let e=await i.l.generateTweet({topic:"Testing AutoReach connection",style:"professional",language:"en"});e.success&&e.content?alert(`Content generated successfully:

${e.content}`):alert(`Content generation failed: ${e.error||"Unknown error"}`)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";alert(`Content generation error: ${e}`)}},x=e=>{switch(e){case"pending":return"⏳";case"success":return"✅";case"error":return"❌"}},m=e=>{switch(e){case"pending":return"text-yellow-600";case"success":return"text-green-600";case"error":return"text-red-600"}};return(0,r.jsxs)("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6 text-gray-800",children:"Frontend-Backend Connection Test"}),(0,r.jsx)("div",{className:"space-y-4 mb-6",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 border rounded-lg",children:[(0,r.jsx)("span",{className:"text-2xl",children:x(e.status)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:`font-medium ${m(e.status)}`,children:e.name}),e.message&&(0,r.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:e.message})]})]},t))}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{onClick:p,disabled:s,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Running Tests...":"Run Connection Tests"}),(0,r.jsx)("button",{onClick:h,disabled:s||!o.y.isAuthenticated(),className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Test Content Generation"})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-800 mb-2",children:"Connection Status"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,r.jsxs)("div",{children:["Backend URL: ","http://localhost:8000/api"]}),(0,r.jsxs)("div",{children:["Authentication: ",o.y.isAuthenticated()?"✅ Authenticated":"❌ Not authenticated"]}),d&&(0,r.jsxs)("div",{className:"break-all",children:["Token: ",d.substring(0,20),"..."]})]})]}),(0,r.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:[(0,r.jsx)("p",{children:"This component tests the connection between the Next.js frontend and FastAPI backend."}),(0,r.jsx)("p",{children:"Make sure the backend is running on port 8000 before testing."})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28073:(e,t,s)=>{Promise.resolve().then(s.bind(s,4763)),Promise.resolve().then(s.t.bind(s,85814,23))},28354:e=>{"use strict";e.exports=require("util")},28717:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(37413),n=s(4536),a=s.n(n),o=s(31213);function i(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:"AutoReach Connection Test"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"This page tests the connection between the Next.js frontend and FastAPI backend. Use this to verify that your setup is working correctly."})]}),(0,r.jsx)(o.default,{}),(0,r.jsx)("div",{className:"mt-12 max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-4 text-gray-800",children:"Setup Instructions"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-700",children:"\uD83D\uDD27 Backend Setup"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-3 rounded font-mono",children:["cd backend",(0,r.jsx)("br",{}),".\\venv\\Scripts\\Activate.ps1",(0,r.jsx)("br",{}),"uvicorn app.main:app --reload"]}),(0,r.jsxs)("p",{children:["Backend should be running on ",(0,r.jsx)("strong",{children:"http://localhost:8000"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-700",children:"⚛️ Frontend Setup"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-3 rounded font-mono",children:["cd frontend",(0,r.jsx)("br",{}),"npm run dev"]}),(0,r.jsxs)("p",{children:["Frontend should be running on ",(0,r.jsx)("strong",{children:"http://localhost:3000"})]})]})]})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)(a(),{href:"/",className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"← Back to Home"})})]})})]})})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31213:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\components\\\\ConnectionTest.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\ConnectionTest.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},37801:(e,t,s)=>{Promise.resolve().then(s.bind(s,31213)),Promise.resolve().then(s.t.bind(s,4536,23))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81030:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var r=s(65239),n=s(48088),a=s(88170),o=s.n(a),i=s(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let d={children:["",{children:["test-connection",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,28717)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-connection\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\test-connection\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/test-connection/page",pathname:"/test-connection",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,786,658,814,70,337],()=>s(81030));module.exports=r})();