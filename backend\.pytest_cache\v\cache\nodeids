["test_api.py::test_api_endpoints", "test_api.py::test_database_connection", "test_db_connection.py::test_connection", "test_env_loading.py::test_env_loading", "test_oauth2_endpoints.py::test_oauth2_endpoints", "test_twitter_oauth.py::test_twitter_api_connection", "test_twitter_oauth.py::test_twitter_credentials", "tests/test_content_api.py::TestContentAPI::test_generate_reply_success", "tests/test_content_api.py::TestContentAPI::test_generate_thread_invalid_num_tweets", "tests/test_content_api.py::TestContentAPI::test_generate_thread_success", "tests/test_content_api.py::TestContentAPI::test_generate_tweet_missing_topic", "tests/test_content_api.py::TestContentAPI::test_generate_tweet_success", "tests/test_content_api.py::TestContentAPI::test_generate_tweet_validation_error", "tests/test_content_api.py::TestContentAPI::test_get_content_history_success", "tests/test_content_service.py::TestContentService::test_generate_reply_success", "tests/test_content_service.py::TestContentService::test_generate_thread_success", "tests/test_content_service.py::TestContentService::test_generate_tweet_content_generator_exception", "tests/test_content_service.py::TestContentService::test_generate_tweet_success", "tests/test_content_service.py::TestContentService::test_generate_tweet_validation_failure", "tests/test_content_service.py::TestContentService::test_log_content_generation_database_error", "tests/test_content_service.py::TestContentService::test_log_content_generation_success", "tests/test_content_strategies.py::TestContentGenerationContext::test_add_strategy", "tests/test_content_strategies.py::TestContentGenerationContext::test_generate_content_reply", "tests/test_content_strategies.py::TestContentGenerationContext::test_generate_content_thread", "tests/test_content_strategies.py::TestContentGenerationContext::test_generate_content_tweet", "tests/test_content_strategies.py::TestContentGenerationContext::test_generate_content_unsupported_type", "tests/test_content_strategies.py::TestContentGenerationContext::test_get_supported_types", "tests/test_content_strategies.py::TestReplyGenerationStrategy::test_generate_reply_success", "tests/test_content_strategies.py::TestReplyGenerationStrategy::test_validate_parameters_missing_required", "tests/test_content_strategies.py::TestReplyGenerationStrategy::test_validate_parameters_valid", "tests/test_content_strategies.py::TestThreadGenerationStrategy::test_generate_thread_success", "tests/test_content_strategies.py::TestThreadGenerationStrategy::test_validate_parameters_default_num_tweets", "tests/test_content_strategies.py::TestThreadGenerationStrategy::test_validate_parameters_invalid_num_tweets_too_large", "tests/test_content_strategies.py::TestThreadGenerationStrategy::test_validate_parameters_invalid_num_tweets_too_small", "tests/test_content_strategies.py::TestThreadGenerationStrategy::test_validate_parameters_invalid_num_tweets_type", "tests/test_content_strategies.py::TestThreadGenerationStrategy::test_validate_parameters_valid", "tests/test_content_strategies.py::TestTweetGenerationStrategy::test_generate_invalid_parameters", "tests/test_content_strategies.py::TestTweetGenerationStrategy::test_generate_tweet_success", "tests/test_content_strategies.py::TestTweetGenerationStrategy::test_validate_parameters_missing_required", "tests/test_content_strategies.py::TestTweetGenerationStrategy::test_validate_parameters_valid", "tests/test_error_factory.py::TestErrorFactory::test_create_api_error", "tests/test_error_factory.py::TestErrorFactory::test_create_api_error_without_message", "tests/test_error_factory.py::TestErrorFactory::test_create_content_generation_error", "tests/test_error_factory.py::TestErrorFactory::test_create_content_generation_error_without_cause", "tests/test_error_factory.py::TestErrorFactory::test_create_database_error", "tests/test_error_factory.py::TestErrorFactory::test_create_database_error_without_cause", "tests/test_error_factory.py::TestErrorFactory::test_create_validation_error", "tests/test_error_factory.py::TestErrorFactory::test_create_validation_error_without_field", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_forbidden_http_error", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_forbidden_http_error_minimal", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_internal_server_http_error", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_not_found_http_error", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_not_found_http_error_without_identifier", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_rate_limit_http_error", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_rate_limit_http_error_default", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_service_unavailable_http_error", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_service_unavailable_http_error_minimal", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_unauthorized_http_error", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_unauthorized_http_error_default_message", "tests/test_error_factory.py::TestHTTPErrorFactory::test_create_validation_http_error", "tests/test_integration.py::TestAPIIntegration::test_api_documentation", "tests/test_integration.py::TestAPIIntegration::test_authentication_flow", "tests/test_integration.py::TestAPIIntegration::test_content_generation_endpoint_structure", "tests/test_integration.py::TestAPIIntegration::test_content_generation_endpoints", "tests/test_integration.py::TestAPIIntegration::test_content_history_endpoint", "tests/test_integration.py::TestAPIIntegration::test_content_history_endpoint_structure", "tests/test_integration.py::TestAPIIntegration::test_cors_headers", "tests/test_integration.py::TestAPIIntegration::test_health_check", "tests/test_integration.py::TestAPIIntegration::test_root_endpoint", "tests/test_integration.py::TestAPIIntegration::test_user_registration_flow", "tests/test_main.py::test_basic_math", "tests/test_main.py::test_list_operations", "tests/test_main.py::test_string_operations", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthEndpoints::test_init_oauth_missing_config", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthEndpoints::test_init_oauth_success", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthEndpoints::test_oauth_callback_invalid_code", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthEndpoints::test_oauth_callback_invalid_json", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthEndpoints::test_oauth_callback_missing_fields", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthEndpoints::test_oauth_callback_server_error", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthEndpoints::test_oauth_callback_success", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthIntegration::test_complete_oauth_flow_existing_user", "tests/test_twitter_oauth_endpoints.py::TestTwitterOAuthIntegration::test_complete_oauth_flow_new_user", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_create_jwt_token", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_create_or_update_user_existing_user", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_create_or_update_user_new_user", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_exchange_code_for_token_failure", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_exchange_code_for_token_success", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_generate_code_challenge", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_generate_code_verifier", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_generate_unique_username_available", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_generate_unique_username_taken", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_get_twitter_user_info_failure", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_get_twitter_user_info_success", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_handle_oauth_callback_success", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_init_oauth_flow_missing_config", "tests/test_twitter_oauth_service.py::TestTwitterOAuthService::test_init_oauth_flow_success", "tests/test_types.py::TestContentGenerationRequest::test_content_generation_request_creation", "tests/test_types.py::TestContentGenerationRequest::test_content_generation_request_optional_context", "tests/test_types.py::TestContentGenerationRequest::test_content_generation_request_to_dict", "tests/test_types.py::TestContentGenerationResult::test_content_generation_result_creation", "tests/test_types.py::TestContentGenerationResult::test_content_generation_result_optional_fields", "tests/test_types.py::TestContentGenerationResult::test_content_generation_result_to_dict", "tests/test_types.py::TestContentGenerationResult::test_content_generation_result_to_dict_without_optional", "tests/test_types.py::TestReplyGenerationRequest::test_reply_generation_request_creation", "tests/test_types.py::TestReplyGenerationRequest::test_reply_generation_request_optional_context", "tests/test_types.py::TestReplyGenerationRequest::test_reply_generation_request_to_dict", "tests/test_types.py::TestThreadGenerationRequest::test_thread_generation_request_creation", "tests/test_types.py::TestThreadGenerationRequest::test_thread_generation_request_to_dict", "tests/test_types.py::TestValidationContext::test_validation_context_creation", "tests/test_types.py::TestValidationContext::test_validation_context_optional_user_context", "tests/test_types.py::TestValidationContext::test_validation_context_to_dict", "tests/test_validation_service.py::TestValidationService::test_validate_content_generation_request_invalid_style", "tests/test_validation_service.py::TestValidationService::test_validate_content_generation_request_missing_topic", "tests/test_validation_service.py::TestValidationService::test_validate_content_generation_request_topic_too_short", "tests/test_validation_service.py::TestValidationService::test_validate_content_generation_request_valid", "tests/test_validation_service.py::TestValidationService::test_validate_field_choices", "tests/test_validation_service.py::TestValidationService::test_validate_field_numeric_range", "tests/test_validation_service.py::TestValidationService::test_validate_field_required_missing", "tests/test_validation_service.py::TestValidationService::test_validate_field_string_length", "tests/test_validation_service.py::TestValidationService::test_validate_thread_generation_request_too_few_tweets", "tests/test_validation_service.py::TestValidationService::test_validate_thread_generation_request_too_many_tweets", "tests/test_validation_service.py::TestValidationService::test_validate_thread_generation_request_valid", "tests/test_validation_service.py::TestValidationService::test_validate_tweet_content_empty", "tests/test_validation_service.py::TestValidationService::test_validate_tweet_content_excessive_hashtags", "tests/test_validation_service.py::TestValidationService::test_validate_tweet_content_too_long", "tests/test_validation_service.py::TestValidationService::test_validate_tweet_content_valid", "tests/test_value_objects.py::TestContentStyle::test_default_style", "tests/test_value_objects.py::TestContentStyle::test_invalid_style_raises_error", "tests/test_value_objects.py::TestContentStyle::test_style_immutability", "tests/test_value_objects.py::TestContentStyle::test_valid_style_creation", "tests/test_value_objects.py::TestLanguage::test_default_language", "tests/test_value_objects.py::TestLanguage::test_invalid_language_raises_error", "tests/test_value_objects.py::TestLanguage::test_language_immutability", "tests/test_value_objects.py::TestLanguage::test_valid_language_creation", "tests/test_value_objects.py::TestThreadSize::test_thread_size_immutability", "tests/test_value_objects.py::TestThreadSize::test_thread_size_too_large_raises_error", "tests/test_value_objects.py::TestThreadSize::test_thread_size_too_small_raises_error", "tests/test_value_objects.py::TestThreadSize::test_valid_thread_size_creation", "tests/test_value_objects.py::TestTopic::test_empty_topic_raises_error", "tests/test_value_objects.py::TestTopic::test_topic_immutability", "tests/test_value_objects.py::TestTopic::test_topic_strips_whitespace", "tests/test_value_objects.py::TestTopic::test_topic_too_long_raises_error", "tests/test_value_objects.py::TestTopic::test_topic_too_short_raises_error", "tests/test_value_objects.py::TestTopic::test_valid_topic_creation", "tests/test_value_objects.py::TestTweetContent::test_empty_tweet_content_raises_error", "tests/test_value_objects.py::TestTweetContent::test_hashtag_counting", "tests/test_value_objects.py::TestTweetContent::test_mention_counting", "tests/test_value_objects.py::TestTweetContent::test_remaining_characters_calculation", "tests/test_value_objects.py::TestTweetContent::test_tweet_content_immutability", "tests/test_value_objects.py::TestTweetContent::test_tweet_content_too_long_raises_error", "tests/test_value_objects.py::TestTweetContent::test_valid_tweet_content_creation", "tests/test_value_objects.py::TestUserContext::test_empty_user_context", "tests/test_value_objects.py::TestUserContext::test_user_context_immutability", "tests/test_value_objects.py::TestUserContext::test_user_context_too_long_raises_error", "tests/test_value_objects.py::TestUserContext::test_valid_user_context_creation"]