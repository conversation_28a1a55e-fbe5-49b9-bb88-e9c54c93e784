{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ContentGeneration.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/ContentGeneration.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/ContentGeneration.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ContentGeneration.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/components/ContentGeneration.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/ContentGeneration.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/content/page.tsx"], "sourcesContent": ["import Header from '@/components/Header';\nimport ContentGeneration from '@/components/ContentGeneration';\n\nexport default function Content() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Content</h1>\n          <p className=\"text-gray-600 mt-2\">Generate and manage your Twitter content with AI.</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Content Generation */}\n          <div>\n            <ContentGeneration />\n          </div>\n\n          {/* Recent Content */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">Recent Content</h2>\n\n            <div className=\"space-y-4\">\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-start mb-2\">\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                    Published\n                  </span>\n                  <span className=\"text-sm text-gray-500\">2 hours ago</span>\n                </div>\n                <p className=\"text-gray-900 mb-2\">\n                  Just discovered an amazing new AI tool that&apos;s revolutionizing content creation! 🚀\n                  The future of marketing is here. #AI #Marketing #Innovation\n                </p>\n                <div className=\"flex items-center text-sm text-gray-500 space-x-4\">\n                  <span>❤️ 24</span>\n                  <span>🔄 8</span>\n                  <span>💬 3</span>\n                </div>\n              </div>\n\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-start mb-2\">\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                    Scheduled\n                  </span>\n                  <span className=\"text-sm text-gray-500\">Tomorrow 9:00 AM</span>\n                </div>\n                <p className=\"text-gray-900 mb-2\">\n                  Monday motivation: Success isn&apos;t just about what you accomplish in your life,\n                  it&apos;s about what you inspire others to do. 💪 #MondayMotivation #Success\n                </p>\n                <div className=\"flex space-x-2\">\n                  <button className=\"text-sm text-primary-600 hover:text-primary-700\">Edit</button>\n                  <button className=\"text-sm text-red-600 hover:text-red-700\">Delete</button>\n                </div>\n              </div>\n\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-start mb-2\">\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\">\n                    Draft\n                  </span>\n                  <span className=\"text-sm text-gray-500\">1 day ago</span>\n                </div>\n                <p className=\"text-gray-900 mb-2\">\n                  The key to effective social media marketing is consistency and authenticity.\n                  Here are 5 tips to improve your engagement... 🧵\n                </p>\n                <div className=\"flex space-x-2\">\n                  <button className=\"text-sm text-primary-600 hover:text-primary-700\">Edit</button>\n                  <button className=\"text-sm text-green-600 hover:text-green-700\">Schedule</button>\n                  <button className=\"text-sm text-red-600 hover:text-red-700\">Delete</button>\n                </div>\n              </div>\n\n              <div className=\"border border-gray-200 rounded-lg p-4\">\n                <div className=\"flex justify-between items-start mb-2\">\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                    Published\n                  </span>\n                  <span className=\"text-sm text-gray-500\">2 days ago</span>\n                </div>\n                <p className=\"text-gray-900 mb-2\">\n                  Building a personal brand takes time, but the results are worth it.\n                  Focus on providing value and being genuine. #PersonalBrand #Growth\n                </p>\n                <div className=\"flex items-center text-sm text-gray-500 space-x-4\">\n                  <span>❤️ 42</span>\n                  <span>🔄 15</span>\n                  <span>💬 7</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"mt-6 text-center\">\n              <button className=\"text-primary-600 hover:text-primary-700 text-sm font-medium\">\n                View All Content\n              </button>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAGpC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;0CACC,cAAA,8OAAC,uIAAA,CAAA,UAAiB;;;;;;;;;;0CAIpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAsG;;;;;;0EAGtH,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAIlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwG;;;;;;0EAGxH,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAIlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;0EAAkD;;;;;;0EACpE,8OAAC;gEAAO,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;0DAIhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAoG;;;;;;0EAGpH,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAIlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;0EAAkD;;;;;;0EACpE,8OAAC;gEAAO,WAAU;0EAA8C;;;;;;0EAChE,8OAAC;gEAAO,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;0DAIhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAsG;;;;;;0EAGtH,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAIlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;sDAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9F", "debugId": null}}, {"offset": {"line": 540, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,IAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,cAAA,CAAA,CAAA,EAAA,yRAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,6RAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,yRAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,aAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}