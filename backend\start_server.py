#!/usr/bin/env python3
"""
Start the AutoReach FastAPI server
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv(override=True)

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    import uvicorn

    print("Starting Reachly API server...")
    print(f"Working directory: {os.getcwd()}")

    # Get port from environment variable (<PERSON><PERSON> sets this)
    port = int(os.environ.get("PORT", 8000))

    # Check if we're in production
    is_production = os.environ.get("RENDER") is not None

    print(f"Running in {'production' if is_production else 'development'} mode")
    print(f"Server will start on port {port}")

    # Start the server
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=port,
        reload=not is_production,  # Only reload in development
        reload_dirs=["."] if not is_production else None
    )
