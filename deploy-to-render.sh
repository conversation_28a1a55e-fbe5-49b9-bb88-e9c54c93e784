#!/bin/bash

# Reachly Render Deployment Helper Script
# This script helps prepare your repository for Render deployment

echo "🚀 Reachly Render Deployment Helper"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "infrastructure/render/render.yaml" ]; then
    echo "❌ Error: render.yaml not found. Please run this script from the project root."
    exit 1
fi

echo "✅ Found render.yaml configuration"

# Check if git is initialized and has commits
if [ ! -d ".git" ]; then
    echo "❌ Error: This is not a git repository. Please initialize git first:"
    echo "   git init"
    echo "   git add ."
    echo "   git commit -m 'Initial commit'"
    echo "   git remote add origin <your-github-repo-url>"
    echo "   git push -u origin main"
    exit 1
fi

echo "✅ Git repository detected"

# Check if there are uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
    echo "⚠️  Warning: You have uncommitted changes."
    echo "   It's recommended to commit all changes before deployment."
    echo ""
    read -p "Do you want to commit all changes now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git add .
        git commit -m "Pre-deployment commit: $(date)"
        echo "✅ Changes committed"
    fi
fi

# Check if we have a remote origin
if ! git remote get-url origin > /dev/null 2>&1; then
    echo "❌ Error: No git remote 'origin' found."
    echo "   Please add your GitHub repository as origin:"
    echo "   git remote add origin <your-github-repo-url>"
    exit 1
fi

echo "✅ Git remote origin configured"

# Push to GitHub
echo "📤 Pushing to GitHub..."
git push origin main

if [ $? -eq 0 ]; then
    echo "✅ Successfully pushed to GitHub"
else
    echo "❌ Failed to push to GitHub. Please check your git configuration."
    exit 1
fi

echo ""
echo "🎉 Repository is ready for Render deployment!"
echo ""
echo "Next steps:"
echo "1. Go to https://render.com/dashboard"
echo "2. Click 'New' → 'Blueprint'"
echo "3. Connect your GitHub repository"
echo "4. Select 'infrastructure/render/render.yaml'"
echo "5. Click 'Apply' to deploy"
echo ""
echo "📖 For detailed instructions, see: RENDER_DEPLOYMENT_GUIDE.md"
echo ""
echo "🔑 Don't forget to add your API keys in the Render dashboard:"
echo "   - Twitter API credentials"
echo "   - OpenAI API key"
echo ""
echo "🌐 After deployment, update these URLs:"
echo "   - Twitter OAuth redirect URI"
echo "   - Frontend/Backend environment variables"
echo ""
echo "Happy deploying! 🚀"
