{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/validation.ts"], "sourcesContent": ["/**\n * Frontend validation utilities following DRY and KISS principles.\n * Centralizes validation logic to eliminate code duplication.\n */\n\nimport {\n  VALIDATION_RULES,\n  REGEX_PATTERNS,\n  CONTENT_CONSTANTS,\n  TWITTER_CONSTANTS\n} from './constants';\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n}\n\nexport interface FieldValidationResult {\n  isValid: boolean;\n  error?: string;\n  warnings?: string[];\n}\n\n/**\n * Base validation utilities\n */\nexport class ValidationUtils {\n  /**\n   * Validate required field\n   */\n  static validateRequired(value: unknown, fieldName: string = 'This field'): FieldValidationResult {\n    if (value === null || value === undefined) {\n      // If fieldName contains \"is required\" or similar, use it as is\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    if (typeof value === 'string' && !value.trim()) {\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    if (Array.isArray(value) && value.length === 0) {\n      if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {\n        return { isValid: false, error: fieldName };\n      }\n      return { isValid: false, error: `${fieldName} is required` };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate string length\n   */\n  static validateLength(\n    value: string,\n    minLength?: number,\n    maxLength?: number\n  ): FieldValidationResult {\n    if (typeof value !== 'string') {\n      return { isValid: false, error: 'Value must be a string' };\n    }\n\n    const length = value.length;\n\n    if (minLength !== undefined && length < minLength) {\n      return {\n        isValid: false,\n        error: `Must be at least ${minLength} characters long`\n      };\n    }\n\n    if (maxLength !== undefined && length > maxLength) {\n      return {\n        isValid: false,\n        error: `Must be no more than ${maxLength} characters long`\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate email format\n   */\n  static validateEmail(email: string): FieldValidationResult {\n    if (!email) {\n      return { isValid: false, error: 'Email is required' };\n    }\n\n    if (!REGEX_PATTERNS.EMAIL.test(email)) {\n      return { isValid: false, error: 'Please enter a valid email address' };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate pattern match\n   */\n  static validatePattern(value: string, pattern: RegExp, errorMessage: string = 'Invalid format'): FieldValidationResult {\n    if (!pattern.test(value)) {\n      return { isValid: false, error: errorMessage };\n    }\n    return { isValid: true };\n  }\n\n  /**\n   * Validate numeric range\n   */\n  static validateRange(value: number, min: number, max: number): FieldValidationResult {\n    if (value < min || value > max) {\n      return { isValid: false, error: `Must be between ${min} and ${max}` };\n    }\n    return { isValid: true };\n  }\n\n  /**\n   * Validate choice from options\n   */\n  static validateChoice(value: string, choices: readonly string[] | string[], errorMessage: string = 'Please select a valid option'): FieldValidationResult {\n    if (!choices.includes(value)) {\n      return { isValid: false, error: errorMessage };\n    }\n    return { isValid: true };\n  }\n\n\n\n  /**\n   * Validate username\n   */\n  static validateUsername(username: string): FieldValidationResult {\n    const requiredResult = this.validateRequired(username, 'Username');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const lengthResult = this.validateLength(username, 3, 20);\n    if (!lengthResult.isValid) return lengthResult;\n\n    if (!REGEX_PATTERNS.USERNAME.test(username)) {\n      return { isValid: false, error: 'Username can only contain letters, numbers, and underscores' };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate password\n   */\n  static validatePassword(password: string): FieldValidationResult {\n    const requiredResult = this.validateRequired(password, 'Password');\n    if (!requiredResult.isValid) return requiredResult;\n\n    if (password.length < VALIDATION_RULES.MIN_PASSWORD_LENGTH) {\n      return {\n        isValid: false,\n        error: `Password must be at least ${VALIDATION_RULES.MIN_PASSWORD_LENGTH} characters long`\n      };\n    }\n\n    if (!REGEX_PATTERNS.PASSWORD.test(password)) {\n      return {\n        isValid: false,\n        error: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'\n      };\n    }\n\n    return { isValid: true };\n  }\n}\n\n/**\n * Content-specific validation utilities\n */\nexport class ContentValidation {\n  /**\n   * Validate topic for content generation\n   */\n  static validateTopic(topic: string): FieldValidationResult {\n    const requiredResult = ValidationUtils.validateRequired(topic, 'Topic');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const length = topic.length;\n    if (length < VALIDATION_RULES.MIN_TOPIC_LENGTH) {\n      return {\n        isValid: false,\n        error: `Topic must be at least ${VALIDATION_RULES.MIN_TOPIC_LENGTH} characters long`\n      };\n    }\n\n    if (length > VALIDATION_RULES.MAX_TOPIC_LENGTH) {\n      return {\n        isValid: false,\n        error: `Topic must be no more than ${VALIDATION_RULES.MAX_TOPIC_LENGTH} characters long`\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Validate content for tweets\n   */\n  static validateContent(content: string): FieldValidationResult {\n    const requiredResult = ValidationUtils.validateRequired(content, 'Content');\n    if (!requiredResult.isValid) return requiredResult;\n\n    const length = content.length;\n    if (length > TWITTER_CONSTANTS.MAX_TWEET_LENGTH) {\n      return {\n        isValid: false,\n        error: `Content must be no more than ${TWITTER_CONSTANTS.MAX_TWEET_LENGTH} characters long`\n      };\n    }\n\n    const warnings: string[] = [];\n\n    // Check for too many hashtags\n    const hashtagCount = ContentValidation.countHashtags(content);\n    if (hashtagCount > 3) {\n      warnings.push('Consider using fewer hashtags for better engagement');\n    }\n\n    // Check for too many mentions\n    const mentionCount = ContentValidation.countMentions(content);\n    if (mentionCount > 5) {\n      warnings.push('Too many mentions may reduce visibility');\n    }\n\n    return {\n      isValid: true,\n      warnings: warnings.length > 0 ? warnings : undefined\n    };\n  }\n\n  /**\n   * Validate content style\n   */\n  static validateStyle(style: string): FieldValidationResult {\n    return ValidationUtils.validateChoice(\n      style,\n      CONTENT_CONSTANTS.SUPPORTED_STYLES,\n      'Please select a valid style'\n    );\n  }\n\n  /**\n   * Validate language\n   */\n  static validateLanguage(language: string): FieldValidationResult {\n    const supportedLanguages = CONTENT_CONSTANTS.SUPPORTED_LANGUAGES.map(lang => lang.code);\n    return ValidationUtils.validateChoice(\n      language,\n      supportedLanguages,\n      'Please select a valid language'\n    );\n  }\n\n  /**\n   * Validate thread size\n   */\n  static validateThreadSize(size: number): FieldValidationResult {\n    if (size < 2) {\n      return { isValid: false, error: 'Thread must have at least 2 tweets' };\n    }\n    if (size > TWITTER_CONSTANTS.MAX_THREAD_TWEETS) {\n      return { isValid: false, error: `Thread cannot have more than ${TWITTER_CONSTANTS.MAX_THREAD_TWEETS} tweets` };\n    }\n    return { isValid: true };\n  }\n\n\n\n  /**\n   * Count hashtags in content\n   */\n  static countHashtags(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.HASHTAG);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Count mentions in content\n   */\n  static countMentions(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.MENTION);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Count URLs in content\n   */\n  static countUrls(content: string): number {\n    const matches = content.match(REGEX_PATTERNS.URL);\n    return matches ? matches.length : 0;\n  }\n\n  /**\n   * Get content statistics\n   */\n  static getContentStats(content: string) {\n    const characterCount = content.length;\n    const wordCount = content.trim() ? content.trim().split(/\\s+/).length : 0;\n\n    return {\n      characterCount,\n      wordCount,\n      hashtagCount: ContentValidation.countHashtags(content),\n      mentionCount: ContentValidation.countMentions(content),\n      urlCount: ContentValidation.countUrls(content),\n      // Add properties expected by tests\n      length: characterCount,\n      remaining: TWITTER_CONSTANTS.MAX_TWEET_LENGTH - characterCount,\n    };\n  }\n}\n\n/**\n * Form validation utilities\n */\nexport class FormValidation {\n  /**\n   * Validate multiple fields and return combined result\n   */\n  static validateFields(\n    validations: Array<() => FieldValidationResult>\n  ): ValidationResult {\n    const errors: string[] = [];\n\n    for (const validation of validations) {\n      const result = validation();\n      if (!result.isValid && result.error) {\n        errors.push(result.error);\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n    };\n  }\n\n  /**\n   * Validate form data against schema\n   */\n  static validateFormData<T extends Record<string, unknown>>(\n    data: T,\n    schema: Record<keyof T, (value: unknown) => FieldValidationResult>\n  ): Record<keyof T, string | undefined> {\n    const errors: Record<keyof T, string | undefined> = {} as Record<keyof T, string | undefined>;\n\n    for (const [field, validator] of Object.entries(schema)) {\n      const result = validator(data[field as keyof T]);\n      if (!result.isValid && result.error) {\n        errors[field as keyof T] = result.error;\n      }\n    }\n\n    return errors;\n  }\n\n  /**\n   * Validate single field\n   */\n  static validateField(\n    value: unknown,\n    validator: (value: unknown) => FieldValidationResult\n  ): FieldValidationResult {\n    return validator(value);\n  }\n}\n\n/**\n * Real-time validation hook utilities\n */\nexport class ValidationHooks {\n  /**\n   * Debounced validation for real-time feedback\n   */\n  static createDebouncedValidator(\n    validator: (value: unknown) => FieldValidationResult,\n    delay: number = 300\n  ) {\n    let timeoutId: NodeJS.Timeout;\n\n    return (value: unknown, callback: (result: FieldValidationResult) => void) => {\n      clearTimeout(timeoutId);\n      timeoutId = setTimeout(() => {\n        const result = validator(value);\n        callback(result);\n      }, delay);\n    };\n  }\n}\n\n/**\n * Common validation schemas for forms\n */\nexport const ValidationSchemas = {\n  contentGeneration: {\n    topic: (value: unknown) => ContentValidation.validateTopic(value as string),\n    style: (value: unknown) => ContentValidation.validateStyle(value as string),\n    language: (value: unknown) => ContentValidation.validateLanguage(value as string),\n  },\n\n  userRegistration: {\n    username: (value: unknown) => ValidationUtils.validateUsername(value as string),\n    email: (value: unknown) => ValidationUtils.validateEmail(value as string),\n    password: (value: unknown) => ValidationUtils.validatePassword(value as string),\n  },\n\n  userLogin: {\n    email: (value: unknown) => ValidationUtils.validateEmail(value as string),\n    password: (value: unknown) => ValidationUtils.validateRequired(value, 'Password'),\n  },\n};\n\n/**\n * Utility functions for common validation patterns\n */\nexport const validateContentGenerationForm = (data: {\n  topic: string;\n  style?: string;\n  language?: string;\n}) => {\n  const errors: Record<string, string | undefined> = {};\n\n  const topicValidation = ValidationSchemas.contentGeneration.topic(data.topic);\n  if (!topicValidation.isValid && topicValidation.error) {\n    errors.topic = topicValidation.error;\n  }\n\n  return errors;\n};\n\nexport const validateUserRegistrationForm = (data: {\n  username: string;\n  email: string;\n  password: string;\n}) => {\n  return FormValidation.validateFormData(data, ValidationSchemas.userRegistration);\n};\n\nexport const validateUserLoginForm = (data: {\n  email: string;\n  password: string;\n}) => {\n  return FormValidation.validateFormData(data, ValidationSchemas.userLogin);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;;AAqBO,MAAM;IACX;;GAEC,GACD,OAAO,iBAAiB,KAAc,EAAE,YAAoB,YAAY,EAAyB;QAC/F,IAAI,UAAU,QAAQ,UAAU,WAAW;YACzC,+DAA+D;YAC/D,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,IAAI,OAAO,UAAU,YAAY,CAAC,MAAM,IAAI,IAAI;YAC9C,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;YAC9C,IAAI,UAAU,WAAW,GAAG,QAAQ,CAAC,eAAe,UAAU,WAAW,GAAG,QAAQ,CAAC,UAAU;gBAC7F,OAAO;oBAAE,SAAS;oBAAO,OAAO;gBAAU;YAC5C;YACA,OAAO;gBAAE,SAAS;gBAAO,OAAO,GAAG,UAAU,YAAY,CAAC;YAAC;QAC7D;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,eACL,KAAa,EACb,SAAkB,EAClB,SAAkB,EACK;QACvB,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAyB;QAC3D;QAEA,MAAM,SAAS,MAAM,MAAM;QAE3B,IAAI,cAAc,aAAa,SAAS,WAAW;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,iBAAiB,EAAE,UAAU,gBAAgB,CAAC;YACxD;QACF;QAEA,IAAI,cAAc,aAAa,SAAS,WAAW;YACjD,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,qBAAqB,EAAE,UAAU,gBAAgB,CAAC;YAC5D;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,IAAI,CAAC,OAAO;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoB;QACtD;QAEA,IAAI,CAAC,uHAAA,CAAA,iBAAc,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,gBAAgB,KAAa,EAAE,OAAe,EAAE,eAAuB,gBAAgB,EAAyB;QACrH,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;YACxB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAE,GAAW,EAAE,GAAW,EAAyB;QACnF,IAAI,QAAQ,OAAO,QAAQ,KAAK;YAC9B,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,gBAAgB,EAAE,IAAI,KAAK,EAAE,KAAK;YAAC;QACtE;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,eAAe,KAAa,EAAE,OAAqC,EAAE,eAAuB,8BAA8B,EAAyB;QACxJ,IAAI,CAAC,QAAQ,QAAQ,CAAC,QAAQ;YAC5B,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAIA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,iBAAiB,IAAI,CAAC,gBAAgB,CAAC,UAAU;QACvD,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,eAAe,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QACtD,IAAI,CAAC,aAAa,OAAO,EAAE,OAAO;QAElC,IAAI,CAAC,uHAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;YAC3C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA8D;QAChG;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,iBAAiB,IAAI,CAAC,gBAAgB,CAAC,UAAU;QACvD,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,IAAI,SAAS,MAAM,GAAG,uHAAA,CAAA,mBAAgB,CAAC,mBAAmB,EAAE;YAC1D,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,0BAA0B,EAAE,uHAAA,CAAA,mBAAgB,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;YAC5F;QACF;QAEA,IAAI,CAAC,uHAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;YAC3C,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,MAAM,iBAAiB,gBAAgB,gBAAgB,CAAC,OAAO;QAC/D,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,SAAS,MAAM,MAAM;QAC3B,IAAI,SAAS,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,EAAE;YAC9C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,uBAAuB,EAAE,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YACtF;QACF;QAEA,IAAI,SAAS,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,EAAE;YAC9C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,2BAA2B,EAAE,uHAAA,CAAA,mBAAgB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YAC1F;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAe,EAAyB;QAC7D,MAAM,iBAAiB,gBAAgB,gBAAgB,CAAC,SAAS;QACjE,IAAI,CAAC,eAAe,OAAO,EAAE,OAAO;QAEpC,MAAM,SAAS,QAAQ,MAAM;QAC7B,IAAI,SAAS,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB,EAAE;YAC/C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,6BAA6B,EAAE,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;YAC7F;QACF;QAEA,MAAM,WAAqB,EAAE;QAE7B,8BAA8B;QAC9B,MAAM,eAAe,kBAAkB,aAAa,CAAC;QACrD,IAAI,eAAe,GAAG;YACpB,SAAS,IAAI,CAAC;QAChB;QAEA,8BAA8B;QAC9B,MAAM,eAAe,kBAAkB,aAAa,CAAC;QACrD,IAAI,eAAe,GAAG;YACpB,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO;YACL,SAAS;YACT,UAAU,SAAS,MAAM,GAAG,IAAI,WAAW;QAC7C;IACF;IAEA;;GAEC,GACD,OAAO,cAAc,KAAa,EAAyB;QACzD,OAAO,gBAAgB,cAAc,CACnC,OACA,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB,EAClC;IAEJ;IAEA;;GAEC,GACD,OAAO,iBAAiB,QAAgB,EAAyB;QAC/D,MAAM,qBAAqB,uHAAA,CAAA,oBAAiB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;QACtF,OAAO,gBAAgB,cAAc,CACnC,UACA,oBACA;IAEJ;IAEA;;GAEC,GACD,OAAO,mBAAmB,IAAY,EAAyB;QAC7D,IAAI,OAAO,GAAG;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAqC;QACvE;QACA,IAAI,OAAO,uHAAA,CAAA,oBAAiB,CAAC,iBAAiB,EAAE;YAC9C,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,6BAA6B,EAAE,uHAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAAC;QAC/G;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAIA;;GAEC,GACD,OAAO,cAAc,OAAe,EAAU;QAC5C,MAAM,UAAU,QAAQ,KAAK,CAAC,uHAAA,CAAA,iBAAc,CAAC,OAAO;QACpD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,cAAc,OAAe,EAAU;QAC5C,MAAM,UAAU,QAAQ,KAAK,CAAC,uHAAA,CAAA,iBAAc,CAAC,OAAO;QACpD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,UAAU,OAAe,EAAU;QACxC,MAAM,UAAU,QAAQ,KAAK,CAAC,uHAAA,CAAA,iBAAc,CAAC,GAAG;QAChD,OAAO,UAAU,QAAQ,MAAM,GAAG;IACpC;IAEA;;GAEC,GACD,OAAO,gBAAgB,OAAe,EAAE;QACtC,MAAM,iBAAiB,QAAQ,MAAM;QACrC,MAAM,YAAY,QAAQ,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM,GAAG;QAExE,OAAO;YACL;YACA;YACA,cAAc,kBAAkB,aAAa,CAAC;YAC9C,cAAc,kBAAkB,aAAa,CAAC;YAC9C,UAAU,kBAAkB,SAAS,CAAC;YACtC,mCAAmC;YACnC,QAAQ;YACR,WAAW,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB,GAAG;QAClD;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,eACL,WAA+C,EAC7B;QAClB,MAAM,SAAmB,EAAE;QAE3B,KAAK,MAAM,cAAc,YAAa;YACpC,MAAM,SAAS;YACf,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBACnC,OAAO,IAAI,CAAC,OAAO,KAAK;YAC1B;QACF;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;QACF;IACF;IAEA;;GAEC,GACD,OAAO,iBACL,IAAO,EACP,MAAkE,EAC7B;QACrC,MAAM,SAA8C,CAAC;QAErD,KAAK,MAAM,CAAC,OAAO,UAAU,IAAI,OAAO,OAAO,CAAC,QAAS;YACvD,MAAM,SAAS,UAAU,IAAI,CAAC,MAAiB;YAC/C,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBACnC,MAAM,CAAC,MAAiB,GAAG,OAAO,KAAK;YACzC;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,cACL,KAAc,EACd,SAAoD,EAC7B;QACvB,OAAO,UAAU;IACnB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,yBACL,SAAoD,EACpD,QAAgB,GAAG,EACnB;QACA,IAAI;QAEJ,OAAO,CAAC,OAAgB;YACtB,aAAa;YACb,YAAY,WAAW;gBACrB,MAAM,SAAS,UAAU;gBACzB,SAAS;YACX,GAAG;QACL;IACF;AACF;AAKO,MAAM,oBAAoB;IAC/B,mBAAmB;QACjB,OAAO,CAAC,QAAmB,kBAAkB,aAAa,CAAC;QAC3D,OAAO,CAAC,QAAmB,kBAAkB,aAAa,CAAC;QAC3D,UAAU,CAAC,QAAmB,kBAAkB,gBAAgB,CAAC;IACnE;IAEA,kBAAkB;QAChB,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC;QAC/D,OAAO,CAAC,QAAmB,gBAAgB,aAAa,CAAC;QACzD,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC;IACjE;IAEA,WAAW;QACT,OAAO,CAAC,QAAmB,gBAAgB,aAAa,CAAC;QACzD,UAAU,CAAC,QAAmB,gBAAgB,gBAAgB,CAAC,OAAO;IACxE;AACF;AAKO,MAAM,gCAAgC,CAAC;IAK5C,MAAM,SAA6C,CAAC;IAEpD,MAAM,kBAAkB,kBAAkB,iBAAiB,CAAC,KAAK,CAAC,KAAK,KAAK;IAC5E,IAAI,CAAC,gBAAgB,OAAO,IAAI,gBAAgB,KAAK,EAAE;QACrD,OAAO,KAAK,GAAG,gBAAgB,KAAK;IACtC;IAEA,OAAO;AACT;AAEO,MAAM,+BAA+B,CAAC;IAK3C,OAAO,eAAe,gBAAgB,CAAC,MAAM,kBAAkB,gBAAgB;AACjF;AAEO,MAAM,wBAAwB,CAAC;IAIpC,OAAO,eAAe,gBAAgB,CAAC,MAAM,kBAAkB,SAAS;AAC1E", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/errorHandling.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Frontend error handling utilities following DRY and KISS principles.\n * Centralizes error handling logic to eliminate code duplication.\n */\n\nimport { HTTP_STATUS, ERROR_MESSAGES, getErrorMessageByStatus } from './constants';\n\nexport interface ApiError {\n  message: string;\n  status?: number;\n  code?: string;\n  details?: Record<string, unknown>;\n}\n\nexport interface ErrorHandlerOptions {\n  showToast?: boolean;\n  logError?: boolean;\n  fallbackMessage?: string;\n}\n\nexport interface ErrorResult {\n  type: string;\n  message: string;\n  userMessage: string;\n  context?: any;\n  timestamp: number;\n}\n\n// Error type constants\nexport const ErrorTypes = {\n  NETWORK: 'NETWORK',\n  VALIDATION: 'VALIDATION',\n  AUTHENTICATION: 'AUTHENTICATION',\n  AUTHORIZATION: 'AUTHORIZATION',\n  RATE_LIMIT: 'RATE_LIMIT',\n  SERVER: 'SERVER',\n  CLIENT: 'CLIENT',\n  UNKNOWN: 'UNKNOWN'\n} as const;\n\n/**\n * Base error handling utilities\n */\nexport class ErrorHandler {\n  /**\n   * Main error handler that processes errors and returns structured result\n   */\n  static handleError(error: unknown, context?: any): ErrorResult {\n    const message = this.getErrorMessage(error);\n    const type = this.getErrorType(error);\n    const userMessage = this.getUserFriendlyMessage(type);\n\n    const result: ErrorResult = {\n      type,\n      message,\n      userMessage,\n      context,\n      timestamp: Date.now()\n    };\n\n    this.logError(error, context);\n\n    return result;\n  }\n\n  /**\n   * Get error message from various error types\n   */\n  static getErrorMessage(error: unknown): string {\n    if (error instanceof Error) {\n      return error.message;\n    }\n    if (typeof error === 'string') {\n      return error;\n    }\n    if (error && typeof error === 'object' && 'message' in error) {\n      return String((error as any).message);\n    }\n    return 'An unknown error occurred';\n  }\n\n  /**\n   * Determine error type based on error content\n   */\n  static getErrorType(error: unknown): string {\n    const message = this.getErrorMessage(error).toLowerCase();\n\n    if (message.includes('network') || message.includes('fetch')) {\n      return ErrorTypes.NETWORK;\n    }\n    if (message.includes('validation') || message.includes('invalid')) {\n      return ErrorTypes.VALIDATION;\n    }\n    if (message.includes('unauthorized') || message.includes('auth')) {\n      return ErrorTypes.AUTHENTICATION;\n    }\n    if (message.includes('forbidden')) {\n      return ErrorTypes.AUTHORIZATION;\n    }\n    if (message.includes('too many requests') || message.includes('rate limit')) {\n      return ErrorTypes.RATE_LIMIT;\n    }\n    if (message.includes('server error') || message.includes('internal')) {\n      return ErrorTypes.SERVER;\n    }\n    if (message.includes('bad request') || message.includes('client')) {\n      return ErrorTypes.CLIENT;\n    }\n\n    return ErrorTypes.UNKNOWN;\n  }\n\n  /**\n   * Get user-friendly message for error type\n   */\n  static getUserFriendlyMessage(errorType: string): string {\n    switch (errorType) {\n      case ErrorTypes.NETWORK:\n        return 'Network error. Please check your connection.';\n      case ErrorTypes.VALIDATION:\n        return 'Please check your input and try again.';\n      case ErrorTypes.AUTHENTICATION:\n        return 'Please log in to continue.';\n      case ErrorTypes.AUTHORIZATION:\n        return 'You do not have permission to perform this action.';\n      case ErrorTypes.RATE_LIMIT:\n        return 'Too many requests. Please wait a moment and try again.';\n      case ErrorTypes.SERVER:\n        return 'Server error. Please try again later.';\n      case ErrorTypes.CLIENT:\n        return 'Invalid request. Please check your input.';\n      default:\n        return 'An unexpected error occurred. Please try again.';\n    }\n  }\n\n  /**\n   * Check if error is retryable\n   */\n  static isRetryableError(error: unknown): boolean {\n    const type = this.getErrorType(error);\n    const message = this.getErrorMessage(error).toLowerCase();\n\n    // Network errors are retryable\n    if (type === ErrorTypes.NETWORK) {\n      return true;\n    }\n\n    // Timeout errors are retryable\n    if (message.includes('timeout')) {\n      return true;\n    }\n\n    // Server errors (5xx) are retryable\n    if (type === ErrorTypes.SERVER || message.includes('server error')) {\n      return true;\n    }\n\n    // Rate limit errors are retryable (after delay)\n    if (type === ErrorTypes.RATE_LIMIT) {\n      return true;\n    }\n\n    // Authentication, validation, and client errors are not retryable\n    return false;\n  }\n\n  /**\n   * Log error with context\n   */\n  static logError(error: unknown, context?: any): void {\n    const message = this.getErrorMessage(error);\n    const logData = {\n      message,\n      context,\n      timestamp: new Date().toISOString(),\n      stack: error instanceof Error ? error.stack : undefined\n    };\n\n    console.error('Error occurred:', logData);\n  }\n\n  /**\n   * Handle API errors with consistent formatting\n   */\n  static handleApiError(\n    error: unknown,\n    options: ErrorHandlerOptions = {}\n  ): ApiError {\n    const {\n      showToast = false,\n      logError = true,\n      fallbackMessage = ERROR_MESSAGES.GENERIC_ERROR\n    } = options;\n\n    let apiError: ApiError;\n\n    // Type guard for error objects\n    const isErrorWithResponse = (err: unknown): err is { response: { status: number; data?: { message?: string; code?: string; details?: Record<string, unknown> } } } => {\n      return typeof err === 'object' && err !== null && 'response' in err;\n    };\n\n    const isErrorWithRequest = (err: unknown): err is { request: unknown } => {\n      return typeof err === 'object' && err !== null && 'request' in err;\n    };\n\n    const isErrorWithMessage = (err: unknown): err is { message: string } => {\n      return typeof err === 'object' && err !== null && 'message' in err;\n    };\n\n    if (isErrorWithResponse(error)) {\n      // HTTP error response\n      const status = error.response.status;\n      const data = error.response.data;\n\n      apiError = {\n        message: data?.message || getErrorMessageByStatus(status),\n        status,\n        code: data?.code,\n        details: data?.details\n      };\n    } else if (isErrorWithRequest(error)) {\n      // Network error\n      apiError = {\n        message: ERROR_MESSAGES.NETWORK_ERROR,\n        status: 0,\n        code: 'NETWORK_ERROR'\n      };\n    } else {\n      // Other error\n      apiError = {\n        message: isErrorWithMessage(error) ? error.message : fallbackMessage,\n        code: 'UNKNOWN_ERROR'\n      };\n    }\n\n    if (logError) {\n      console.error('API Error:', apiError, error);\n    }\n\n    if (showToast) {\n      // This would integrate with your toast system\n      // toast.error(apiError.message);\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle validation errors\n   */\n  static handleValidationError(\n    errors: Record<string, string> | string[] | unknown,\n    options: ErrorHandlerOptions = {}\n  ): ApiError {\n    const { logError = true } = options;\n\n    let message: string;\n\n    if (Array.isArray(errors)) {\n      message = errors.join(', ');\n    } else if (typeof errors === 'object' && errors !== null) {\n      message = Object.values(errors).join(', ');\n    } else {\n      message = ERROR_MESSAGES.VALIDATION_ERROR;\n    }\n\n    const apiError: ApiError = {\n      message,\n      status: HTTP_STATUS.UNPROCESSABLE_ENTITY,\n      code: 'VALIDATION_ERROR',\n      details: typeof errors === 'object' && errors !== null ? errors as Record<string, unknown> : undefined\n    };\n\n    if (logError) {\n      console.warn('Validation Error:', apiError);\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle async operation errors\n   */\n  static async handleAsyncError<T>(\n    operation: () => Promise<T>,\n    options: ErrorHandlerOptions = {}\n  ): Promise<{ data?: T; error?: ApiError }> {\n    try {\n      const data = await operation();\n      return { data };\n    } catch (error) {\n      const apiError = this.handleApiError(error, options);\n      return { error: apiError };\n    }\n  }\n\n  /**\n   * Create error boundary handler\n   */\n  static createErrorBoundaryHandler(\n    fallbackComponent?: React.ComponentType<{ error: Error }>\n  ) {\n    return (error: Error, errorInfo: any) => {\n      console.error('Error Boundary caught an error:', error, errorInfo);\n\n      // Log to error reporting service\n      // errorReportingService.captureException(error, errorInfo);\n\n      return fallbackComponent;\n    };\n  }\n}\n\n/**\n * Specific error handlers for different scenarios\n */\nexport class ContentErrorHandler {\n  /**\n   * Handle content generation errors\n   */\n  static handleGenerationError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.GENERATION_FAILED,\n      logError: true\n    });\n  }\n\n  /**\n   * Handle content validation errors\n   */\n  static handleContentValidationError(errors: any): ApiError {\n    return ErrorHandler.handleValidationError(errors, {\n      logError: true\n    });\n  }\n}\n\n/**\n * Authentication error handlers\n */\nexport class AuthErrorHandler {\n  /**\n   * Handle authentication errors\n   */\n  static handleAuthError(error: any): ApiError {\n    const apiError = ErrorHandler.handleApiError(error, {\n      logError: true\n    });\n\n    // Handle specific auth scenarios\n    if (apiError.status === HTTP_STATUS.UNAUTHORIZED) {\n      // Clear auth tokens\n      localStorage.removeItem('authToken');\n\n      // Redirect to login if needed\n      // router.push('/login');\n    }\n\n    return apiError;\n  }\n\n  /**\n   * Handle token expiration\n   */\n  static handleTokenExpiration(): void {\n    localStorage.removeItem('authToken');\n    // Show token expired message\n    // toast.error(ERROR_MESSAGES.UNAUTHORIZED);\n    // Redirect to login\n    // router.push('/login');\n  }\n}\n\n/**\n * Network error handlers\n */\nexport class NetworkErrorHandler {\n  /**\n   * Handle network connectivity issues\n   */\n  static handleNetworkError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.NETWORK_ERROR,\n      logError: true\n    });\n  }\n\n  /**\n   * Handle rate limiting\n   */\n  static handleRateLimitError(error: any): ApiError {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: ERROR_MESSAGES.RATE_LIMIT_EXCEEDED,\n      logError: true\n    });\n  }\n}\n\n/**\n * Error retry utilities\n */\nexport class RetryHandler {\n  /**\n   * Retry failed operations with exponential backoff\n   */\n  static async retryOperation<T>(\n    operation: () => Promise<T>,\n    maxRetries: number = 3,\n    baseDelay: number = 1000\n  ): Promise<T> {\n    let lastError: any;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        return await operation();\n      } catch (error) {\n        lastError = error;\n\n        if (attempt === maxRetries) {\n          throw error;\n        }\n\n        // Exponential backoff\n        const delay = baseDelay * Math.pow(2, attempt);\n        await new Promise(resolve => setTimeout(resolve, delay));\n      }\n    }\n\n    throw lastError;\n  }\n\n  /**\n   * Check if error is retryable\n   */\n  static isRetryableError(error: any): boolean {\n    if (!error.response) {\n      return true; // Network errors are retryable\n    }\n\n    const status = error.response.status;\n    return status >= 500 || status === HTTP_STATUS.TOO_MANY_REQUESTS;\n  }\n}\n\n/**\n * Error logging utilities\n */\nexport class ErrorLogger {\n  /**\n   * Log error with timestamp and context\n   */\n  static log(error: unknown, context?: any): void {\n    const timestamp = new Date().toISOString();\n    const message = error instanceof Error ? error.message : String(error);\n\n    if (context) {\n      console.error(`[ERROR] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.error(`[ERROR] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log warning with timestamp and context\n   */\n  static warn(message: string, context?: any): void {\n    const timestamp = new Date().toISOString();\n\n    if (context) {\n      console.warn(`[WARN] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.warn(`[WARN] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log info message\n   */\n  static info(message: string, context?: any): void {\n    const timestamp = new Date().toISOString();\n\n    if (context) {\n      console.log(`[INFO] ${timestamp}`, message, 'Context:', context);\n    } else {\n      console.log(`[INFO] ${timestamp}`, message);\n    }\n  }\n\n  /**\n   * Log debug message (only in development)\n   */\n  static debug(message: string, context?: any): void {\n    if (process.env.NODE_ENV === 'development') {\n      const timestamp = new Date().toISOString();\n\n      if (context) {\n        console.log(`[DEBUG] ${timestamp}`, message, 'Context:', context);\n      } else {\n        console.log(`[DEBUG] ${timestamp}`, message);\n      }\n    }\n  }\n\n  /**\n   * Log error to console with context (legacy method)\n   */\n  static logError(\n    error: any,\n    context: string,\n    additionalData?: any\n  ): void {\n    console.group(`🚨 Error in ${context}`);\n    console.error('Error:', error);\n    if (additionalData) {\n      console.error('Additional Data:', additionalData);\n    }\n    console.error('Stack:', error.stack);\n    console.groupEnd();\n  }\n\n  /**\n   * Log warning with context (legacy method)\n   */\n  static logWarning(\n    message: string,\n    context: string,\n    additionalData?: any\n  ): void {\n    console.group(`⚠️ Warning in ${context}`);\n    console.warn('Message:', message);\n    if (additionalData) {\n      console.warn('Additional Data:', additionalData);\n    }\n    console.groupEnd();\n  }\n}\n\n/**\n * Utility functions for common error handling patterns\n */\nexport const withErrorHandling = <T extends any[], R>(\n  fn: (...args: T) => Promise<R>,\n  errorHandler?: (error: any) => void\n) => {\n  return async (...args: T): Promise<R | undefined> => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      if (errorHandler) {\n        errorHandler(error);\n      } else {\n        ErrorHandler.handleApiError(error, { logError: true });\n      }\n      return undefined;\n    }\n  };\n};\n\nexport const createAsyncErrorHandler = (\n  defaultErrorMessage: string = ERROR_MESSAGES.GENERIC_ERROR\n) => {\n  return (error: any) => {\n    return ErrorHandler.handleApiError(error, {\n      fallbackMessage: defaultErrorMessage,\n      logError: true\n    });\n  };\n};\n\n// Export commonly used error handlers\nexport const handleContentError = ContentErrorHandler.handleGenerationError;\nexport const handleAuthError = AuthErrorHandler.handleAuthError;\nexport const handleNetworkError = NetworkErrorHandler.handleNetworkError;\n"], "names": [], "mappings": "AAAA,qDAAqD,GACrD;;;CAGC;;;;;;;;;;;;;;AAED;;AAwBO,MAAM,aAAa;IACxB,SAAS;IACT,YAAY;IACZ,gBAAgB;IAChB,eAAe;IACf,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,SAAS;AACX;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,YAAY,KAAc,EAAE,OAAa,EAAe;QAC7D,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC;QACrC,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,MAAM,cAAc,IAAI,CAAC,sBAAsB,CAAC;QAEhD,MAAM,SAAsB;YAC1B;YACA;YACA;YACA;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,IAAI,CAAC,QAAQ,CAAC,OAAO;QAErB,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,gBAAgB,KAAc,EAAU;QAC7C,IAAI,iBAAiB,OAAO;YAC1B,OAAO,MAAM,OAAO;QACtB;QACA,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO;QACT;QACA,IAAI,SAAS,OAAO,UAAU,YAAY,aAAa,OAAO;YAC5D,OAAO,OAAO,AAAC,MAAc,OAAO;QACtC;QACA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,aAAa,KAAc,EAAU;QAC1C,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,OAAO,WAAW;QAEvD,IAAI,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,UAAU;YAC5D,OAAO,WAAW,OAAO;QAC3B;QACA,IAAI,QAAQ,QAAQ,CAAC,iBAAiB,QAAQ,QAAQ,CAAC,YAAY;YACjE,OAAO,WAAW,UAAU;QAC9B;QACA,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,SAAS;YAChE,OAAO,WAAW,cAAc;QAClC;QACA,IAAI,QAAQ,QAAQ,CAAC,cAAc;YACjC,OAAO,WAAW,aAAa;QACjC;QACA,IAAI,QAAQ,QAAQ,CAAC,wBAAwB,QAAQ,QAAQ,CAAC,eAAe;YAC3E,OAAO,WAAW,UAAU;QAC9B;QACA,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,aAAa;YACpE,OAAO,WAAW,MAAM;QAC1B;QACA,IAAI,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,WAAW;YACjE,OAAO,WAAW,MAAM;QAC1B;QAEA,OAAO,WAAW,OAAO;IAC3B;IAEA;;GAEC,GACD,OAAO,uBAAuB,SAAiB,EAAU;QACvD,OAAQ;YACN,KAAK,WAAW,OAAO;gBACrB,OAAO;YACT,KAAK,WAAW,UAAU;gBACxB,OAAO;YACT,KAAK,WAAW,cAAc;gBAC5B,OAAO;YACT,KAAK,WAAW,aAAa;gBAC3B,OAAO;YACT,KAAK,WAAW,UAAU;gBACxB,OAAO;YACT,KAAK,WAAW,MAAM;gBACpB,OAAO;YACT,KAAK,WAAW,MAAM;gBACpB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,OAAO,iBAAiB,KAAc,EAAW;QAC/C,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,OAAO,WAAW;QAEvD,+BAA+B;QAC/B,IAAI,SAAS,WAAW,OAAO,EAAE;YAC/B,OAAO;QACT;QAEA,+BAA+B;QAC/B,IAAI,QAAQ,QAAQ,CAAC,YAAY;YAC/B,OAAO;QACT;QAEA,oCAAoC;QACpC,IAAI,SAAS,WAAW,MAAM,IAAI,QAAQ,QAAQ,CAAC,iBAAiB;YAClE,OAAO;QACT;QAEA,gDAAgD;QAChD,IAAI,SAAS,WAAW,UAAU,EAAE;YAClC,OAAO;QACT;QAEA,kEAAkE;QAClE,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,SAAS,KAAc,EAAE,OAAa,EAAQ;QACnD,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC;QACrC,MAAM,UAAU;YACd;YACA;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,OAAO,iBAAiB,QAAQ,MAAM,KAAK,GAAG;QAChD;QAEA,QAAQ,KAAK,CAAC,mBAAmB;IACnC;IAEA;;GAEC,GACD,OAAO,eACL,KAAc,EACd,UAA+B,CAAC,CAAC,EACvB;QACV,MAAM,EACJ,YAAY,KAAK,EACjB,WAAW,IAAI,EACf,kBAAkB,uHAAA,CAAA,iBAAc,CAAC,aAAa,EAC/C,GAAG;QAEJ,IAAI;QAEJ,+BAA+B;QAC/B,MAAM,sBAAsB,CAAC;YAC3B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,cAAc;QAClE;QAEA,MAAM,qBAAqB,CAAC;YAC1B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,aAAa;QACjE;QAEA,MAAM,qBAAqB,CAAC;YAC1B,OAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,aAAa;QACjE;QAEA,IAAI,oBAAoB,QAAQ;YAC9B,sBAAsB;YACtB,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;YACpC,MAAM,OAAO,MAAM,QAAQ,CAAC,IAAI;YAEhC,WAAW;gBACT,SAAS,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,0BAAuB,AAAD,EAAE;gBAClD;gBACA,MAAM,MAAM;gBACZ,SAAS,MAAM;YACjB;QACF,OAAO,IAAI,mBAAmB,QAAQ;YACpC,gBAAgB;YAChB,WAAW;gBACT,SAAS,uHAAA,CAAA,iBAAc,CAAC,aAAa;gBACrC,QAAQ;gBACR,MAAM;YACR;QACF,OAAO;YACL,cAAc;YACd,WAAW;gBACT,SAAS,mBAAmB,SAAS,MAAM,OAAO,GAAG;gBACrD,MAAM;YACR;QACF;QAEA,IAAI,UAAU;YACZ,QAAQ,KAAK,CAAC,cAAc,UAAU;QACxC;QAEA,IAAI,WAAW;QACb,8CAA8C;QAC9C,iCAAiC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,sBACL,MAAmD,EACnD,UAA+B,CAAC,CAAC,EACvB;QACV,MAAM,EAAE,WAAW,IAAI,EAAE,GAAG;QAE5B,IAAI;QAEJ,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,UAAU,OAAO,IAAI,CAAC;QACxB,OAAO,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;YACxD,UAAU,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC;QACvC,OAAO;YACL,UAAU,uHAAA,CAAA,iBAAc,CAAC,gBAAgB;QAC3C;QAEA,MAAM,WAAqB;YACzB;YACA,QAAQ,uHAAA,CAAA,cAAW,CAAC,oBAAoB;YACxC,MAAM;YACN,SAAS,OAAO,WAAW,YAAY,WAAW,OAAO,SAAoC;QAC/F;QAEA,IAAI,UAAU;YACZ,QAAQ,IAAI,CAAC,qBAAqB;QACpC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,iBACX,SAA2B,EAC3B,UAA+B,CAAC,CAAC,EACQ;QACzC,IAAI;YACF,MAAM,OAAO,MAAM;YACnB,OAAO;gBAAE;YAAK;QAChB,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,OAAO;YAC5C,OAAO;gBAAE,OAAO;YAAS;QAC3B;IACF;IAEA;;GAEC,GACD,OAAO,2BACL,iBAAyD,EACzD;QACA,OAAO,CAAC,OAAc;YACpB,QAAQ,KAAK,CAAC,mCAAmC,OAAO;YAExD,iCAAiC;YACjC,4DAA4D;YAE5D,OAAO;QACT;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,sBAAsB,KAAU,EAAY;QACjD,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,uHAAA,CAAA,iBAAc,CAAC,iBAAiB;YACjD,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,6BAA6B,MAAW,EAAY;QACzD,OAAO,aAAa,qBAAqB,CAAC,QAAQ;YAChD,UAAU;QACZ;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,gBAAgB,KAAU,EAAY;QAC3C,MAAM,WAAW,aAAa,cAAc,CAAC,OAAO;YAClD,UAAU;QACZ;QAEA,iCAAiC;QACjC,IAAI,SAAS,MAAM,KAAK,uHAAA,CAAA,cAAW,CAAC,YAAY,EAAE;YAChD,oBAAoB;YACpB,aAAa,UAAU,CAAC;QAExB,8BAA8B;QAC9B,yBAAyB;QAC3B;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,wBAA8B;QACnC,aAAa,UAAU,CAAC;IACxB,6BAA6B;IAC7B,4CAA4C;IAC5C,oBAAoB;IACpB,yBAAyB;IAC3B;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,mBAAmB,KAAU,EAAY;QAC9C,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,uHAAA,CAAA,iBAAc,CAAC,aAAa;YAC7C,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,OAAO,qBAAqB,KAAU,EAAY;QAChD,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB,uHAAA,CAAA,iBAAc,CAAC,mBAAmB;YACnD,UAAU;QACZ;IACF;AACF;AAKO,MAAM;IACX;;GAEC,GACD,aAAa,eACX,SAA2B,EAC3B,aAAqB,CAAC,EACtB,YAAoB,IAAI,EACZ;QACZ,IAAI;QAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;YACtD,IAAI;gBACF,OAAO,MAAM;YACf,EAAE,OAAO,OAAO;gBACd,YAAY;gBAEZ,IAAI,YAAY,YAAY;oBAC1B,MAAM;gBACR;gBAEA,sBAAsB;gBACtB,MAAM,QAAQ,YAAY,KAAK,GAAG,CAAC,GAAG;gBACtC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;QACF;QAEA,MAAM;IACR;IAEA;;GAEC,GACD,OAAO,iBAAiB,KAAU,EAAW;QAC3C,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB,OAAO,MAAM,+BAA+B;QAC9C;QAEA,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;QACpC,OAAO,UAAU,OAAO,WAAW,uHAAA,CAAA,cAAW,CAAC,iBAAiB;IAClE;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,IAAI,KAAc,EAAE,OAAa,EAAQ;QAC9C,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QAEhE,IAAI,SAAS;YACX,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,YAAY;QAC7D,OAAO;YACL,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE;QACxC;IACF;IAEA;;GAEC,GACD,OAAO,KAAK,OAAe,EAAE,OAAa,EAAQ;QAChD,MAAM,YAAY,IAAI,OAAO,WAAW;QAExC,IAAI,SAAS;YACX,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,YAAY;QAC3D,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE;QACtC;IACF;IAEA;;GAEC,GACD,OAAO,KAAK,OAAe,EAAE,OAAa,EAAQ;QAChD,MAAM,YAAY,IAAI,OAAO,WAAW;QAExC,IAAI,SAAS;YACX,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,YAAY;QAC1D,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE;QACrC;IACF;IAEA;;GAEC,GACD,OAAO,MAAM,OAAe,EAAE,OAAa,EAAQ;QACjD,wCAA4C;YAC1C,MAAM,YAAY,IAAI,OAAO,WAAW;YAExC,IAAI,SAAS;gBACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,YAAY;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,EAAE;YACtC;QACF;IACF;IAEA;;GAEC,GACD,OAAO,SACL,KAAU,EACV,OAAe,EACf,cAAoB,EACd;QACN,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,SAAS;QACtC,QAAQ,KAAK,CAAC,UAAU;QACxB,IAAI,gBAAgB;YAClB,QAAQ,KAAK,CAAC,oBAAoB;QACpC;QACA,QAAQ,KAAK,CAAC,UAAU,MAAM,KAAK;QACnC,QAAQ,QAAQ;IAClB;IAEA;;GAEC,GACD,OAAO,WACL,OAAe,EACf,OAAe,EACf,cAAoB,EACd;QACN,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS;QACxC,QAAQ,IAAI,CAAC,YAAY;QACzB,IAAI,gBAAgB;YAClB,QAAQ,IAAI,CAAC,oBAAoB;QACnC;QACA,QAAQ,QAAQ;IAClB;AACF;AAKO,MAAM,oBAAoB,CAC/B,IACA;IAEA,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,MAAM;QACrB,EAAE,OAAO,OAAO;YACd,IAAI,cAAc;gBAChB,aAAa;YACf,OAAO;gBACL,aAAa,cAAc,CAAC,OAAO;oBAAE,UAAU;gBAAK;YACtD;YACA,OAAO;QACT;IACF;AACF;AAEO,MAAM,0BAA0B,CACrC,sBAA8B,uHAAA,CAAA,iBAAc,CAAC,aAAa;IAE1D,OAAO,CAAC;QACN,OAAO,aAAa,cAAc,CAAC,OAAO;YACxC,iBAAiB;YACjB,UAAU;QACZ;IACF;AACF;AAGO,MAAM,qBAAqB,oBAAoB,qBAAqB;AACpE,MAAM,kBAAkB,iBAAiB,eAAe;AACxD,MAAM,qBAAqB,oBAAoB,kBAAkB", "debugId": null}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/lib/contentService.ts"], "sourcesContent": ["import { ApiClient } from './apiClient';\nimport { ContentValidation, ValidationUtils } from './validation';\nimport { ContentErrorHandler } from './errorHandling';\nimport { TWITTER_CONSTANTS } from './constants';\nimport {\n  ContentGenerationRequest,\n  ThreadGenerationRequest,\n  ReplyGenerationRequest,\n  ContentResponse,\n  ContentHistoryResponse,\n} from '../types/api';\n\n// Legacy types for backward compatibility\nexport interface ContentRequest {\n  topic: string;\n  tone: 'professional' | 'casual' | 'humorous' | 'inspirational';\n  length: 'short' | 'medium' | 'long';\n  includeHashtags: boolean;\n  includeEmojis: boolean;\n}\n\nexport interface GeneratedContent {\n  id: string;\n  content: string;\n  hashtags: string[];\n  createdAt: string;\n  status: 'draft' | 'scheduled' | 'published';\n}\n\nexport interface ScheduleRequest {\n  contentId: string;\n  scheduledTime: string;\n}\n\nexport interface ContentFilters {\n  status?: 'draft' | 'scheduled' | 'published';\n  dateFrom?: string;\n  dateTo?: string;\n  limit?: number;\n  offset?: number;\n}\n\n// Content Service following Single Responsibility Principle\nexport class ContentService {\n  constructor(private apiClient: ApiClient) {}\n\n  // New backend API methods with error handling\n  async generateTweet(request: ContentGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateTweetRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-tweet', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async generateThread(request: ThreadGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateThreadRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-thread', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async generateReply(request: ReplyGenerationRequest): Promise<ContentResponse> {\n    try {\n      this.validateReplyRequest(request);\n      return await this.apiClient.post<ContentResponse>('/content/generate-reply', request as unknown as Record<string, unknown>);\n    } catch (error) {\n      throw ContentErrorHandler.handleGenerationError(error);\n    }\n  }\n\n  async getContentHistory(skip: number = 0, limit: number = 50): Promise<ContentHistoryResponse> {\n    const params = new URLSearchParams({\n      skip: skip.toString(),\n      limit: limit.toString()\n    });\n    return this.apiClient.get<ContentHistoryResponse>(`/content/history?${params}`);\n  }\n\n  // Legacy methods for backward compatibility\n  async generateContent(request: ContentRequest): Promise<GeneratedContent> {\n    this.validateContentRequest(request);\n    // Convert legacy request to new format\n    const newRequest: ContentGenerationRequest = {\n      topic: request.topic,\n      style: request.tone,\n      user_context: `Length: ${request.length}, Include hashtags: ${request.includeHashtags}, Include emojis: ${request.includeEmojis}`\n    };\n\n    const response = await this.generateTweet(newRequest);\n\n    // Convert response to legacy format\n    return {\n      id: Date.now().toString(),\n      content: response.content || '',\n      hashtags: [],\n      createdAt: new Date().toISOString(),\n      status: 'draft'\n    };\n  }\n\n  // TODO: Implement these methods when backend endpoints are ready\n  // For now, these are removed to eliminate dead code\n  //\n  // Future implementations:\n  // - getContent(): Use history endpoint with filtering\n  // - getContentById(): Implement backend endpoint for single content retrieval\n  // - updateContent(): Implement backend endpoint for content updates\n  // - scheduleContent(): Use scheduled posts API\n  // - deleteContent(): Implement backend endpoint for content deletion\n  // - publishContent(): Use Twitter posting API\n\n  // Private validation methods using centralized validation utilities\n  private validateTweetRequest(request: ContentGenerationRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n  }\n\n  private validateThreadRequest(request: ThreadGenerationRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n\n    if (request.num_tweets) {\n      if (request.num_tweets < 2) {\n        throw new Error('Thread must contain at least 2 tweets');\n      }\n      if (request.num_tweets > TWITTER_CONSTANTS.MAX_THREAD_TWEETS) {\n        throw new Error(`Thread cannot exceed ${TWITTER_CONSTANTS.MAX_THREAD_TWEETS} tweets`);\n      }\n    }\n  }\n\n  private validateReplyRequest(request: ReplyGenerationRequest): void {\n    const requiredValidation = ValidationUtils.validateRequired(request.original_tweet, 'Original tweet');\n    if (!requiredValidation.isValid) {\n      throw new Error(requiredValidation.error);\n    }\n\n    const lengthValidation = ValidationUtils.validateLength(\n      request.original_tweet,\n      1,\n      TWITTER_CONSTANTS.MAX_TWEET_LENGTH\n    );\n    if (!lengthValidation.isValid) {\n      throw new Error(lengthValidation.error);\n    }\n  }\n\n  private validateContentRequest(request: ContentRequest): void {\n    const topicValidation = ContentValidation.validateTopic(request.topic);\n    if (!topicValidation.isValid) {\n      throw new Error(topicValidation.error);\n    }\n  }\n\n  // Removed unused validation methods to eliminate dead code\n\n  private buildQueryParams(filters?: ContentFilters): string {\n    if (!filters) return '';\n\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, String(value));\n      }\n    });\n\n    const queryString = params.toString();\n    return queryString ? `?${queryString}` : '';\n  }\n}\n\n// Export singleton instance\nimport { apiClient } from './apiClient';\nexport const contentService = new ContentService(apiClient);\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAgLA,4BAA4B;AAC5B;;;;AAzIO,MAAM;;IACX,YAAY,AAAQ,SAAoB,CAAE;aAAtB,YAAA;IAAuB;IAE3C,8CAA8C;IAC9C,MAAM,cAAc,OAAiC,EAA4B;QAC/E,IAAI;YACF,IAAI,CAAC,oBAAoB,CAAC;YAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,2BAA2B;QAC/E,EAAE,OAAO,OAAO;YACd,MAAM,2HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,eAAe,OAAgC,EAA4B;QAC/E,IAAI;YACF,IAAI,CAAC,qBAAqB,CAAC;YAC3B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,4BAA4B;QAChF,EAAE,OAAO,OAAO;YACd,MAAM,2HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,cAAc,OAA+B,EAA4B;QAC7E,IAAI;YACF,IAAI,CAAC,oBAAoB,CAAC;YAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAkB,2BAA2B;QAC/E,EAAE,OAAO,OAAO;YACd,MAAM,2HAAA,CAAA,sBAAmB,CAAC,qBAAqB,CAAC;QAClD;IACF;IAEA,MAAM,kBAAkB,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAmC;QAC7F,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAyB,CAAC,iBAAiB,EAAE,QAAQ;IAChF;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB,OAAuB,EAA6B;QACxE,IAAI,CAAC,sBAAsB,CAAC;QAC5B,uCAAuC;QACvC,MAAM,aAAuC;YAC3C,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,IAAI;YACnB,cAAc,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,oBAAoB,EAAE,QAAQ,eAAe,CAAC,kBAAkB,EAAE,QAAQ,aAAa,EAAE;QACnI;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC;QAE1C,oCAAoC;QACpC,OAAO;YACL,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS,SAAS,OAAO,IAAI;YAC7B,UAAU,EAAE;YACZ,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;QACV;IACF;IAEA,iEAAiE;IACjE,oDAAoD;IACpD,EAAE;IACF,0BAA0B;IAC1B,sDAAsD;IACtD,8EAA8E;IAC9E,oEAAoE;IACpE,+CAA+C;IAC/C,qEAAqE;IACrE,8CAA8C;IAE9C,oEAAoE;IAC5D,qBAAqB,OAAiC,EAAQ;QACpE,MAAM,kBAAkB,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;IACF;IAEQ,sBAAsB,OAAgC,EAAQ;QACpE,MAAM,kBAAkB,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;QAEA,IAAI,QAAQ,UAAU,EAAE;YACtB,IAAI,QAAQ,UAAU,GAAG,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,QAAQ,UAAU,GAAG,uHAAA,CAAA,oBAAiB,CAAC,iBAAiB,EAAE;gBAC5D,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,uHAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC;YACtF;QACF;IACF;IAEQ,qBAAqB,OAA+B,EAAQ;QAClE,MAAM,qBAAqB,wHAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC,QAAQ,cAAc,EAAE;QACpF,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC/B,MAAM,IAAI,MAAM,mBAAmB,KAAK;QAC1C;QAEA,MAAM,mBAAmB,wHAAA,CAAA,kBAAe,CAAC,cAAc,CACrD,QAAQ,cAAc,EACtB,GACA,uHAAA,CAAA,oBAAiB,CAAC,gBAAgB;QAEpC,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B,MAAM,IAAI,MAAM,iBAAiB,KAAK;QACxC;IACF;IAEQ,uBAAuB,OAAuB,EAAQ;QAC5D,MAAM,kBAAkB,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,QAAQ,KAAK;QACrE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM,gBAAgB,KAAK;QACvC;IACF;IAEA,2DAA2D;IAEnD,iBAAiB,OAAwB,EAAU;QACzD,IAAI,CAAC,SAAS,OAAO;QAErB,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,OAAO;YAC5B;QACF;QAEA,MAAM,cAAc,OAAO,QAAQ;QACnC,OAAO,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG;IAC3C;AACF;;AAIO,MAAM,iBAAiB,IAAI,eAAe,uHAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/components/ConnectionTest.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { apiClient } from '../lib/apiClient';\nimport { authService, LoginResponse } from '../lib/authService';\nimport { contentService } from '../lib/contentService';\nimport { HealthCheckResponse, ContentHistoryResponse } from '../types/api';\n\ninterface TestResult {\n  name: string;\n  status: 'pending' | 'success' | 'error';\n  message?: string;\n}\n\nexport default function ConnectionTest() {\n  const [tests, setTests] = useState<TestResult[]>([\n    { name: 'Backend Health Check', status: 'pending' },\n    { name: 'API Configuration', status: 'pending' },\n    { name: 'Authentication Test', status: 'pending' },\n    { name: 'Content Service Test', status: 'pending' },\n  ]);\n\n  const [isRunning, setIsRunning] = useState(false);\n  const [authToken, setAuthToken] = useState<string | null>(null);\n\n  const updateTest = (index: number, status: TestResult['status'], message?: string) => {\n    setTests(prev => prev.map((test, i) =>\n      i === index ? { ...test, status, message } : test\n    ));\n  };\n\n  const runTests = async () => {\n    setIsRunning(true);\n\n    // Reset all tests\n    setTests(prev => prev.map(test => ({ ...test, status: 'pending' })));\n\n    try {\n      // Test 1: Backend Health Check\n      try {\n        // Use the API health endpoint\n        const health = await apiClient.get<HealthCheckResponse>('/health');\n        if (health && health.status === 'healthy') {\n          updateTest(0, 'success', `Backend is healthy (API: ${health.api || 'ready'})`);\n        } else {\n          updateTest(0, 'error', 'Backend health check failed');\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        updateTest(0, 'error', `Backend not accessible: ${errorMessage}`);\n      }\n\n      // Test 2: API Configuration\n      try {\n        const baseURL = apiClient.instance.defaults.baseURL;\n        if (baseURL?.includes('8000')) {\n          updateTest(1, 'success', `API URL: ${baseURL}`);\n        } else {\n          updateTest(1, 'error', `Incorrect API URL: ${baseURL}`);\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        updateTest(1, 'error', `API configuration error: ${errorMessage}`);\n      }\n\n      // Test 3: Authentication Test\n      try {\n        const testUser = {\n          username: `test_${Date.now()}`,\n          email: `test_${Date.now()}@example.com`,\n          password: 'testpass123',\n          full_name: 'Test User'\n        };\n\n        // Try to register (might fail if user exists)\n        try {\n          await authService.register(testUser);\n        } catch (error: unknown) {\n          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n          if (!errorMessage.includes('already registered')) {\n            throw error;\n          }\n        }\n\n        // Try to login\n        const loginResult: LoginResponse = await authService.login({\n          username: testUser.username,\n          password: testUser.password\n        });\n\n        if (loginResult.access_token) {\n          setAuthToken(loginResult.access_token);\n          updateTest(2, 'success', 'Authentication successful');\n        } else {\n          updateTest(2, 'error', 'Login failed');\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        updateTest(2, 'error', `Authentication failed: ${errorMessage}`);\n      }\n\n      // Test 4: Content Service Test\n      try {\n        if (authService.isAuthenticated()) {\n          const history: ContentHistoryResponse = await contentService.getContentHistory(0, 5);\n          updateTest(3, 'success', `Content history retrieved (${history.total || 0} items)`);\n        } else {\n          updateTest(3, 'error', 'Not authenticated for content test');\n        }\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        updateTest(3, 'error', `Content service failed: ${errorMessage}`);\n      }\n\n    } catch (error: unknown) {\n      console.error('Test suite error:', error);\n    } finally {\n      setIsRunning(false);\n    }\n  };\n\n  const testContentGeneration = async () => {\n    if (!authService.isAuthenticated()) {\n      alert('Please run authentication test first');\n      return;\n    }\n\n    try {\n      const result = await contentService.generateTweet({\n        topic: 'Testing AutoReach connection',\n        style: 'professional',\n        language: 'en'\n      });\n\n      if (result.success && result.content) {\n        alert(`Content generated successfully:\\n\\n${result.content}`);\n      } else {\n        alert(`Content generation failed: ${result.error || 'Unknown error'}`);\n      }\n    } catch (error: unknown) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n      alert(`Content generation error: ${errorMessage}`);\n    }\n  };\n\n  const getStatusIcon = (status: TestResult['status']) => {\n    switch (status) {\n      case 'pending': return '⏳';\n      case 'success': return '✅';\n      case 'error': return '❌';\n    }\n  };\n\n  const getStatusColor = (status: TestResult['status']) => {\n    switch (status) {\n      case 'pending': return 'text-yellow-600';\n      case 'success': return 'text-green-600';\n      case 'error': return 'text-red-600';\n    }\n  };\n\n  return (\n    <div className=\"max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg\">\n      <h2 className=\"text-2xl font-bold mb-6 text-gray-800\">\n        Frontend-Backend Connection Test\n      </h2>\n\n      <div className=\"space-y-4 mb-6\">\n        {tests.map((test, index) => (\n          <div key={index} className=\"flex items-center space-x-3 p-3 border rounded-lg\">\n            <span className=\"text-2xl\">{getStatusIcon(test.status)}</span>\n            <div className=\"flex-1\">\n              <div className={`font-medium ${getStatusColor(test.status)}`}>\n                {test.name}\n              </div>\n              {test.message && (\n                <div className=\"text-sm text-gray-600 mt-1\">\n                  {test.message}\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"flex space-x-4\">\n        <button\n          onClick={runTests}\n          disabled={isRunning}\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isRunning ? 'Running Tests...' : 'Run Connection Tests'}\n        </button>\n\n        <button\n          onClick={testContentGeneration}\n          disabled={isRunning || !authService.isAuthenticated()}\n          className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          Test Content Generation\n        </button>\n      </div>\n\n      <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n        <h3 className=\"font-medium text-gray-800 mb-2\">Connection Status</h3>\n        <div className=\"text-sm text-gray-600 space-y-1\">\n          <div>Backend URL: {process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'}</div>\n          <div>Authentication: {authService.isAuthenticated() ? '✅ Authenticated' : '❌ Not authenticated'}</div>\n          {authToken && (\n            <div className=\"break-all\">Token: {authToken.substring(0, 20)}...</div>\n          )}\n        </div>\n      </div>\n\n      <div className=\"mt-4 text-xs text-gray-500\">\n        <p>This component tests the connection between the Next.js frontend and FastAPI backend.</p>\n        <p>Make sure the backend is running on port 8000 before testing.</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC/C;YAAE,MAAM;YAAwB,QAAQ;QAAU;QAClD;YAAE,MAAM;YAAqB,QAAQ;QAAU;QAC/C;YAAE,MAAM;YAAuB,QAAQ;QAAU;QACjD;YAAE,MAAM;YAAwB,QAAQ;QAAU;KACnD;IAED,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,aAAa,CAAC,OAAe,QAA8B;QAC/D,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAC,MAAM,IAC/B,MAAM,QAAQ;oBAAE,GAAG,IAAI;oBAAE;oBAAQ;gBAAQ,IAAI;IAEjD;IAEA,MAAM,WAAW;QACf,aAAa;QAEb,kBAAkB;QAClB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,CAAC;QAEjE,IAAI;YACF,+BAA+B;YAC/B,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,SAAS,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAsB;gBACxD,IAAI,UAAU,OAAO,MAAM,KAAK,WAAW;oBACzC,WAAW,GAAG,WAAW,CAAC,yBAAyB,EAAE,OAAO,GAAG,IAAI,QAAQ,CAAC,CAAC;gBAC/E,OAAO;oBACL,WAAW,GAAG,SAAS;gBACzB;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAAE,cAAc;YAClE;YAEA,4BAA4B;YAC5B,IAAI;gBACF,MAAM,UAAU,uHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO;gBACnD,IAAI,SAAS,SAAS,SAAS;oBAC7B,WAAW,GAAG,WAAW,CAAC,SAAS,EAAE,SAAS;gBAChD,OAAO;oBACL,WAAW,GAAG,SAAS,CAAC,mBAAmB,EAAE,SAAS;gBACxD;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,WAAW,GAAG,SAAS,CAAC,yBAAyB,EAAE,cAAc;YACnE;YAEA,8BAA8B;YAC9B,IAAI;gBACF,MAAM,WAAW;oBACf,UAAU,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;oBAC9B,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,YAAY,CAAC;oBACvC,UAAU;oBACV,WAAW;gBACb;gBAEA,8CAA8C;gBAC9C,IAAI;oBACF,MAAM,yHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBAC7B,EAAE,OAAO,OAAgB;oBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAC9D,IAAI,CAAC,aAAa,QAAQ,CAAC,uBAAuB;wBAChD,MAAM;oBACR;gBACF;gBAEA,eAAe;gBACf,MAAM,cAA6B,MAAM,yHAAA,CAAA,cAAW,CAAC,KAAK,CAAC;oBACzD,UAAU,SAAS,QAAQ;oBAC3B,UAAU,SAAS,QAAQ;gBAC7B;gBAEA,IAAI,YAAY,YAAY,EAAE;oBAC5B,aAAa,YAAY,YAAY;oBACrC,WAAW,GAAG,WAAW;gBAC3B,OAAO;oBACL,WAAW,GAAG,SAAS;gBACzB;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,WAAW,GAAG,SAAS,CAAC,uBAAuB,EAAE,cAAc;YACjE;YAEA,+BAA+B;YAC/B,IAAI;gBACF,IAAI,yHAAA,CAAA,cAAW,CAAC,eAAe,IAAI;oBACjC,MAAM,UAAkC,MAAM,4HAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC,GAAG;oBAClF,WAAW,GAAG,WAAW,CAAC,2BAA2B,EAAE,QAAQ,KAAK,IAAI,EAAE,OAAO,CAAC;gBACpF,OAAO;oBACL,WAAW,GAAG,SAAS;gBACzB;YACF,EAAE,OAAO,OAAgB;gBACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,WAAW,GAAG,SAAS,CAAC,wBAAwB,EAAE,cAAc;YAClE;QAEF,EAAE,OAAO,OAAgB;YACvB,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,yHAAA,CAAA,cAAW,CAAC,eAAe,IAAI;YAClC,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,4HAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;gBAChD,OAAO;gBACP,OAAO;gBACP,UAAU;YACZ;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,EAAE;gBACpC,MAAM,CAAC,mCAAmC,EAAE,OAAO,OAAO,EAAE;YAC9D,OAAO;gBACL,MAAM,CAAC,2BAA2B,EAAE,OAAO,KAAK,IAAI,iBAAiB;YACvE;QACF,EAAE,OAAO,OAAgB;YACvB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,CAAC,0BAA0B,EAAE,cAAc;QACnD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;QACvB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;QACvB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAItD,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCAAK,WAAU;0CAAY,cAAc,KAAK,MAAM;;;;;;0CACrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,YAAY,EAAE,eAAe,KAAK,MAAM,GAAG;kDACzD,KAAK,IAAI;;;;;;oCAEX,KAAK,OAAO,kBACX,8OAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO;;;;;;;;;;;;;uBARX;;;;;;;;;;0BAgBd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,YAAY,qBAAqB;;;;;;kCAGpC,8OAAC;wBACC,SAAS;wBACT,UAAU,aAAa,CAAC,yHAAA,CAAA,cAAW,CAAC,eAAe;wBACnD,WAAU;kCACX;;;;;;;;;;;;0BAKH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAI;oCAAc,iEAAmC;;;;;;;0CACtD,8OAAC;;oCAAI;oCAAiB,yHAAA,CAAA,cAAW,CAAC,eAAe,KAAK,oBAAoB;;;;;;;4BACzE,2BACC,8OAAC;gCAAI,WAAU;;oCAAY;oCAAQ,UAAU,SAAS,CAAC,GAAG;oCAAI;;;;;;;;;;;;;;;;;;;0BAKpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAE;;;;;;kCACH,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;AAIX", "debugId": null}}]}