{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/AutoReach/frontend/app/auth/twitter/oauth2-callback/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState, useCallback, Suspense } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { authService } from '../../../lib/authService';\n\nfunction TwitterOAuth2CallbackContent() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { refreshUser } = useAuth();\n  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');\n  const [message, setMessage] = useState('');\n  const [user, setUser] = useState<{\n    id: number;\n    username: string;\n    twitter_username: string;\n    twitter_user_id: string;\n    full_name?: string;\n    is_active: boolean;\n  } | null>(null);\n\n  const handleTwitterOAuth2Callback = useCallback(async () => {\n    try {\n      // Log ALL URL parameters for debugging\n      const urlParams = new URLSearchParams(window.location.search);\n      console.log('=== TWITTER CALLBACK DEBUG ===');\n      console.log('Full URL:', window.location.href);\n      console.log('All URL params:', Object.fromEntries(urlParams.entries()));\n      \n      // Get OAuth parameters from URL\n      const code = searchParams.get('code');\n      const state = searchParams.get('state');\n      const error = searchParams.get('error');\n      const error_description = searchParams.get('error_description');\n\n      console.log('OAuth params:', { code, state, error, error_description });\n\n      if (error) {\n        console.error('Twitter OAuth error:', { error, error_description });\n        setStatus('error');\n        setMessage(`Twitter authorization failed: ${error} - ${error_description || 'No description'}`);\n        setTimeout(() => router.push('/'), 3000);\n        return;\n      }\n\n      if (!code || !state) {\n        setStatus('error');\n        setMessage('Missing OAuth parameters in callback URL');\n        setTimeout(() => router.push('/'), 3000);\n        return;\n      }\n\n      // Get stored OAuth state and code verifier\n      const storedState = sessionStorage.getItem('twitter_oauth2_state');\n      const codeVerifier = sessionStorage.getItem('twitter_oauth2_code_verifier');\n\n      if (!storedState || !codeVerifier) {\n        setStatus('error');\n        setMessage('OAuth state not found. Please restart the authentication process.');\n        setTimeout(() => router.push('/'), 3000);\n        return;\n      }\n\n      if (state !== storedState) {\n        setStatus('error');\n        setMessage('Invalid OAuth state. Possible security issue.');\n        setTimeout(() => router.push('/'), 3000);\n        return;\n      }\n\n      // Complete the OAuth flow\n      const result = await authService.handleTwitterOAuth2Callback({\n        code,\n        state,\n        code_verifier: codeVerifier\n      });\n\n      // Clean up stored tokens\n      sessionStorage.removeItem('twitter_oauth2_state');\n      sessionStorage.removeItem('twitter_oauth2_code_verifier');\n\n      setStatus('success');\n      setMessage('Successfully logged in with Twitter!');\n      setUser(result.user);\n\n      // Refresh the AuthContext with the new user data\n      await refreshUser();\n\n      // Redirect to dashboard after success\n      setTimeout(() => router.push('/dashboard'), 2000);\n\n    } catch (error) {\n      console.error('Callback handler error:', error);\n      setStatus('error');\n      setMessage(error instanceof Error ? error.message : 'Failed to authenticate with Twitter');\n      setTimeout(() => router.push('/'), 3000);\n    }\n  }, [searchParams, router, refreshUser]);\n\n  useEffect(() => {\n    handleTwitterOAuth2Callback();\n  }, [handleTwitterOAuth2Callback]);\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900\">\n            Twitter Authentication\n          </h2>\n        </div>\n\n        <div className=\"bg-white shadow-md rounded-lg p-6\">\n          {status === 'loading' && (\n            <div className=\"text-center\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600\">Processing Twitter authentication...</p>\n            </div>\n          )}\n\n          {status === 'success' && (\n            <div className=\"text-center\">\n              <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4\">\n                <svg className=\"h-6 w-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Success!</h3>\n              <p className=\"text-gray-600 mb-4\">{message}</p>\n              {user && (\n                <div className=\"bg-gray-50 rounded-md p-3 text-left\">\n                  <p className=\"text-sm text-gray-700\">\n                    <strong>Welcome, {user.full_name || user.username}!</strong>\n                  </p>\n                  <p className=\"text-sm text-gray-500\">\n                    @{user.twitter_username}\n                  </p>\n                </div>\n              )}\n              <p className=\"text-sm text-gray-500 mt-4\">Redirecting to dashboard...</p>\n            </div>\n          )}\n\n          {status === 'error' && (\n            <div className=\"text-center\">\n              <div className=\"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4\">\n                <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Authentication Failed</h3>\n              <p className=\"text-gray-600 mb-4\">{message}</p>\n              <p className=\"text-sm text-gray-500\">Redirecting to home page...</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction LoadingFallback() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900\">\n            Twitter Authentication\n          </h2>\n          <div className=\"bg-white shadow-md rounded-lg p-6 mt-8\">\n            <div className=\"text-center\">\n              <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600\">Loading...</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default function TwitterOAuth2CallbackPage() {\n  return (\n    <Suspense fallback={<LoadingFallback />}>\n      <TwitterOAuth2CallbackContent />\n    </Suspense>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,SAAS;;IACP,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC9B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAOrB;IAEV,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iFAAE;YAC9C,IAAI;gBACF,uCAAuC;gBACvC,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;gBAC5D,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,aAAa,OAAO,QAAQ,CAAC,IAAI;gBAC7C,QAAQ,GAAG,CAAC,mBAAmB,OAAO,WAAW,CAAC,UAAU,OAAO;gBAEnE,gCAAgC;gBAChC,MAAM,OAAO,aAAa,GAAG,CAAC;gBAC9B,MAAM,QAAQ,aAAa,GAAG,CAAC;gBAC/B,MAAM,QAAQ,aAAa,GAAG,CAAC;gBAC/B,MAAM,oBAAoB,aAAa,GAAG,CAAC;gBAE3C,QAAQ,GAAG,CAAC,iBAAiB;oBAAE;oBAAM;oBAAO;oBAAO;gBAAkB;gBAErE,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,wBAAwB;wBAAE;wBAAO;oBAAkB;oBACjE,UAAU;oBACV,WAAW,CAAC,8BAA8B,EAAE,MAAM,GAAG,EAAE,qBAAqB,kBAAkB;oBAC9F;iGAAW,IAAM,OAAO,IAAI,CAAC;gGAAM;oBACnC;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,OAAO;oBACnB,UAAU;oBACV,WAAW;oBACX;iGAAW,IAAM,OAAO,IAAI,CAAC;gGAAM;oBACnC;gBACF;gBAEA,2CAA2C;gBAC3C,MAAM,cAAc,eAAe,OAAO,CAAC;gBAC3C,MAAM,eAAe,eAAe,OAAO,CAAC;gBAE5C,IAAI,CAAC,eAAe,CAAC,cAAc;oBACjC,UAAU;oBACV,WAAW;oBACX;iGAAW,IAAM,OAAO,IAAI,CAAC;gGAAM;oBACnC;gBACF;gBAEA,IAAI,UAAU,aAAa;oBACzB,UAAU;oBACV,WAAW;oBACX;iGAAW,IAAM,OAAO,IAAI,CAAC;gGAAM;oBACnC;gBACF;gBAEA,0BAA0B;gBAC1B,MAAM,SAAS,MAAM,4HAAA,CAAA,cAAW,CAAC,2BAA2B,CAAC;oBAC3D;oBACA;oBACA,eAAe;gBACjB;gBAEA,yBAAyB;gBACzB,eAAe,UAAU,CAAC;gBAC1B,eAAe,UAAU,CAAC;gBAE1B,UAAU;gBACV,WAAW;gBACX,QAAQ,OAAO,IAAI;gBAEnB,iDAAiD;gBACjD,MAAM;gBAEN,sCAAsC;gBACtC;6FAAW,IAAM,OAAO,IAAI,CAAC;4FAAe;YAE9C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,UAAU;gBACV,WAAW,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACpD;6FAAW,IAAM,OAAO,IAAI,CAAC;4FAAM;YACrC;QACF;gFAAG;QAAC;QAAc;QAAQ;KAAY;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kDAAE;YACR;QACF;iDAAG;QAAC;KAA4B;IAEhC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAA6C;;;;;;;;;;;8BAK7D,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,2BACV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;wBAIhC,WAAW,2BACV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAChF,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;gCAClC,sBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACX,cAAA,6LAAC;;oDAAO;oDAAU,KAAK,SAAS,IAAI,KAAK,QAAQ;oDAAC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;;gDAAwB;gDACjC,KAAK,gBAAgB;;;;;;;;;;;;;8CAI7B,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;wBAI7C,WAAW,yBACV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAuB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;GAzJS;;QACQ,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QACZ,kIAAA,CAAA,UAAO;;;KAHxB;AA2JT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAG3D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;MAlBS;AAoBM,SAAS;IACtB,qBACE,6LAAC,6JAAA,CAAA,WAAQ;QAAC,wBAAU,6LAAC;;;;;kBACnB,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}