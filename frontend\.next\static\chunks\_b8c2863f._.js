(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/lib/validation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Frontend validation utilities following DRY and KISS principles.
 * Centralizes validation logic to eliminate code duplication.
 */ __turbopack_context__.s({
    "ContentValidation": (()=>ContentValidation),
    "FormValidation": (()=>FormValidation),
    "ValidationHooks": (()=>ValidationHooks),
    "ValidationSchemas": (()=>ValidationSchemas),
    "ValidationUtils": (()=>ValidationUtils),
    "validateContentGenerationForm": (()=>validateContentGenerationForm),
    "validateUserLoginForm": (()=>validateUserLoginForm),
    "validateUserRegistrationForm": (()=>validateUserRegistrationForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
;
class ValidationUtils {
    /**
   * Validate required field
   */ static validateRequired(value, fieldName = 'This field') {
        if (value === null || value === undefined) {
            // If fieldName contains "is required" or similar, use it as is
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        if (typeof value === 'string' && !value.trim()) {
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        if (Array.isArray(value) && value.length === 0) {
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate string length
   */ static validateLength(value, minLength, maxLength) {
        if (typeof value !== 'string') {
            return {
                isValid: false,
                error: 'Value must be a string'
            };
        }
        const length = value.length;
        if (minLength !== undefined && length < minLength) {
            return {
                isValid: false,
                error: `Must be at least ${minLength} characters long`
            };
        }
        if (maxLength !== undefined && length > maxLength) {
            return {
                isValid: false,
                error: `Must be no more than ${maxLength} characters long`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate email format
   */ static validateEmail(email) {
        if (!email) {
            return {
                isValid: false,
                error: 'Email is required'
            };
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].EMAIL.test(email)) {
            return {
                isValid: false,
                error: 'Please enter a valid email address'
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate pattern match
   */ static validatePattern(value, pattern, errorMessage = 'Invalid format') {
        if (!pattern.test(value)) {
            return {
                isValid: false,
                error: errorMessage
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate numeric range
   */ static validateRange(value, min, max) {
        if (value < min || value > max) {
            return {
                isValid: false,
                error: `Must be between ${min} and ${max}`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate choice from options
   */ static validateChoice(value, choices, errorMessage = 'Please select a valid option') {
        if (!choices.includes(value)) {
            return {
                isValid: false,
                error: errorMessage
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate username
   */ static validateUsername(username) {
        const requiredResult = this.validateRequired(username, 'Username');
        if (!requiredResult.isValid) return requiredResult;
        const lengthResult = this.validateLength(username, 3, 20);
        if (!lengthResult.isValid) return lengthResult;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].USERNAME.test(username)) {
            return {
                isValid: false,
                error: 'Username can only contain letters, numbers, and underscores'
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate password
   */ static validatePassword(password) {
        const requiredResult = this.validateRequired(password, 'Password');
        if (!requiredResult.isValid) return requiredResult;
        if (password.length < __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_PASSWORD_LENGTH) {
            return {
                isValid: false,
                error: `Password must be at least ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_PASSWORD_LENGTH} characters long`
            };
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].PASSWORD.test(password)) {
            return {
                isValid: false,
                error: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
            };
        }
        return {
            isValid: true
        };
    }
}
class ContentValidation {
    /**
   * Validate topic for content generation
   */ static validateTopic(topic) {
        const requiredResult = ValidationUtils.validateRequired(topic, 'Topic');
        if (!requiredResult.isValid) return requiredResult;
        const length = topic.length;
        if (length < __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_TOPIC_LENGTH) {
            return {
                isValid: false,
                error: `Topic must be at least ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_TOPIC_LENGTH} characters long`
            };
        }
        if (length > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH) {
            return {
                isValid: false,
                error: `Topic must be no more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH} characters long`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate content for tweets
   */ static validateContent(content) {
        const requiredResult = ValidationUtils.validateRequired(content, 'Content');
        if (!requiredResult.isValid) return requiredResult;
        const length = content.length;
        if (length > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH) {
            return {
                isValid: false,
                error: `Content must be no more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH} characters long`
            };
        }
        const warnings = [];
        // Check for too many hashtags
        const hashtagCount = ContentValidation.countHashtags(content);
        if (hashtagCount > 3) {
            warnings.push('Consider using fewer hashtags for better engagement');
        }
        // Check for too many mentions
        const mentionCount = ContentValidation.countMentions(content);
        if (mentionCount > 5) {
            warnings.push('Too many mentions may reduce visibility');
        }
        return {
            isValid: true,
            warnings: warnings.length > 0 ? warnings : undefined
        };
    }
    /**
   * Validate content style
   */ static validateStyle(style) {
        return ValidationUtils.validateChoice(style, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_CONSTANTS"].SUPPORTED_STYLES, 'Please select a valid style');
    }
    /**
   * Validate language
   */ static validateLanguage(language) {
        const supportedLanguages = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_CONSTANTS"].SUPPORTED_LANGUAGES.map((lang)=>lang.code);
        return ValidationUtils.validateChoice(language, supportedLanguages, 'Please select a valid language');
    }
    /**
   * Validate thread size
   */ static validateThreadSize(size) {
        if (size < 2) {
            return {
                isValid: false,
                error: 'Thread must have at least 2 tweets'
            };
        }
        if (size > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS) {
            return {
                isValid: false,
                error: `Thread cannot have more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS} tweets`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Count hashtags in content
   */ static countHashtags(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].HASHTAG);
        return matches ? matches.length : 0;
    }
    /**
   * Count mentions in content
   */ static countMentions(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].MENTION);
        return matches ? matches.length : 0;
    }
    /**
   * Count URLs in content
   */ static countUrls(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].URL);
        return matches ? matches.length : 0;
    }
    /**
   * Get content statistics
   */ static getContentStats(content) {
        const characterCount = content.length;
        const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
        return {
            characterCount,
            wordCount,
            hashtagCount: ContentValidation.countHashtags(content),
            mentionCount: ContentValidation.countMentions(content),
            urlCount: ContentValidation.countUrls(content),
            // Add properties expected by tests
            length: characterCount,
            remaining: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH - characterCount
        };
    }
}
class FormValidation {
    /**
   * Validate multiple fields and return combined result
   */ static validateFields(validations) {
        const errors = [];
        for (const validation of validations){
            const result = validation();
            if (!result.isValid && result.error) {
                errors.push(result.error);
            }
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    /**
   * Validate form data against schema
   */ static validateFormData(data, schema) {
        const errors = {};
        for (const [field, validator] of Object.entries(schema)){
            const result = validator(data[field]);
            if (!result.isValid && result.error) {
                errors[field] = result.error;
            }
        }
        return errors;
    }
    /**
   * Validate single field
   */ static validateField(value, validator) {
        return validator(value);
    }
}
class ValidationHooks {
    /**
   * Debounced validation for real-time feedback
   */ static createDebouncedValidator(validator, delay = 300) {
        let timeoutId;
        return (value, callback)=>{
            clearTimeout(timeoutId);
            timeoutId = setTimeout(()=>{
                const result = validator(value);
                callback(result);
            }, delay);
        };
    }
}
const ValidationSchemas = {
    contentGeneration: {
        topic: (value)=>ContentValidation.validateTopic(value),
        style: (value)=>ContentValidation.validateStyle(value),
        language: (value)=>ContentValidation.validateLanguage(value)
    },
    userRegistration: {
        username: (value)=>ValidationUtils.validateUsername(value),
        email: (value)=>ValidationUtils.validateEmail(value),
        password: (value)=>ValidationUtils.validatePassword(value)
    },
    userLogin: {
        email: (value)=>ValidationUtils.validateEmail(value),
        password: (value)=>ValidationUtils.validateRequired(value, 'Password')
    }
};
const validateContentGenerationForm = (data)=>{
    const errors = {};
    const topicValidation = ValidationSchemas.contentGeneration.topic(data.topic);
    if (!topicValidation.isValid && topicValidation.error) {
        errors.topic = topicValidation.error;
    }
    return errors;
};
const validateUserRegistrationForm = (data)=>{
    return FormValidation.validateFormData(data, ValidationSchemas.userRegistration);
};
const validateUserLoginForm = (data)=>{
    return FormValidation.validateFormData(data, ValidationSchemas.userLogin);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/errorHandling.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ /**
 * Frontend error handling utilities following DRY and KISS principles.
 * Centralizes error handling logic to eliminate code duplication.
 */ __turbopack_context__.s({
    "AuthErrorHandler": (()=>AuthErrorHandler),
    "ContentErrorHandler": (()=>ContentErrorHandler),
    "ErrorHandler": (()=>ErrorHandler),
    "ErrorLogger": (()=>ErrorLogger),
    "ErrorTypes": (()=>ErrorTypes),
    "NetworkErrorHandler": (()=>NetworkErrorHandler),
    "RetryHandler": (()=>RetryHandler),
    "createAsyncErrorHandler": (()=>createAsyncErrorHandler),
    "handleAuthError": (()=>handleAuthError),
    "handleContentError": (()=>handleContentError),
    "handleNetworkError": (()=>handleNetworkError),
    "withErrorHandling": (()=>withErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
;
const ErrorTypes = {
    NETWORK: 'NETWORK',
    VALIDATION: 'VALIDATION',
    AUTHENTICATION: 'AUTHENTICATION',
    AUTHORIZATION: 'AUTHORIZATION',
    RATE_LIMIT: 'RATE_LIMIT',
    SERVER: 'SERVER',
    CLIENT: 'CLIENT',
    UNKNOWN: 'UNKNOWN'
};
class ErrorHandler {
    /**
   * Main error handler that processes errors and returns structured result
   */ static handleError(error, context) {
        const message = this.getErrorMessage(error);
        const type = this.getErrorType(error);
        const userMessage = this.getUserFriendlyMessage(type);
        const result = {
            type,
            message,
            userMessage,
            context,
            timestamp: Date.now()
        };
        this.logError(error, context);
        return result;
    }
    /**
   * Get error message from various error types
   */ static getErrorMessage(error) {
        if (error instanceof Error) {
            return error.message;
        }
        if (typeof error === 'string') {
            return error;
        }
        if (error && typeof error === 'object' && 'message' in error) {
            return String(error.message);
        }
        return 'An unknown error occurred';
    }
    /**
   * Determine error type based on error content
   */ static getErrorType(error) {
        const message = this.getErrorMessage(error).toLowerCase();
        if (message.includes('network') || message.includes('fetch')) {
            return ErrorTypes.NETWORK;
        }
        if (message.includes('validation') || message.includes('invalid')) {
            return ErrorTypes.VALIDATION;
        }
        if (message.includes('unauthorized') || message.includes('auth')) {
            return ErrorTypes.AUTHENTICATION;
        }
        if (message.includes('forbidden')) {
            return ErrorTypes.AUTHORIZATION;
        }
        if (message.includes('too many requests') || message.includes('rate limit')) {
            return ErrorTypes.RATE_LIMIT;
        }
        if (message.includes('server error') || message.includes('internal')) {
            return ErrorTypes.SERVER;
        }
        if (message.includes('bad request') || message.includes('client')) {
            return ErrorTypes.CLIENT;
        }
        return ErrorTypes.UNKNOWN;
    }
    /**
   * Get user-friendly message for error type
   */ static getUserFriendlyMessage(errorType) {
        switch(errorType){
            case ErrorTypes.NETWORK:
                return 'Network error. Please check your connection.';
            case ErrorTypes.VALIDATION:
                return 'Please check your input and try again.';
            case ErrorTypes.AUTHENTICATION:
                return 'Please log in to continue.';
            case ErrorTypes.AUTHORIZATION:
                return 'You do not have permission to perform this action.';
            case ErrorTypes.RATE_LIMIT:
                return 'Too many requests. Please wait a moment and try again.';
            case ErrorTypes.SERVER:
                return 'Server error. Please try again later.';
            case ErrorTypes.CLIENT:
                return 'Invalid request. Please check your input.';
            default:
                return 'An unexpected error occurred. Please try again.';
        }
    }
    /**
   * Check if error is retryable
   */ static isRetryableError(error) {
        const type = this.getErrorType(error);
        const message = this.getErrorMessage(error).toLowerCase();
        // Network errors are retryable
        if (type === ErrorTypes.NETWORK) {
            return true;
        }
        // Timeout errors are retryable
        if (message.includes('timeout')) {
            return true;
        }
        // Server errors (5xx) are retryable
        if (type === ErrorTypes.SERVER || message.includes('server error')) {
            return true;
        }
        // Rate limit errors are retryable (after delay)
        if (type === ErrorTypes.RATE_LIMIT) {
            return true;
        }
        // Authentication, validation, and client errors are not retryable
        return false;
    }
    /**
   * Log error with context
   */ static logError(error, context) {
        const message = this.getErrorMessage(error);
        const logData = {
            message,
            context,
            timestamp: new Date().toISOString(),
            stack: error instanceof Error ? error.stack : undefined
        };
        console.error('Error occurred:', logData);
    }
    /**
   * Handle API errors with consistent formatting
   */ static handleApiError(error, options = {}) {
        const { showToast = false, logError = true, fallbackMessage = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERIC_ERROR } = options;
        let apiError;
        // Type guard for error objects
        const isErrorWithResponse = (err)=>{
            return typeof err === 'object' && err !== null && 'response' in err;
        };
        const isErrorWithRequest = (err)=>{
            return typeof err === 'object' && err !== null && 'request' in err;
        };
        const isErrorWithMessage = (err)=>{
            return typeof err === 'object' && err !== null && 'message' in err;
        };
        if (isErrorWithResponse(error)) {
            // HTTP error response
            const status = error.response.status;
            const data = error.response.data;
            apiError = {
                message: data?.message || (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getErrorMessageByStatus"])(status),
                status,
                code: data?.code,
                details: data?.details
            };
        } else if (isErrorWithRequest(error)) {
            // Network error
            apiError = {
                message: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].NETWORK_ERROR,
                status: 0,
                code: 'NETWORK_ERROR'
            };
        } else {
            // Other error
            apiError = {
                message: isErrorWithMessage(error) ? error.message : fallbackMessage,
                code: 'UNKNOWN_ERROR'
            };
        }
        if (logError) {
            console.error('API Error:', apiError, error);
        }
        if (showToast) {
        // This would integrate with your toast system
        // toast.error(apiError.message);
        }
        return apiError;
    }
    /**
   * Handle validation errors
   */ static handleValidationError(errors, options = {}) {
        const { logError = true } = options;
        let message;
        if (Array.isArray(errors)) {
            message = errors.join(', ');
        } else if (typeof errors === 'object' && errors !== null) {
            message = Object.values(errors).join(', ');
        } else {
            message = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].VALIDATION_ERROR;
        }
        const apiError = {
            message,
            status: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HTTP_STATUS"].UNPROCESSABLE_ENTITY,
            code: 'VALIDATION_ERROR',
            details: typeof errors === 'object' && errors !== null ? errors : undefined
        };
        if (logError) {
            console.warn('Validation Error:', apiError);
        }
        return apiError;
    }
    /**
   * Handle async operation errors
   */ static async handleAsyncError(operation, options = {}) {
        try {
            const data = await operation();
            return {
                data
            };
        } catch (error) {
            const apiError = this.handleApiError(error, options);
            return {
                error: apiError
            };
        }
    }
    /**
   * Create error boundary handler
   */ static createErrorBoundaryHandler(fallbackComponent) {
        return (error, errorInfo)=>{
            console.error('Error Boundary caught an error:', error, errorInfo);
            // Log to error reporting service
            // errorReportingService.captureException(error, errorInfo);
            return fallbackComponent;
        };
    }
}
class ContentErrorHandler {
    /**
   * Handle content generation errors
   */ static handleGenerationError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERATION_FAILED,
            logError: true
        });
    }
    /**
   * Handle content validation errors
   */ static handleContentValidationError(errors) {
        return ErrorHandler.handleValidationError(errors, {
            logError: true
        });
    }
}
class AuthErrorHandler {
    /**
   * Handle authentication errors
   */ static handleAuthError(error) {
        const apiError = ErrorHandler.handleApiError(error, {
            logError: true
        });
        // Handle specific auth scenarios
        if (apiError.status === __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HTTP_STATUS"].UNAUTHORIZED) {
            // Clear auth tokens
            localStorage.removeItem('authToken');
        // Redirect to login if needed
        // router.push('/login');
        }
        return apiError;
    }
    /**
   * Handle token expiration
   */ static handleTokenExpiration() {
        localStorage.removeItem('authToken');
    // Show token expired message
    // toast.error(ERROR_MESSAGES.UNAUTHORIZED);
    // Redirect to login
    // router.push('/login');
    }
}
class NetworkErrorHandler {
    /**
   * Handle network connectivity issues
   */ static handleNetworkError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].NETWORK_ERROR,
            logError: true
        });
    }
    /**
   * Handle rate limiting
   */ static handleRateLimitError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].RATE_LIMIT_EXCEEDED,
            logError: true
        });
    }
}
class RetryHandler {
    /**
   * Retry failed operations with exponential backoff
   */ static async retryOperation(operation, maxRetries = 3, baseDelay = 1000) {
        let lastError;
        for(let attempt = 0; attempt <= maxRetries; attempt++){
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw error;
                }
                // Exponential backoff
                const delay = baseDelay * Math.pow(2, attempt);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            }
        }
        throw lastError;
    }
    /**
   * Check if error is retryable
   */ static isRetryableError(error) {
        if (!error.response) {
            return true; // Network errors are retryable
        }
        const status = error.response.status;
        return status >= 500 || status === __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HTTP_STATUS"].TOO_MANY_REQUESTS;
    }
}
class ErrorLogger {
    /**
   * Log error with timestamp and context
   */ static log(error, context) {
        const timestamp = new Date().toISOString();
        const message = error instanceof Error ? error.message : String(error);
        if (context) {
            console.error(`[ERROR] ${timestamp}`, message, 'Context:', context);
        } else {
            console.error(`[ERROR] ${timestamp}`, message);
        }
    }
    /**
   * Log warning with timestamp and context
   */ static warn(message, context) {
        const timestamp = new Date().toISOString();
        if (context) {
            console.warn(`[WARN] ${timestamp}`, message, 'Context:', context);
        } else {
            console.warn(`[WARN] ${timestamp}`, message);
        }
    }
    /**
   * Log info message
   */ static info(message, context) {
        const timestamp = new Date().toISOString();
        if (context) {
            console.log(`[INFO] ${timestamp}`, message, 'Context:', context);
        } else {
            console.log(`[INFO] ${timestamp}`, message);
        }
    }
    /**
   * Log debug message (only in development)
   */ static debug(message, context) {
        if ("TURBOPACK compile-time truthy", 1) {
            const timestamp = new Date().toISOString();
            if (context) {
                console.log(`[DEBUG] ${timestamp}`, message, 'Context:', context);
            } else {
                console.log(`[DEBUG] ${timestamp}`, message);
            }
        }
    }
    /**
   * Log error to console with context (legacy method)
   */ static logError(error, context, additionalData) {
        console.group(`🚨 Error in ${context}`);
        console.error('Error:', error);
        if (additionalData) {
            console.error('Additional Data:', additionalData);
        }
        console.error('Stack:', error.stack);
        console.groupEnd();
    }
    /**
   * Log warning with context (legacy method)
   */ static logWarning(message, context, additionalData) {
        console.group(`⚠️ Warning in ${context}`);
        console.warn('Message:', message);
        if (additionalData) {
            console.warn('Additional Data:', additionalData);
        }
        console.groupEnd();
    }
}
const withErrorHandling = (fn, errorHandler)=>{
    return async (...args)=>{
        try {
            return await fn(...args);
        } catch (error) {
            if (errorHandler) {
                errorHandler(error);
            } else {
                ErrorHandler.handleApiError(error, {
                    logError: true
                });
            }
            return undefined;
        }
    };
};
const createAsyncErrorHandler = (defaultErrorMessage = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERIC_ERROR)=>{
    return (error)=>{
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: defaultErrorMessage,
            logError: true
        });
    };
};
const handleContentError = ContentErrorHandler.handleGenerationError;
const handleAuthError = AuthErrorHandler.handleAuthError;
const handleNetworkError = NetworkErrorHandler.handleNetworkError;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/contentService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ContentService": (()=>ContentService),
    "contentService": (()=>contentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/validation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/errorHandling.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
// Export singleton instance
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/apiClient.ts [app-client] (ecmascript)");
;
;
;
class ContentService {
    apiClient;
    constructor(apiClient){
        this.apiClient = apiClient;
    }
    // New backend API methods with error handling
    async generateTweet(request) {
        try {
            this.validateTweetRequest(request);
            return await this.apiClient.post('/content/generate-tweet', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async generateThread(request) {
        try {
            this.validateThreadRequest(request);
            return await this.apiClient.post('/content/generate-thread', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async generateReply(request) {
        try {
            this.validateReplyRequest(request);
            return await this.apiClient.post('/content/generate-reply', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async getContentHistory(skip = 0, limit = 50) {
        const params = new URLSearchParams({
            skip: skip.toString(),
            limit: limit.toString()
        });
        return this.apiClient.get(`/content/history?${params}`);
    }
    // Legacy methods for backward compatibility
    async generateContent(request) {
        this.validateContentRequest(request);
        // Convert legacy request to new format
        const newRequest = {
            topic: request.topic,
            style: request.tone,
            user_context: `Length: ${request.length}, Include hashtags: ${request.includeHashtags}, Include emojis: ${request.includeEmojis}`
        };
        const response = await this.generateTweet(newRequest);
        // Convert response to legacy format
        return {
            id: Date.now().toString(),
            content: response.content || '',
            hashtags: [],
            createdAt: new Date().toISOString(),
            status: 'draft'
        };
    }
    // TODO: Implement these methods when backend endpoints are ready
    // For now, these are removed to eliminate dead code
    //
    // Future implementations:
    // - getContent(): Use history endpoint with filtering
    // - getContentById(): Implement backend endpoint for single content retrieval
    // - updateContent(): Implement backend endpoint for content updates
    // - scheduleContent(): Use scheduled posts API
    // - deleteContent(): Implement backend endpoint for content deletion
    // - publishContent(): Use Twitter posting API
    // Private validation methods using centralized validation utilities
    validateTweetRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
    }
    validateThreadRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
        if (request.num_tweets) {
            if (request.num_tweets < 2) {
                throw new Error('Thread must contain at least 2 tweets');
            }
            if (request.num_tweets > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS) {
                throw new Error(`Thread cannot exceed ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS} tweets`);
            }
        }
    }
    validateReplyRequest(request) {
        const requiredValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationUtils"].validateRequired(request.original_tweet, 'Original tweet');
        if (!requiredValidation.isValid) {
            throw new Error(requiredValidation.error);
        }
        const lengthValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationUtils"].validateLength(request.original_tweet, 1, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH);
        if (!lengthValidation.isValid) {
            throw new Error(lengthValidation.error);
        }
    }
    validateContentRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
    }
    // Removed unused validation methods to eliminate dead code
    buildQueryParams(filters) {
        if (!filters) return '';
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value])=>{
            if (value !== undefined && value !== null) {
                params.append(key, String(value));
            }
        });
        const queryString = params.toString();
        return queryString ? `?${queryString}` : '';
    }
}
;
const contentService = new ContentService(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"]);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/ConnectionTest.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ConnectionTest)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/authService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$contentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/contentService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function ConnectionTest() {
    _s();
    const [tests, setTests] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        {
            name: 'Backend Health Check',
            status: 'pending'
        },
        {
            name: 'API Configuration',
            status: 'pending'
        },
        {
            name: 'Authentication Test',
            status: 'pending'
        },
        {
            name: 'Content Service Test',
            status: 'pending'
        }
    ]);
    const [isRunning, setIsRunning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [authToken, setAuthToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const updateTest = (index, status, message)=>{
        setTests((prev)=>prev.map((test, i)=>i === index ? {
                    ...test,
                    status,
                    message
                } : test));
    };
    const runTests = async ()=>{
        setIsRunning(true);
        // Reset all tests
        setTests((prev)=>prev.map((test)=>({
                    ...test,
                    status: 'pending'
                })));
        try {
            // Test 1: Backend Health Check
            try {
                // Use the API health endpoint
                const health = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/health');
                if (health && health.status === 'healthy') {
                    updateTest(0, 'success', `Backend is healthy (API: ${health.api || 'ready'})`);
                } else {
                    updateTest(0, 'error', 'Backend health check failed');
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                updateTest(0, 'error', `Backend not accessible: ${errorMessage}`);
            }
            // Test 2: API Configuration
            try {
                const baseURL = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].instance.defaults.baseURL;
                if (baseURL?.includes('8000')) {
                    updateTest(1, 'success', `API URL: ${baseURL}`);
                } else {
                    updateTest(1, 'error', `Incorrect API URL: ${baseURL}`);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                updateTest(1, 'error', `API configuration error: ${errorMessage}`);
            }
            // Test 3: Authentication Test
            try {
                const testUser = {
                    username: `test_${Date.now()}`,
                    email: `test_${Date.now()}@example.com`,
                    password: 'testpass123',
                    full_name: 'Test User'
                };
                // Try to register (might fail if user exists)
                try {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].register(testUser);
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                    if (!errorMessage.includes('already registered')) {
                        throw error;
                    }
                }
                // Try to login
                const loginResult = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].login({
                    username: testUser.username,
                    password: testUser.password
                });
                if (loginResult.access_token) {
                    setAuthToken(loginResult.access_token);
                    updateTest(2, 'success', 'Authentication successful');
                } else {
                    updateTest(2, 'error', 'Login failed');
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                updateTest(2, 'error', `Authentication failed: ${errorMessage}`);
            }
            // Test 4: Content Service Test
            try {
                if (__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].isAuthenticated()) {
                    const history = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$contentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["contentService"].getContentHistory(0, 5);
                    updateTest(3, 'success', `Content history retrieved (${history.total || 0} items)`);
                } else {
                    updateTest(3, 'error', 'Not authenticated for content test');
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                updateTest(3, 'error', `Content service failed: ${errorMessage}`);
            }
        } catch (error) {
            console.error('Test suite error:', error);
        } finally{
            setIsRunning(false);
        }
    };
    const testContentGeneration = async ()=>{
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].isAuthenticated()) {
            alert('Please run authentication test first');
            return;
        }
        try {
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$contentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["contentService"].generateTweet({
                topic: 'Testing AutoReach connection',
                style: 'professional',
                language: 'en'
            });
            if (result.success && result.content) {
                alert(`Content generated successfully:\n\n${result.content}`);
            } else {
                alert(`Content generation failed: ${result.error || 'Unknown error'}`);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
            alert(`Content generation error: ${errorMessage}`);
        }
    };
    const getStatusIcon = (status)=>{
        switch(status){
            case 'pending':
                return '⏳';
            case 'success':
                return '✅';
            case 'error':
                return '❌';
        }
    };
    const getStatusColor = (status)=>{
        switch(status){
            case 'pending':
                return 'text-yellow-600';
            case 'success':
                return 'text-green-600';
            case 'error':
                return 'text-red-600';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                className: "text-2xl font-bold mb-6 text-gray-800",
                children: "Frontend-Backend Connection Test"
            }, void 0, false, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 164,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4 mb-6",
                children: tests.map((test, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-3 p-3 border rounded-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-2xl",
                                children: getStatusIcon(test.status)
                            }, void 0, false, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 171,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `font-medium ${getStatusColor(test.status)}`,
                                        children: test.name
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/ConnectionTest.tsx",
                                        lineNumber: 173,
                                        columnNumber: 15
                                    }, this),
                                    test.message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-gray-600 mt-1",
                                        children: test.message
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/ConnectionTest.tsx",
                                        lineNumber: 177,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 172,
                                columnNumber: 13
                            }, this)
                        ]
                    }, index, true, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 170,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 168,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex space-x-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: runTests,
                        disabled: isRunning,
                        className: "px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",
                        children: isRunning ? 'Running Tests...' : 'Run Connection Tests'
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 187,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: testContentGeneration,
                        disabled: isRunning || !__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].isAuthenticated(),
                        className: "px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",
                        children: "Test Content Generation"
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 195,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 186,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-6 p-4 bg-gray-50 rounded-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "font-medium text-gray-800 mb-2",
                        children: "Connection Status"
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 205,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-sm text-gray-600 space-y-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    "Backend URL: ",
                                    ("TURBOPACK compile-time value", "http://localhost:8000/api") || 'http://localhost:8000/api'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 207,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    "Authentication: ",
                                    __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].isAuthenticated() ? '✅ Authenticated' : '❌ Not authenticated'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 208,
                                columnNumber: 11
                            }, this),
                            authToken && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "break-all",
                                children: [
                                    "Token: ",
                                    authToken.substring(0, 20),
                                    "..."
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/components/ConnectionTest.tsx",
                                lineNumber: 210,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 206,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 204,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4 text-xs text-gray-500",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "This component tests the connection between the Next.js frontend and FastAPI backend."
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 216,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Make sure the backend is running on port 8000 before testing."
                    }, void 0, false, {
                        fileName: "[project]/app/components/ConnectionTest.tsx",
                        lineNumber: 217,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/ConnectionTest.tsx",
                lineNumber: 215,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/ConnectionTest.tsx",
        lineNumber: 163,
        columnNumber: 5
    }, this);
}
_s(ConnectionTest, "QAwEEpOZX5NjvK+4c0mLmSsOH5A=");
_c = ConnectionTest;
var _c;
__turbopack_context__.k.register(_c, "ConnectionTest");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/shared/lib/router/utils/querystring.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    assign: null,
    searchParamsToUrlQuery: null,
    urlQueryToSearchParams: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    assign: function() {
        return assign;
    },
    searchParamsToUrlQuery: function() {
        return searchParamsToUrlQuery;
    },
    urlQueryToSearchParams: function() {
        return urlQueryToSearchParams;
    }
});
function searchParamsToUrlQuery(searchParams) {
    const query = {};
    for (const [key, value] of searchParams.entries()){
        const existing = query[key];
        if (typeof existing === 'undefined') {
            query[key] = value;
        } else if (Array.isArray(existing)) {
            existing.push(value);
        } else {
            query[key] = [
                existing,
                value
            ];
        }
    }
    return query;
}
function stringifyUrlQueryParam(param) {
    if (typeof param === 'string') {
        return param;
    }
    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {
        return String(param);
    } else {
        return '';
    }
}
function urlQueryToSearchParams(query) {
    const searchParams = new URLSearchParams();
    for (const [key, value] of Object.entries(query)){
        if (Array.isArray(value)) {
            for (const item of value){
                searchParams.append(key, stringifyUrlQueryParam(item));
            }
        } else {
            searchParams.set(key, stringifyUrlQueryParam(value));
        }
    }
    return searchParams;
}
function assign(target) {
    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
        searchParamsList[_key - 1] = arguments[_key];
    }
    for (const searchParams of searchParamsList){
        for (const key of searchParams.keys()){
            target.delete(key);
        }
        for (const [key, value] of searchParams.entries()){
            target.append(key, value);
        }
    }
    return target;
} //# sourceMappingURL=querystring.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/router/utils/format-url.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
// Format function modified from nodejs
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    formatUrl: null,
    formatWithValidation: null,
    urlObjectKeys: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    formatUrl: function() {
        return formatUrl;
    },
    formatWithValidation: function() {
        return formatWithValidation;
    },
    urlObjectKeys: function() {
        return urlObjectKeys;
    }
});
const _interop_require_wildcard = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs [app-client] (ecmascript)");
const _querystring = /*#__PURE__*/ _interop_require_wildcard._(__turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/router/utils/querystring.js [app-client] (ecmascript)"));
const slashedProtocols = /https?|ftp|gopher|file/;
function formatUrl(urlObj) {
    let { auth, hostname } = urlObj;
    let protocol = urlObj.protocol || '';
    let pathname = urlObj.pathname || '';
    let hash = urlObj.hash || '';
    let query = urlObj.query || '';
    let host = false;
    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';
    if (urlObj.host) {
        host = auth + urlObj.host;
    } else if (hostname) {
        host = auth + (~hostname.indexOf(':') ? "[" + hostname + "]" : hostname);
        if (urlObj.port) {
            host += ':' + urlObj.port;
        }
    }
    if (query && typeof query === 'object') {
        query = String(_querystring.urlQueryToSearchParams(query));
    }
    let search = urlObj.search || query && "?" + query || '';
    if (protocol && !protocol.endsWith(':')) protocol += ':';
    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {
        host = '//' + (host || '');
        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;
    } else if (!host) {
        host = '';
    }
    if (hash && hash[0] !== '#') hash = '#' + hash;
    if (search && search[0] !== '?') search = '?' + search;
    pathname = pathname.replace(/[?#]/g, encodeURIComponent);
    search = search.replace('#', '%23');
    return "" + protocol + host + pathname + search + hash;
}
const urlObjectKeys = [
    'auth',
    'hash',
    'host',
    'hostname',
    'href',
    'path',
    'pathname',
    'port',
    'protocol',
    'query',
    'search',
    'slashes'
];
function formatWithValidation(url) {
    if ("TURBOPACK compile-time truthy", 1) {
        if (url !== null && typeof url === 'object') {
            Object.keys(url).forEach((key)=>{
                if (!urlObjectKeys.includes(key)) {
                    console.warn("Unknown key passed via urlObject into url.format: " + key);
                }
            });
        }
    }
    return formatUrl(url);
} //# sourceMappingURL=format-url.js.map
}}),
"[project]/node_modules/next/dist/client/use-merged-ref.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "useMergedRef", {
    enumerable: true,
    get: function() {
        return useMergedRef;
    }
});
const _react = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
function useMergedRef(refA, refB) {
    const cleanupA = (0, _react.useRef)(null);
    const cleanupB = (0, _react.useRef)(null);
    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.
    // (this happens often if the user doesn't pass a ref to Link/Form/Image)
    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),
    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs
    // (because it hasn't been updated for React 19)
    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.
    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.
    return (0, _react.useCallback)((current)=>{
        if (current === null) {
            const cleanupFnA = cleanupA.current;
            if (cleanupFnA) {
                cleanupA.current = null;
                cleanupFnA();
            }
            const cleanupFnB = cleanupB.current;
            if (cleanupFnB) {
                cleanupB.current = null;
                cleanupFnB();
            }
        } else {
            if (refA) {
                cleanupA.current = applyRef(refA, current);
            }
            if (refB) {
                cleanupB.current = applyRef(refB, current);
            }
        }
    }, [
        refA,
        refB
    ]);
}
function applyRef(refA, current) {
    if (typeof refA === 'function') {
        const cleanup = refA(current);
        if (typeof cleanup === 'function') {
            return cleanup;
        } else {
            return ()=>refA(null);
        }
    } else {
        refA.current = current;
        return ()=>{
            refA.current = null;
        };
    }
}
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=use-merged-ref.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/utils.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    DecodeError: null,
    MiddlewareNotFoundError: null,
    MissingStaticPage: null,
    NormalizeError: null,
    PageNotFoundError: null,
    SP: null,
    ST: null,
    WEB_VITALS: null,
    execOnce: null,
    getDisplayName: null,
    getLocationOrigin: null,
    getURL: null,
    isAbsoluteUrl: null,
    isResSent: null,
    loadGetInitialProps: null,
    normalizeRepeatedSlashes: null,
    stringifyError: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    DecodeError: function() {
        return DecodeError;
    },
    MiddlewareNotFoundError: function() {
        return MiddlewareNotFoundError;
    },
    MissingStaticPage: function() {
        return MissingStaticPage;
    },
    NormalizeError: function() {
        return NormalizeError;
    },
    PageNotFoundError: function() {
        return PageNotFoundError;
    },
    SP: function() {
        return SP;
    },
    ST: function() {
        return ST;
    },
    WEB_VITALS: function() {
        return WEB_VITALS;
    },
    execOnce: function() {
        return execOnce;
    },
    getDisplayName: function() {
        return getDisplayName;
    },
    getLocationOrigin: function() {
        return getLocationOrigin;
    },
    getURL: function() {
        return getURL;
    },
    isAbsoluteUrl: function() {
        return isAbsoluteUrl;
    },
    isResSent: function() {
        return isResSent;
    },
    loadGetInitialProps: function() {
        return loadGetInitialProps;
    },
    normalizeRepeatedSlashes: function() {
        return normalizeRepeatedSlashes;
    },
    stringifyError: function() {
        return stringifyError;
    }
});
const WEB_VITALS = [
    'CLS',
    'FCP',
    'FID',
    'INP',
    'LCP',
    'TTFB'
];
function execOnce(fn) {
    let used = false;
    let result;
    return function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        if (!used) {
            used = true;
            result = fn(...args);
        }
        return result;
    };
}
// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1
// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3
const ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\d+\-.]*?:/;
const isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);
function getLocationOrigin() {
    const { protocol, hostname, port } = window.location;
    return protocol + "//" + hostname + (port ? ':' + port : '');
}
function getURL() {
    const { href } = window.location;
    const origin = getLocationOrigin();
    return href.substring(origin.length);
}
function getDisplayName(Component) {
    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';
}
function isResSent(res) {
    return res.finished || res.headersSent;
}
function normalizeRepeatedSlashes(url) {
    const urlParts = url.split('?');
    const urlNoQuery = urlParts[0];
    return urlNoQuery // first we replace any non-encoded backslashes with forward
    // then normalize repeated forward slashes
    .replace(/\\/g, '/').replace(/\/\/+/g, '/') + (urlParts[1] ? "?" + urlParts.slice(1).join('?') : '');
}
async function loadGetInitialProps(App, ctx) {
    if ("TURBOPACK compile-time truthy", 1) {
        var _App_prototype;
        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {
            const message = '"' + getDisplayName(App) + '.getInitialProps()" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';
            throw Object.defineProperty(new Error(message), "__NEXT_ERROR_CODE", {
                value: "E394",
                enumerable: false,
                configurable: true
            });
        }
    }
    // when called from _app `ctx` is nested in `ctx`
    const res = ctx.res || ctx.ctx && ctx.ctx.res;
    if (!App.getInitialProps) {
        if (ctx.ctx && ctx.Component) {
            // @ts-ignore pageProps default
            return {
                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)
            };
        }
        return {};
    }
    const props = await App.getInitialProps(ctx);
    if (res && isResSent(res)) {
        return props;
    }
    if (!props) {
        const message = '"' + getDisplayName(App) + '.getInitialProps()" should resolve to an object. But found "' + props + '" instead.';
        throw Object.defineProperty(new Error(message), "__NEXT_ERROR_CODE", {
            value: "E394",
            enumerable: false,
            configurable: true
        });
    }
    if ("TURBOPACK compile-time truthy", 1) {
        if (Object.keys(props).length === 0 && !ctx.ctx) {
            console.warn("" + getDisplayName(App) + " returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps");
        }
    }
    return props;
}
const SP = typeof performance !== 'undefined';
const ST = SP && [
    'mark',
    'measure',
    'getEntriesByName'
].every((method)=>typeof performance[method] === 'function');
class DecodeError extends Error {
}
class NormalizeError extends Error {
}
class PageNotFoundError extends Error {
    constructor(page){
        super();
        this.code = 'ENOENT';
        this.name = 'PageNotFoundError';
        this.message = "Cannot find module for page: " + page;
    }
}
class MissingStaticPage extends Error {
    constructor(page, message){
        super();
        this.message = "Failed to load static file for page: " + page + " " + message;
    }
}
class MiddlewareNotFoundError extends Error {
    constructor(){
        super();
        this.code = 'ENOENT';
        this.message = "Cannot find the middleware module";
    }
}
function stringifyError(error) {
    return JSON.stringify({
        message: error.message,
        stack: error.stack
    });
} //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/router/utils/is-local-url.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "isLocalURL", {
    enumerable: true,
    get: function() {
        return isLocalURL;
    }
});
const _utils = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/utils.js [app-client] (ecmascript)");
const _hasbasepath = __turbopack_context__.r("[project]/node_modules/next/dist/client/has-base-path.js [app-client] (ecmascript)");
function isLocalURL(url) {
    // prevent a hydration mismatch on href for url with anchor refs
    if (!(0, _utils.isAbsoluteUrl)(url)) return true;
    try {
        // absolute urls can be local if they are on the same origin
        const locationOrigin = (0, _utils.getLocationOrigin)();
        const resolved = new URL(url, locationOrigin);
        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);
    } catch (_) {
        return false;
    }
} //# sourceMappingURL=is-local-url.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/utils/error-once.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "errorOnce", {
    enumerable: true,
    get: function() {
        return errorOnce;
    }
});
let errorOnce = (_)=>{};
if ("TURBOPACK compile-time truthy", 1) {
    const errors = new Set();
    errorOnce = (msg)=>{
        if (!errors.has(msg)) {
            console.error(msg);
        }
        errors.add(msg);
    };
} //# sourceMappingURL=error-once.js.map
}}),
"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    default: null,
    useLinkStatus: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    /**
 * A React component that extends the HTML `<a>` element to provide
 * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)
 * and client-side navigation. This is the primary way to navigate between routes in Next.js.
 *
 * @remarks
 * - Prefetching is only enabled in production.
 *
 * @see https://nextjs.org/docs/app/api-reference/components/link
 */ default: function() {
        return LinkComponent;
    },
    useLinkStatus: function() {
        return useLinkStatus;
    }
});
const _interop_require_wildcard = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs [app-client] (ecmascript)");
const _jsxruntime = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const _react = /*#__PURE__*/ _interop_require_wildcard._(__turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"));
const _formaturl = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/router/utils/format-url.js [app-client] (ecmascript)");
const _approutercontextsharedruntime = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js [app-client] (ecmascript)");
const _routerreducertypes = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js [app-client] (ecmascript)");
const _usemergedref = __turbopack_context__.r("[project]/node_modules/next/dist/client/use-merged-ref.js [app-client] (ecmascript)");
const _utils = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/utils.js [app-client] (ecmascript)");
const _addbasepath = __turbopack_context__.r("[project]/node_modules/next/dist/client/add-base-path.js [app-client] (ecmascript)");
const _warnonce = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/utils/warn-once.js [app-client] (ecmascript)");
const _links = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/links.js [app-client] (ecmascript)");
const _islocalurl = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/router/utils/is-local-url.js [app-client] (ecmascript)");
const _approuterinstance = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/app-router-instance.js [app-client] (ecmascript)");
const _erroronce = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/utils/error-once.js [app-client] (ecmascript)");
function isModifiedEvent(event) {
    const eventTarget = event.currentTarget;
    const target = eventTarget.getAttribute('target');
    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download
    event.nativeEvent && event.nativeEvent.which === 2;
}
function linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {
    const { nodeName } = e.currentTarget;
    // anchors inside an svg have a lowercase nodeName
    const isAnchorNodeName = nodeName.toUpperCase() === 'A';
    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {
        // ignore click for browser’s default behavior
        return;
    }
    if (!(0, _islocalurl.isLocalURL)(href)) {
        if (replace) {
            // browser default behavior does not replace the history state
            // so we need to do it manually
            e.preventDefault();
            location.replace(href);
        }
        // ignore click for browser’s default behavior
        return;
    }
    e.preventDefault();
    const navigate = ()=>{
        if (onNavigate) {
            let isDefaultPrevented = false;
            onNavigate({
                preventDefault: ()=>{
                    isDefaultPrevented = true;
                }
            });
            if (isDefaultPrevented) {
                return;
            }
        }
        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);
    };
    _react.default.startTransition(navigate);
}
function formatStringOrUrl(urlObjOrString) {
    if (typeof urlObjOrString === 'string') {
        return urlObjOrString;
    }
    return (0, _formaturl.formatUrl)(urlObjOrString);
}
function LinkComponent(props) {
    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);
    let children;
    const linkInstanceRef = (0, _react.useRef)(null);
    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;
    children = childrenProp;
    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {
        children = /*#__PURE__*/ (0, _jsxruntime.jsx)("a", {
            children: children
        });
    }
    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);
    const prefetchEnabled = prefetchProp !== false;
    /**
   * The possible states for prefetch are:
   * - null: this is the default "auto" mode, where we will prefetch partially if the link is in the viewport
   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially
   * - false: we will not prefetch if in the viewport at all
   * - 'unstable_dynamicOnHover': this starts in "auto" mode, but switches to "full" when the link is hovered
   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;
    if ("TURBOPACK compile-time truthy", 1) {
        function createPropError(args) {
            return Object.defineProperty(new Error("Failed prop type: The prop `" + args.key + "` expects a " + args.expected + " in `<Link>`, but got `" + args.actual + "` instead." + (typeof window !== 'undefined' ? "\nOpen your browser's console to view the Component stack trace." : '')), "__NEXT_ERROR_CODE", {
                value: "E319",
                enumerable: false,
                configurable: true
            });
        }
        // TypeScript trick for type-guarding:
        const requiredPropsGuard = {
            href: true
        };
        const requiredProps = Object.keys(requiredPropsGuard);
        requiredProps.forEach((key)=>{
            if (key === 'href') {
                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {
                    throw createPropError({
                        key,
                        expected: '`string` or `object`',
                        actual: props[key] === null ? 'null' : typeof props[key]
                    });
                }
            } else {
                // TypeScript trick for type-guarding:
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const _ = key;
            }
        });
        // TypeScript trick for type-guarding:
        const optionalPropsGuard = {
            as: true,
            replace: true,
            scroll: true,
            shallow: true,
            passHref: true,
            prefetch: true,
            unstable_dynamicOnHover: true,
            onClick: true,
            onMouseEnter: true,
            onTouchStart: true,
            legacyBehavior: true,
            onNavigate: true
        };
        const optionalProps = Object.keys(optionalPropsGuard);
        optionalProps.forEach((key)=>{
            const valType = typeof props[key];
            if (key === 'as') {
                if (props[key] && valType !== 'string' && valType !== 'object') {
                    throw createPropError({
                        key,
                        expected: '`string` or `object`',
                        actual: valType
                    });
                }
            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {
                if (props[key] && valType !== 'function') {
                    throw createPropError({
                        key,
                        expected: '`function`',
                        actual: valType
                    });
                }
            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {
                if (props[key] != null && valType !== 'boolean') {
                    throw createPropError({
                        key,
                        expected: '`boolean`',
                        actual: valType
                    });
                }
            } else {
                // TypeScript trick for type-guarding:
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                const _ = key;
            }
        });
    }
    if ("TURBOPACK compile-time truthy", 1) {
        if (props.locale) {
            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');
        }
        if (!asProp) {
            let href;
            if (typeof hrefProp === 'string') {
                href = hrefProp;
            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {
                href = hrefProp.pathname;
            }
            if (href) {
                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));
                if (hasDynamicSegment) {
                    throw Object.defineProperty(new Error("Dynamic href `" + href + "` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href"), "__NEXT_ERROR_CODE", {
                        value: "E267",
                        enumerable: false,
                        configurable: true
                    });
                }
            }
        }
    }
    const { href, as } = _react.default.useMemo({
        "LinkComponent.useMemo": ()=>{
            const resolvedHref = formatStringOrUrl(hrefProp);
            return {
                href: resolvedHref,
                as: asProp ? formatStringOrUrl(asProp) : resolvedHref
            };
        }
    }["LinkComponent.useMemo"], [
        hrefProp,
        asProp
    ]);
    // This will return the first child, if multiple are provided it will throw an error
    let child;
    if (legacyBehavior) {
        if ("TURBOPACK compile-time truthy", 1) {
            if (onClick) {
                console.warn('"onClick" was passed to <Link> with `href` of `' + hrefProp + '` but "legacyBehavior" was set. The legacy behavior requires onClick be set on the child of next/link');
            }
            if (onMouseEnterProp) {
                console.warn('"onMouseEnter" was passed to <Link> with `href` of `' + hrefProp + '` but "legacyBehavior" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');
            }
            try {
                child = _react.default.Children.only(children);
            } catch (err) {
                if (!children) {
                    throw Object.defineProperty(new Error("No children were passed to <Link> with `href` of `" + hrefProp + "` but one child is required https://nextjs.org/docs/messages/link-no-children"), "__NEXT_ERROR_CODE", {
                        value: "E320",
                        enumerable: false,
                        configurable: true
                    });
                }
                throw Object.defineProperty(new Error("Multiple children were passed to <Link> with `href` of `" + hrefProp + "` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children" + (typeof window !== 'undefined' ? " \nOpen your browser's console to view the Component stack trace." : '')), "__NEXT_ERROR_CODE", {
                    value: "E266",
                    enumerable: false,
                    configurable: true
                });
            }
        } else {
            "TURBOPACK unreachable";
        }
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ((children == null ? void 0 : children.type) === 'a') {
                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), "__NEXT_ERROR_CODE", {
                    value: "E209",
                    enumerable: false,
                    configurable: true
                });
            }
        }
    }
    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;
    // Use a callback ref to attach an IntersectionObserver to the anchor tag on
    // mount. In the future we will also use this to keep track of all the
    // currently mounted <Link> instances, e.g. so we can re-prefetch them after
    // a revalidation or refresh.
    const observeLinkVisibilityOnMount = _react.default.useCallback({
        "LinkComponent.useCallback[observeLinkVisibilityOnMount]": (element)=>{
            if (router !== null) {
                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);
            }
            return ({
                "LinkComponent.useCallback[observeLinkVisibilityOnMount]": ()=>{
                    if (linkInstanceRef.current) {
                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);
                        linkInstanceRef.current = null;
                    }
                    (0, _links.unmountPrefetchableInstance)(element);
                }
            })["LinkComponent.useCallback[observeLinkVisibilityOnMount]"];
        }
    }["LinkComponent.useCallback[observeLinkVisibilityOnMount]"], [
        prefetchEnabled,
        href,
        router,
        appPrefetchKind,
        setOptimisticLinkStatus
    ]);
    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);
    const childProps = {
        ref: mergedRef,
        onClick (e) {
            if ("TURBOPACK compile-time truthy", 1) {
                if (!e) {
                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to "onClick" prop.'), "__NEXT_ERROR_CODE", {
                        value: "E312",
                        enumerable: false,
                        configurable: true
                    });
                }
            }
            if (!legacyBehavior && typeof onClick === 'function') {
                onClick(e);
            }
            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {
                child.props.onClick(e);
            }
            if (!router) {
                return;
            }
            if (e.defaultPrevented) {
                return;
            }
            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);
        },
        onMouseEnter (e) {
            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {
                onMouseEnterProp(e);
            }
            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {
                child.props.onMouseEnter(e);
            }
            if (!router) {
                return;
            }
            if ("TURBOPACK compile-time truthy", 1) {
                return;
            }
            "TURBOPACK unreachable";
            const upgradeToDynamicPrefetch = undefined;
        },
        onTouchStart: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : function onTouchStart(e) {
            if (!legacyBehavior && typeof onTouchStartProp === 'function') {
                onTouchStartProp(e);
            }
            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {
                child.props.onTouchStart(e);
            }
            if (!router) {
                return;
            }
            if (!prefetchEnabled) {
                return;
            }
            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;
            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);
        }
    };
    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is
    // defined, we specify the current 'href', so that repetition is not needed by the user.
    // If the url is absolute, we can bypass the logic to prepend the basePath.
    if ((0, _utils.isAbsoluteUrl)(as)) {
        childProps.href = as;
    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {
        childProps.href = (0, _addbasepath.addBasePath)(as);
    }
    let link;
    if (legacyBehavior) {
        if ("TURBOPACK compile-time truthy", 1) {
            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\n\n' + 'npx @next/codemod@latest new-link .\n\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');
        }
        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);
    } else {
        link = /*#__PURE__*/ (0, _jsxruntime.jsx)("a", {
            ...restProps,
            ...childProps,
            children: children
        });
    }
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {
        value: linkStatus,
        children: link
    });
}
const LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);
const useLinkStatus = ()=>{
    return (0, _react.useContext)(LinkStatusContext);
};
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=link.js.map
}}),
}]);

//# sourceMappingURL=_b8c2863f._.js.map