exports.id=70,exports.ids=[70],exports.modules={21595:(e,t,r)=>{Promise.resolve().then(r.bind(r,73918))},24731:(e,t,r)=>{"use strict";r.d(t,{uE:()=>i});var n=r(51060),s=r(51785),a=r(38157);class o{constructor(e=a.I){this.storage=e,this.client=this.createClient(),this.setupInterceptors()}createClient(){return n.A.create({baseURL:s.i3.BASE_URL,timeout:s.i3.TIMEOUT,headers:{"Content-Type":"application/json"}})}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.storage.getItem(s.d5.AUTH_TOKEN);return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(this.handleError(e))),this.client.interceptors.response.use(e=>e,e=>(e.response?.status===401&&this.handleUnauthorized(),Promise.reject(this.handleError(e))))}handleUnauthorized(){this.storage.removeItem(s.d5.AUTH_TOKEN)}handleError(e){if(!e.response)return Error(s.UU.NETWORK_ERROR);switch(e.response.status){case 401:return Error(s.UU.UNAUTHORIZED);case 400:return Error(s.UU.VALIDATION_ERROR);default:let t=e.response.data;return Error(t?.message||s.UU.GENERIC_ERROR)}}get instance(){return this.client}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}}let i=new o},32211:(e,t,r)=>{Promise.resolve().then(r.bind(r,63772))},38157:(e,t,r)=>{"use strict";r.d(t,{I:()=>s});class n{getItem(e){return null}setItem(e,t){}removeItem(e){}clear(){}}let s=new n},45443:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},51785:(e,t,r)=>{"use strict";r.d(t,{ID:()=>s,Ij:()=>n,KA:()=>l,Ot:()=>i,RW:()=>A,UU:()=>h,WF:()=>d,d5:()=>c,gY:()=>u,gx:()=>E,i3:()=>o,m0:()=>a});let n=[{name:"Dashboard",path:"/dashboard"},{name:"Content",path:"/content"},{name:"Analytics",path:"/analytics"},{name:"Settings",path:"/settings"},{name:"Auth",path:"/auth"},{name:"Test API",path:"/test-connection"}],s=[{value:"engaging",label:"Engaging"},{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"educational",label:"Educational"},{value:"humorous",label:"Humorous"},{value:"informative",label:"Informative"},{value:"helpful",label:"Helpful"}],a=[{value:"short",label:"Short (1-2 sentences)"},{value:"medium",label:"Medium (3-5 sentences)"},{value:"long",label:"Long (6+ sentences)"}],o={BASE_URL:"http://localhost:8000/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},i={MAX_TWEET_LENGTH:280,MAX_THREAD_TWEETS:25,DEFAULT_THREAD_SIZE:3,MAX_HASHTAGS_RECOMMENDED:3,MAX_MENTIONS_RECOMMENDED:5},l={DEFAULT_STYLE:"engaging",DEFAULT_LANGUAGE:"en",SUPPORTED_STYLES:["engaging","professional","casual","educational","humorous","informative","helpful"],SUPPORTED_LANGUAGES:[{code:"en",name:"English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"}]},u={MIN_TOPIC_LENGTH:3,MAX_TOPIC_LENGTH:200,MIN_CONTENT_LENGTH:1,MAX_CONTENT_LENGTH:2e3,MIN_USERNAME_LENGTH:3,MAX_USERNAME_LENGTH:50,MIN_PASSWORD_LENGTH:8,MAX_PASSWORD_LENGTH:128},c={AUTH_TOKEN:"authToken",USER_PREFERENCES:"userPreferences",DRAFT_CONTENT:"draftContent",THEME:"theme"},h={NETWORK_ERROR:"Network error. Please check your connection.",UNAUTHORIZED:"Your session has expired. Please log in again.",FORBIDDEN:"Access denied.",NOT_FOUND:"The requested resource was not found.",VALIDATION_ERROR:"Please check your input and try again.",GENERATION_FAILED:"Content generation failed. Please try again.",RATE_LIMIT_EXCEEDED:"Rate limit exceeded. Please try again later.",GENERIC_ERROR:"Something went wrong. Please try again."},E={OK:200,CREATED:201,NO_CONTENT:204,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,CONFLICT:409,UNPROCESSABLE_ENTITY:422,TOO_MANY_REQUESTS:429,INTERNAL_SERVER_ERROR:500,SERVICE_UNAVAILABLE:503},d={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,USERNAME:/^[a-zA-Z0-9_]+$/,PASSWORD:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,HASHTAG:/#\w+/g,MENTION:/@\w+/g,URL:/https?:\/\/[^\s]+/g},A=e=>{switch(e){case E.UNAUTHORIZED:return h.UNAUTHORIZED;case E.FORBIDDEN:return h.FORBIDDEN;case E.NOT_FOUND:return h.NOT_FOUND;case E.UNPROCESSABLE_ENTITY:return h.VALIDATION_ERROR;case E.TOO_MANY_REQUESTS:return h.RATE_LIMIT_EXCEEDED;case E.INTERNAL_SERVER_ERROR:case E.SERVICE_UNAVAILABLE:return h.GENERATION_FAILED;default:return h.GENERIC_ERROR}}},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>i});var n=r(37413),s=r(1455),a=r.n(s);r(82704);var o=r(73918);let i={title:"Reachly",description:"Automate your Twitter growth with AI-powered content creation and engagement",icons:{icon:"/favicon.svg",shortcut:"/favicon.svg",apple:"/favicon.svg"}};function l({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${a().variable} font-sans antialiased`,children:(0,n.jsx)(o.AuthProvider,{children:e})})})}},59515:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},63772:(e,t,r)=>{"use strict";r.d(t,{A:()=>E,AuthProvider:()=>h});var n=r(60687),s=r(43210),a=r(16189),o=r(74265);let i=(0,s.createContext)(void 0),l=["/","/login","/register","/auth/twitter/callback","/auth/twitter/oauth2-callback","/test-twitter-oauth2","/terms","/privacy","/about","/contact"],u=["/auth/twitter/callback","/auth/twitter/oauth2-callback"],c=["/dashboard","/content","/analytics","/settings","/profile"];function h({children:e}){let[t,r]=(0,s.useState)(null),[h,E]=(0,s.useState)(!0),d=(0,a.useRouter)(),A=(0,a.usePathname)(),m=!!t&&!!o.y.getToken();l.some(e=>"/"===e?"/"===A:A.startsWith(e)),c.some(e=>A.startsWith(e)),u.some(e=>A.startsWith(e));let T=async()=>{try{console.log("\uD83D\uDD04 AuthContext: Starting refreshUser...");let e=o.y.getToken();if(console.log("\uD83D\uDD0D AuthContext: Token check:",{hasToken:!!e,tokenLength:e?.length}),!e){console.log("❌ AuthContext: No token found, setting user to null"),r(null);return}console.log("\uD83D\uDD04 AuthContext: Fetching current user...");let t=await o.y.getCurrentUser();console.log("✅ AuthContext: User fetched successfully:",{userId:t.id,username:t.username,twitterUsername:t.twitter_username}),r(t)}catch(e){console.error("❌ AuthContext: Failed to refresh user:",e),r(null),o.y.logout()}},R=async e=>{try{await o.y.login(e),await T()}catch(e){throw e}};return(0,n.jsx)(i.Provider,{value:{user:t,isLoading:h,isAuthenticated:m,login:R,logout:()=>{o.y.logout(),r(null),d.push("/")},refreshUser:T},children:e})}function E(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},73918:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var n=r(12907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\contexts\\AuthContext.tsx","AuthProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\contexts\\AuthContext.tsx","useAuth"),(0,n.registerClientReference)(function(){throw Error("Attempted to call withAuth() from the server but withAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\contexts\\AuthContext.tsx","withAuth")},74265:(e,t,r)=>{"use strict";r.d(t,{y:()=>i});var n=r(51785),s=r(38157),a=r(24731);class o{constructor(e){this.apiClient=e}async login(e){this.validateLoginRequest(e);let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let r=await this.apiClient.instance.post("/auth/token",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return s.I.setItem(n.d5.AUTH_TOKEN,r.data.access_token),r.data}async register(e){return this.validateRegisterRequest(e),this.apiClient.post("/users/",e)}async getCurrentUser(){return this.apiClient.get("/auth/me")}async logout(){s.I.removeItem(n.d5.AUTH_TOKEN)}isAuthenticated(){return!!s.I.getItem(n.d5.AUTH_TOKEN)}getToken(){return s.I.getItem(n.d5.AUTH_TOKEN)}async initiateTwitterOAuth2(){return(await this.apiClient.instance.post("/auth/oauth2/twitter/init")).data}async handleTwitterOAuth2Callback(e){let t=await this.apiClient.instance.post("/auth/oauth2/twitter/callback",e);return s.I.setItem(n.d5.AUTH_TOKEN,t.data.access_token),t.data}validateLoginRequest(e){if(!e.username?.trim())throw Error("Username is required");if(!e.password?.trim())throw Error("Password is required")}validateRegisterRequest(e){if(!e.username?.trim())throw Error("Username is required");if(!e.email?.trim())throw Error("Email is required");if(!e.password?.trim())throw Error("Password is required");if(e.password.length<6)throw Error("Password must be at least 6 characters");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email))throw Error("Please enter a valid email address")}}let i=new o(a.uE)},82704:()=>{}};