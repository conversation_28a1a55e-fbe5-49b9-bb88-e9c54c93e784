(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/components/ui/Logo.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
;
;
const Logo = ({ href = '/dashboard', size = 'md', showText = true })=>{
    const sizes = {
        sm: {
            icon: 'w-6 h-6',
            text: 'text-lg'
        },
        md: {
            icon: 'w-8 h-8',
            text: 'text-xl'
        },
        lg: {
            icon: 'w-10 h-10',
            text: 'text-2xl'
        }
    };
    const logoContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center space-x-2 select-none",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `${sizes[size].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: `text-white font-bold ${size === 'sm' ? 'text-sm' : 'text-lg'}`,
                    children: "A"
                }, void 0, false, {
                    fileName: "[project]/app/components/ui/Logo.tsx",
                    lineNumber: 19,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/components/ui/Logo.tsx",
                lineNumber: 18,
                columnNumber: 7
            }, this),
            showText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: `${sizes[size].text} font-bold text-gray-900 whitespace-nowrap`,
                children: "AutoReach"
            }, void 0, false, {
                fileName: "[project]/app/components/ui/Logo.tsx",
                lineNumber: 22,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/ui/Logo.tsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
    if (href) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            href: href,
            children: logoContent
        }, void 0, false, {
            fileName: "[project]/app/components/ui/Logo.tsx",
            lineNumber: 29,
            columnNumber: 7
        }, this);
    }
    return logoContent;
};
_c = Logo;
const __TURBOPACK__default__export__ = Logo;
var _c;
__turbopack_context__.k.register(_c, "Logo");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/Header.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Logo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/ui/Logo.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/contexts/AuthContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
const Header = ({ showNavigation = true })=>{
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUserMenuOpen, setIsUserMenuOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { user, logout, isAuthenticated } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "bg-white shadow-sm border-b border-gray-200 relative z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-between items-center h-16",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Logo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                                fileName: "[project]/app/components/Header.tsx",
                                lineNumber: 26,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/components/Header.tsx",
                            lineNumber: 25,
                            columnNumber: 11
                        }, this),
                        showNavigation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "hidden md:flex space-x-6",
                            children: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NAVIGATION_ITEMS"].map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: item.path,
                                    className: `px-3 py-2 rounded-md text-sm font-medium transition-colors ${pathname === item.path ? 'text-primary-600 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'} ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100' : ''}`,
                                    children: item.name
                                }, item.path, false, {
                                    fileName: "[project]/app/components/Header.tsx",
                                    lineNumber: 33,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/app/components/Header.tsx",
                            lineNumber: 31,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                showNavigation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),
                                    className: "md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500",
                                    "aria-label": "Toggle mobile menu",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-6 h-6",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: isMobileMenuOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M6 18L18 6M6 6l12 12"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/Header.tsx",
                                            lineNumber: 59,
                                            columnNumber: 21
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M4 6h16M4 12h16M4 18h16"
                                        }, void 0, false, {
                                            fileName: "[project]/app/components/Header.tsx",
                                            lineNumber: 61,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/components/Header.tsx",
                                        lineNumber: 57,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/components/Header.tsx",
                                    lineNumber: 52,
                                    columnNumber: 15
                                }, this),
                                isAuthenticated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),
                                            className: "flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium text-purple-600",
                                                        children: user?.full_name?.[0] || user?.username?.[0] || 'U'
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/Header.tsx",
                                                        lineNumber: 75,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/Header.tsx",
                                                    lineNumber: 74,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "hidden sm:block text-sm font-medium",
                                                    children: user?.full_name || user?.username
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/Header.tsx",
                                                    lineNumber: 79,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                    className: "w-4 h-4",
                                                    fill: "none",
                                                    stroke: "currentColor",
                                                    viewBox: "0 0 24 24",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M19 9l-7 7-7-7"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/components/Header.tsx",
                                                        lineNumber: 83,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/Header.tsx",
                                                    lineNumber: 82,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/Header.tsx",
                                            lineNumber: 70,
                                            columnNumber: 17
                                        }, this),
                                        isUserMenuOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "px-4 py-2 text-sm text-gray-700 border-b border-gray-100",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "font-medium",
                                                            children: user?.full_name || user?.username
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/components/Header.tsx",
                                                            lineNumber: 91,
                                                            columnNumber: 23
                                                        }, this),
                                                        user?.twitter_username && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-gray-500",
                                                            children: [
                                                                "@",
                                                                user.twitter_username
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/components/Header.tsx",
                                                            lineNumber: 93,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/components/Header.tsx",
                                                    lineNumber: 90,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: "/settings",
                                                    onClick: ()=>setIsUserMenuOpen(false),
                                                    className: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                                                    children: "Settings"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/Header.tsx",
                                                    lineNumber: 96,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>{
                                                        setIsUserMenuOpen(false);
                                                        logout();
                                                    },
                                                    className: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                                                    children: "Sign out"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/components/Header.tsx",
                                                    lineNumber: 103,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/Header.tsx",
                                            lineNumber: 89,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/Header.tsx",
                                    lineNumber: 69,
                                    columnNumber: 15
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/login",
                                    className: "bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",
                                    children: "Sign In"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/Header.tsx",
                                    lineNumber: 116,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/Header.tsx",
                            lineNumber: 49,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/Header.tsx",
                    lineNumber: 23,
                    columnNumber: 9
                }, this),
                showNavigation && isMobileMenuOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                        className: "px-4 py-4 space-y-2",
                        children: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NAVIGATION_ITEMS"].map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: item.path,
                                onClick: ()=>setIsMobileMenuOpen(false),
                                className: `block px-4 py-3 rounded-lg text-base font-medium transition-colors ${pathname === item.path ? 'text-primary-600 bg-primary-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'} ${item.name === 'Test API' ? 'bg-green-50 text-green-700 hover:bg-green-100 border border-green-200' : ''}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "flex items-center gap-3",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-lg",
                                            children: [
                                                item.name === 'Dashboard' && '📊',
                                                item.name === 'Content' && '✍️',
                                                item.name === 'Analytics' && '📈',
                                                item.name === 'Settings' && '⚙️',
                                                item.name === 'Auth' && '🔐',
                                                item.name === 'Test API' && '🧪'
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/components/Header.tsx",
                                            lineNumber: 142,
                                            columnNumber: 21
                                        }, this),
                                        item.name
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/components/Header.tsx",
                                    lineNumber: 141,
                                    columnNumber: 19
                                }, this)
                            }, item.path, false, {
                                fileName: "[project]/app/components/Header.tsx",
                                lineNumber: 131,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/components/Header.tsx",
                        lineNumber: 129,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/components/Header.tsx",
                    lineNumber: 128,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/components/Header.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/Header.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
};
_s(Header, "jgcMI8MVy19yZaLNhNGU9b7wE5g=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = Header;
const __TURBOPACK__default__export__ = Header;
var _c;
__turbopack_context__.k.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/validation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Frontend validation utilities following DRY and KISS principles.
 * Centralizes validation logic to eliminate code duplication.
 */ __turbopack_context__.s({
    "ContentValidation": (()=>ContentValidation),
    "FormValidation": (()=>FormValidation),
    "ValidationHooks": (()=>ValidationHooks),
    "ValidationSchemas": (()=>ValidationSchemas),
    "ValidationUtils": (()=>ValidationUtils),
    "validateContentGenerationForm": (()=>validateContentGenerationForm),
    "validateUserLoginForm": (()=>validateUserLoginForm),
    "validateUserRegistrationForm": (()=>validateUserRegistrationForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
;
class ValidationUtils {
    /**
   * Validate required field
   */ static validateRequired(value, fieldName = 'This field') {
        if (value === null || value === undefined) {
            // If fieldName contains "is required" or similar, use it as is
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        if (typeof value === 'string' && !value.trim()) {
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        if (Array.isArray(value) && value.length === 0) {
            if (fieldName.toLowerCase().includes('required') || fieldName.toLowerCase().includes('error')) {
                return {
                    isValid: false,
                    error: fieldName
                };
            }
            return {
                isValid: false,
                error: `${fieldName} is required`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate string length
   */ static validateLength(value, minLength, maxLength) {
        if (typeof value !== 'string') {
            return {
                isValid: false,
                error: 'Value must be a string'
            };
        }
        const length = value.length;
        if (minLength !== undefined && length < minLength) {
            return {
                isValid: false,
                error: `Must be at least ${minLength} characters long`
            };
        }
        if (maxLength !== undefined && length > maxLength) {
            return {
                isValid: false,
                error: `Must be no more than ${maxLength} characters long`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate email format
   */ static validateEmail(email) {
        if (!email) {
            return {
                isValid: false,
                error: 'Email is required'
            };
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].EMAIL.test(email)) {
            return {
                isValid: false,
                error: 'Please enter a valid email address'
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate pattern match
   */ static validatePattern(value, pattern, errorMessage = 'Invalid format') {
        if (!pattern.test(value)) {
            return {
                isValid: false,
                error: errorMessage
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate numeric range
   */ static validateRange(value, min, max) {
        if (value < min || value > max) {
            return {
                isValid: false,
                error: `Must be between ${min} and ${max}`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate choice from options
   */ static validateChoice(value, choices, errorMessage = 'Please select a valid option') {
        if (!choices.includes(value)) {
            return {
                isValid: false,
                error: errorMessage
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate username
   */ static validateUsername(username) {
        const requiredResult = this.validateRequired(username, 'Username');
        if (!requiredResult.isValid) return requiredResult;
        const lengthResult = this.validateLength(username, 3, 20);
        if (!lengthResult.isValid) return lengthResult;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].USERNAME.test(username)) {
            return {
                isValid: false,
                error: 'Username can only contain letters, numbers, and underscores'
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate password
   */ static validatePassword(password) {
        const requiredResult = this.validateRequired(password, 'Password');
        if (!requiredResult.isValid) return requiredResult;
        if (password.length < __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_PASSWORD_LENGTH) {
            return {
                isValid: false,
                error: `Password must be at least ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_PASSWORD_LENGTH} characters long`
            };
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].PASSWORD.test(password)) {
            return {
                isValid: false,
                error: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
            };
        }
        return {
            isValid: true
        };
    }
}
class ContentValidation {
    /**
   * Validate topic for content generation
   */ static validateTopic(topic) {
        const requiredResult = ValidationUtils.validateRequired(topic, 'Topic');
        if (!requiredResult.isValid) return requiredResult;
        const length = topic.length;
        if (length < __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_TOPIC_LENGTH) {
            return {
                isValid: false,
                error: `Topic must be at least ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_TOPIC_LENGTH} characters long`
            };
        }
        if (length > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH) {
            return {
                isValid: false,
                error: `Topic must be no more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH} characters long`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Validate content for tweets
   */ static validateContent(content) {
        const requiredResult = ValidationUtils.validateRequired(content, 'Content');
        if (!requiredResult.isValid) return requiredResult;
        const length = content.length;
        if (length > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH) {
            return {
                isValid: false,
                error: `Content must be no more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH} characters long`
            };
        }
        const warnings = [];
        // Check for too many hashtags
        const hashtagCount = ContentValidation.countHashtags(content);
        if (hashtagCount > 3) {
            warnings.push('Consider using fewer hashtags for better engagement');
        }
        // Check for too many mentions
        const mentionCount = ContentValidation.countMentions(content);
        if (mentionCount > 5) {
            warnings.push('Too many mentions may reduce visibility');
        }
        return {
            isValid: true,
            warnings: warnings.length > 0 ? warnings : undefined
        };
    }
    /**
   * Validate content style
   */ static validateStyle(style) {
        return ValidationUtils.validateChoice(style, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_CONSTANTS"].SUPPORTED_STYLES, 'Please select a valid style');
    }
    /**
   * Validate language
   */ static validateLanguage(language) {
        const supportedLanguages = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_CONSTANTS"].SUPPORTED_LANGUAGES.map((lang)=>lang.code);
        return ValidationUtils.validateChoice(language, supportedLanguages, 'Please select a valid language');
    }
    /**
   * Validate thread size
   */ static validateThreadSize(size) {
        if (size < 2) {
            return {
                isValid: false,
                error: 'Thread must have at least 2 tweets'
            };
        }
        if (size > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS) {
            return {
                isValid: false,
                error: `Thread cannot have more than ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS} tweets`
            };
        }
        return {
            isValid: true
        };
    }
    /**
   * Count hashtags in content
   */ static countHashtags(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].HASHTAG);
        return matches ? matches.length : 0;
    }
    /**
   * Count mentions in content
   */ static countMentions(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].MENTION);
        return matches ? matches.length : 0;
    }
    /**
   * Count URLs in content
   */ static countUrls(content) {
        const matches = content.match(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REGEX_PATTERNS"].URL);
        return matches ? matches.length : 0;
    }
    /**
   * Get content statistics
   */ static getContentStats(content) {
        const characterCount = content.length;
        const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
        return {
            characterCount,
            wordCount,
            hashtagCount: ContentValidation.countHashtags(content),
            mentionCount: ContentValidation.countMentions(content),
            urlCount: ContentValidation.countUrls(content),
            // Add properties expected by tests
            length: characterCount,
            remaining: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH - characterCount
        };
    }
}
class FormValidation {
    /**
   * Validate multiple fields and return combined result
   */ static validateFields(validations) {
        const errors = [];
        for (const validation of validations){
            const result = validation();
            if (!result.isValid && result.error) {
                errors.push(result.error);
            }
        }
        return {
            isValid: errors.length === 0,
            errors
        };
    }
    /**
   * Validate form data against schema
   */ static validateFormData(data, schema) {
        const errors = {};
        for (const [field, validator] of Object.entries(schema)){
            const result = validator(data[field]);
            if (!result.isValid && result.error) {
                errors[field] = result.error;
            }
        }
        return errors;
    }
    /**
   * Validate single field
   */ static validateField(value, validator) {
        return validator(value);
    }
}
class ValidationHooks {
    /**
   * Debounced validation for real-time feedback
   */ static createDebouncedValidator(validator, delay = 300) {
        let timeoutId;
        return (value, callback)=>{
            clearTimeout(timeoutId);
            timeoutId = setTimeout(()=>{
                const result = validator(value);
                callback(result);
            }, delay);
        };
    }
}
const ValidationSchemas = {
    contentGeneration: {
        topic: (value)=>ContentValidation.validateTopic(value),
        style: (value)=>ContentValidation.validateStyle(value),
        language: (value)=>ContentValidation.validateLanguage(value)
    },
    userRegistration: {
        username: (value)=>ValidationUtils.validateUsername(value),
        email: (value)=>ValidationUtils.validateEmail(value),
        password: (value)=>ValidationUtils.validatePassword(value)
    },
    userLogin: {
        email: (value)=>ValidationUtils.validateEmail(value),
        password: (value)=>ValidationUtils.validateRequired(value, 'Password')
    }
};
const validateContentGenerationForm = (data)=>{
    const errors = {};
    const topicValidation = ValidationSchemas.contentGeneration.topic(data.topic);
    if (!topicValidation.isValid && topicValidation.error) {
        errors.topic = topicValidation.error;
    }
    return errors;
};
const validateUserRegistrationForm = (data)=>{
    return FormValidation.validateFormData(data, ValidationSchemas.userRegistration);
};
const validateUserLoginForm = (data)=>{
    return FormValidation.validateFormData(data, ValidationSchemas.userLogin);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/errorHandling.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* eslint-disable @typescript-eslint/no-explicit-any */ /**
 * Frontend error handling utilities following DRY and KISS principles.
 * Centralizes error handling logic to eliminate code duplication.
 */ __turbopack_context__.s({
    "AuthErrorHandler": (()=>AuthErrorHandler),
    "ContentErrorHandler": (()=>ContentErrorHandler),
    "ErrorHandler": (()=>ErrorHandler),
    "ErrorLogger": (()=>ErrorLogger),
    "ErrorTypes": (()=>ErrorTypes),
    "NetworkErrorHandler": (()=>NetworkErrorHandler),
    "RetryHandler": (()=>RetryHandler),
    "createAsyncErrorHandler": (()=>createAsyncErrorHandler),
    "handleAuthError": (()=>handleAuthError),
    "handleContentError": (()=>handleContentError),
    "handleNetworkError": (()=>handleNetworkError),
    "withErrorHandling": (()=>withErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
;
const ErrorTypes = {
    NETWORK: 'NETWORK',
    VALIDATION: 'VALIDATION',
    AUTHENTICATION: 'AUTHENTICATION',
    AUTHORIZATION: 'AUTHORIZATION',
    RATE_LIMIT: 'RATE_LIMIT',
    SERVER: 'SERVER',
    CLIENT: 'CLIENT',
    UNKNOWN: 'UNKNOWN'
};
class ErrorHandler {
    /**
   * Main error handler that processes errors and returns structured result
   */ static handleError(error, context) {
        const message = this.getErrorMessage(error);
        const type = this.getErrorType(error);
        const userMessage = this.getUserFriendlyMessage(type);
        const result = {
            type,
            message,
            userMessage,
            context,
            timestamp: Date.now()
        };
        this.logError(error, context);
        return result;
    }
    /**
   * Get error message from various error types
   */ static getErrorMessage(error) {
        if (error instanceof Error) {
            return error.message;
        }
        if (typeof error === 'string') {
            return error;
        }
        if (error && typeof error === 'object' && 'message' in error) {
            return String(error.message);
        }
        return 'An unknown error occurred';
    }
    /**
   * Determine error type based on error content
   */ static getErrorType(error) {
        const message = this.getErrorMessage(error).toLowerCase();
        if (message.includes('network') || message.includes('fetch')) {
            return ErrorTypes.NETWORK;
        }
        if (message.includes('validation') || message.includes('invalid')) {
            return ErrorTypes.VALIDATION;
        }
        if (message.includes('unauthorized') || message.includes('auth')) {
            return ErrorTypes.AUTHENTICATION;
        }
        if (message.includes('forbidden')) {
            return ErrorTypes.AUTHORIZATION;
        }
        if (message.includes('too many requests') || message.includes('rate limit')) {
            return ErrorTypes.RATE_LIMIT;
        }
        if (message.includes('server error') || message.includes('internal')) {
            return ErrorTypes.SERVER;
        }
        if (message.includes('bad request') || message.includes('client')) {
            return ErrorTypes.CLIENT;
        }
        return ErrorTypes.UNKNOWN;
    }
    /**
   * Get user-friendly message for error type
   */ static getUserFriendlyMessage(errorType) {
        switch(errorType){
            case ErrorTypes.NETWORK:
                return 'Network error. Please check your connection.';
            case ErrorTypes.VALIDATION:
                return 'Please check your input and try again.';
            case ErrorTypes.AUTHENTICATION:
                return 'Please log in to continue.';
            case ErrorTypes.AUTHORIZATION:
                return 'You do not have permission to perform this action.';
            case ErrorTypes.RATE_LIMIT:
                return 'Too many requests. Please wait a moment and try again.';
            case ErrorTypes.SERVER:
                return 'Server error. Please try again later.';
            case ErrorTypes.CLIENT:
                return 'Invalid request. Please check your input.';
            default:
                return 'An unexpected error occurred. Please try again.';
        }
    }
    /**
   * Check if error is retryable
   */ static isRetryableError(error) {
        const type = this.getErrorType(error);
        const message = this.getErrorMessage(error).toLowerCase();
        // Network errors are retryable
        if (type === ErrorTypes.NETWORK) {
            return true;
        }
        // Timeout errors are retryable
        if (message.includes('timeout')) {
            return true;
        }
        // Server errors (5xx) are retryable
        if (type === ErrorTypes.SERVER || message.includes('server error')) {
            return true;
        }
        // Rate limit errors are retryable (after delay)
        if (type === ErrorTypes.RATE_LIMIT) {
            return true;
        }
        // Authentication, validation, and client errors are not retryable
        return false;
    }
    /**
   * Log error with context
   */ static logError(error, context) {
        const message = this.getErrorMessage(error);
        const logData = {
            message,
            context,
            timestamp: new Date().toISOString(),
            stack: error instanceof Error ? error.stack : undefined
        };
        console.error('Error occurred:', logData);
    }
    /**
   * Handle API errors with consistent formatting
   */ static handleApiError(error, options = {}) {
        const { showToast = false, logError = true, fallbackMessage = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERIC_ERROR } = options;
        let apiError;
        // Type guard for error objects
        const isErrorWithResponse = (err)=>{
            return typeof err === 'object' && err !== null && 'response' in err;
        };
        const isErrorWithRequest = (err)=>{
            return typeof err === 'object' && err !== null && 'request' in err;
        };
        const isErrorWithMessage = (err)=>{
            return typeof err === 'object' && err !== null && 'message' in err;
        };
        if (isErrorWithResponse(error)) {
            // HTTP error response
            const status = error.response.status;
            const data = error.response.data;
            apiError = {
                message: data?.message || (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getErrorMessageByStatus"])(status),
                status,
                code: data?.code,
                details: data?.details
            };
        } else if (isErrorWithRequest(error)) {
            // Network error
            apiError = {
                message: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].NETWORK_ERROR,
                status: 0,
                code: 'NETWORK_ERROR'
            };
        } else {
            // Other error
            apiError = {
                message: isErrorWithMessage(error) ? error.message : fallbackMessage,
                code: 'UNKNOWN_ERROR'
            };
        }
        if (logError) {
            console.error('API Error:', apiError, error);
        }
        if (showToast) {
        // This would integrate with your toast system
        // toast.error(apiError.message);
        }
        return apiError;
    }
    /**
   * Handle validation errors
   */ static handleValidationError(errors, options = {}) {
        const { logError = true } = options;
        let message;
        if (Array.isArray(errors)) {
            message = errors.join(', ');
        } else if (typeof errors === 'object' && errors !== null) {
            message = Object.values(errors).join(', ');
        } else {
            message = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].VALIDATION_ERROR;
        }
        const apiError = {
            message,
            status: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HTTP_STATUS"].UNPROCESSABLE_ENTITY,
            code: 'VALIDATION_ERROR',
            details: typeof errors === 'object' && errors !== null ? errors : undefined
        };
        if (logError) {
            console.warn('Validation Error:', apiError);
        }
        return apiError;
    }
    /**
   * Handle async operation errors
   */ static async handleAsyncError(operation, options = {}) {
        try {
            const data = await operation();
            return {
                data
            };
        } catch (error) {
            const apiError = this.handleApiError(error, options);
            return {
                error: apiError
            };
        }
    }
    /**
   * Create error boundary handler
   */ static createErrorBoundaryHandler(fallbackComponent) {
        return (error, errorInfo)=>{
            console.error('Error Boundary caught an error:', error, errorInfo);
            // Log to error reporting service
            // errorReportingService.captureException(error, errorInfo);
            return fallbackComponent;
        };
    }
}
class ContentErrorHandler {
    /**
   * Handle content generation errors
   */ static handleGenerationError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERATION_FAILED,
            logError: true
        });
    }
    /**
   * Handle content validation errors
   */ static handleContentValidationError(errors) {
        return ErrorHandler.handleValidationError(errors, {
            logError: true
        });
    }
}
class AuthErrorHandler {
    /**
   * Handle authentication errors
   */ static handleAuthError(error) {
        const apiError = ErrorHandler.handleApiError(error, {
            logError: true
        });
        // Handle specific auth scenarios
        if (apiError.status === __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HTTP_STATUS"].UNAUTHORIZED) {
            // Clear auth tokens
            localStorage.removeItem('authToken');
        // Redirect to login if needed
        // router.push('/login');
        }
        return apiError;
    }
    /**
   * Handle token expiration
   */ static handleTokenExpiration() {
        localStorage.removeItem('authToken');
    // Show token expired message
    // toast.error(ERROR_MESSAGES.UNAUTHORIZED);
    // Redirect to login
    // router.push('/login');
    }
}
class NetworkErrorHandler {
    /**
   * Handle network connectivity issues
   */ static handleNetworkError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].NETWORK_ERROR,
            logError: true
        });
    }
    /**
   * Handle rate limiting
   */ static handleRateLimitError(error) {
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].RATE_LIMIT_EXCEEDED,
            logError: true
        });
    }
}
class RetryHandler {
    /**
   * Retry failed operations with exponential backoff
   */ static async retryOperation(operation, maxRetries = 3, baseDelay = 1000) {
        let lastError;
        for(let attempt = 0; attempt <= maxRetries; attempt++){
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                if (attempt === maxRetries) {
                    throw error;
                }
                // Exponential backoff
                const delay = baseDelay * Math.pow(2, attempt);
                await new Promise((resolve)=>setTimeout(resolve, delay));
            }
        }
        throw lastError;
    }
    /**
   * Check if error is retryable
   */ static isRetryableError(error) {
        if (!error.response) {
            return true; // Network errors are retryable
        }
        const status = error.response.status;
        return status >= 500 || status === __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HTTP_STATUS"].TOO_MANY_REQUESTS;
    }
}
class ErrorLogger {
    /**
   * Log error with timestamp and context
   */ static log(error, context) {
        const timestamp = new Date().toISOString();
        const message = error instanceof Error ? error.message : String(error);
        if (context) {
            console.error(`[ERROR] ${timestamp}`, message, 'Context:', context);
        } else {
            console.error(`[ERROR] ${timestamp}`, message);
        }
    }
    /**
   * Log warning with timestamp and context
   */ static warn(message, context) {
        const timestamp = new Date().toISOString();
        if (context) {
            console.warn(`[WARN] ${timestamp}`, message, 'Context:', context);
        } else {
            console.warn(`[WARN] ${timestamp}`, message);
        }
    }
    /**
   * Log info message
   */ static info(message, context) {
        const timestamp = new Date().toISOString();
        if (context) {
            console.log(`[INFO] ${timestamp}`, message, 'Context:', context);
        } else {
            console.log(`[INFO] ${timestamp}`, message);
        }
    }
    /**
   * Log debug message (only in development)
   */ static debug(message, context) {
        if ("TURBOPACK compile-time truthy", 1) {
            const timestamp = new Date().toISOString();
            if (context) {
                console.log(`[DEBUG] ${timestamp}`, message, 'Context:', context);
            } else {
                console.log(`[DEBUG] ${timestamp}`, message);
            }
        }
    }
    /**
   * Log error to console with context (legacy method)
   */ static logError(error, context, additionalData) {
        console.group(`🚨 Error in ${context}`);
        console.error('Error:', error);
        if (additionalData) {
            console.error('Additional Data:', additionalData);
        }
        console.error('Stack:', error.stack);
        console.groupEnd();
    }
    /**
   * Log warning with context (legacy method)
   */ static logWarning(message, context, additionalData) {
        console.group(`⚠️ Warning in ${context}`);
        console.warn('Message:', message);
        if (additionalData) {
            console.warn('Additional Data:', additionalData);
        }
        console.groupEnd();
    }
}
const withErrorHandling = (fn, errorHandler)=>{
    return async (...args)=>{
        try {
            return await fn(...args);
        } catch (error) {
            if (errorHandler) {
                errorHandler(error);
            } else {
                ErrorHandler.handleApiError(error, {
                    logError: true
                });
            }
            return undefined;
        }
    };
};
const createAsyncErrorHandler = (defaultErrorMessage = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERIC_ERROR)=>{
    return (error)=>{
        return ErrorHandler.handleApiError(error, {
            fallbackMessage: defaultErrorMessage,
            logError: true
        });
    };
};
const handleContentError = ContentErrorHandler.handleGenerationError;
const handleAuthError = AuthErrorHandler.handleAuthError;
const handleNetworkError = NetworkErrorHandler.handleNetworkError;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/contentService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ContentService": (()=>ContentService),
    "contentService": (()=>contentService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/validation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/errorHandling.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
// Export singleton instance
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/apiClient.ts [app-client] (ecmascript)");
;
;
;
class ContentService {
    apiClient;
    constructor(apiClient){
        this.apiClient = apiClient;
    }
    // New backend API methods with error handling
    async generateTweet(request) {
        try {
            this.validateTweetRequest(request);
            return await this.apiClient.post('/content/generate-tweet', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async generateThread(request) {
        try {
            this.validateThreadRequest(request);
            return await this.apiClient.post('/content/generate-thread', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async generateReply(request) {
        try {
            this.validateReplyRequest(request);
            return await this.apiClient.post('/content/generate-reply', request);
        } catch (error) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
        }
    }
    async getContentHistory(skip = 0, limit = 50) {
        const params = new URLSearchParams({
            skip: skip.toString(),
            limit: limit.toString()
        });
        return this.apiClient.get(`/content/history?${params}`);
    }
    // Legacy methods for backward compatibility
    async generateContent(request) {
        this.validateContentRequest(request);
        // Convert legacy request to new format
        const newRequest = {
            topic: request.topic,
            style: request.tone,
            user_context: `Length: ${request.length}, Include hashtags: ${request.includeHashtags}, Include emojis: ${request.includeEmojis}`
        };
        const response = await this.generateTweet(newRequest);
        // Convert response to legacy format
        return {
            id: Date.now().toString(),
            content: response.content || '',
            hashtags: [],
            createdAt: new Date().toISOString(),
            status: 'draft'
        };
    }
    // TODO: Implement these methods when backend endpoints are ready
    // For now, these are removed to eliminate dead code
    //
    // Future implementations:
    // - getContent(): Use history endpoint with filtering
    // - getContentById(): Implement backend endpoint for single content retrieval
    // - updateContent(): Implement backend endpoint for content updates
    // - scheduleContent(): Use scheduled posts API
    // - deleteContent(): Implement backend endpoint for content deletion
    // - publishContent(): Use Twitter posting API
    // Private validation methods using centralized validation utilities
    validateTweetRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
    }
    validateThreadRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
        if (request.num_tweets) {
            if (request.num_tweets < 2) {
                throw new Error('Thread must contain at least 2 tweets');
            }
            if (request.num_tweets > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS) {
                throw new Error(`Thread cannot exceed ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_THREAD_TWEETS} tweets`);
            }
        }
    }
    validateReplyRequest(request) {
        const requiredValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationUtils"].validateRequired(request.original_tweet, 'Original tweet');
        if (!requiredValidation.isValid) {
            throw new Error(requiredValidation.error);
        }
        const lengthValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationUtils"].validateLength(request.original_tweet, 1, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TWITTER_CONSTANTS"].MAX_TWEET_LENGTH);
        if (!lengthValidation.isValid) {
            throw new Error(lengthValidation.error);
        }
    }
    validateContentRequest(request) {
        const topicValidation = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(request.topic);
        if (!topicValidation.isValid) {
            throw new Error(topicValidation.error);
        }
    }
    // Removed unused validation methods to eliminate dead code
    buildQueryParams(filters) {
        if (!filters) return '';
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value])=>{
            if (value !== undefined && value !== null) {
                params.append(key, String(value));
            }
        });
        const queryString = params.toString();
        return queryString ? `?${queryString}` : '';
    }
}
;
const contentService = new ContentService(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"]);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/useErrorHandling.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Custom hooks for error handling following SOLID and KISS principles.
 * Provides consistent error handling across components.
 */ __turbopack_context__.s({
    "useAsyncOperation": (()=>useAsyncOperation),
    "useAuthErrorHandling": (()=>useAuthErrorHandling),
    "useContentErrorHandling": (()=>useContentErrorHandling),
    "useErrorBoundary": (()=>useErrorBoundary),
    "useErrorHandler": (()=>useErrorHandler),
    "useErrorHandling": (()=>useErrorHandling),
    "useNetworkErrorHandling": (()=>useNetworkErrorHandling),
    "useRetryableOperation": (()=>useRetryableOperation),
    "useToastNotifications": (()=>useToastNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/errorHandling.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature();
;
;
;
function useErrorHandling(maxRetries = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY_ATTEMPTS) {
    _s();
    const [errorState, setErrorState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        error: null,
        isError: false,
        errorId: null
    });
    const [retryState, setRetryState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isRetrying: false,
        retryCount: 0,
        maxRetries,
        canRetry: true
    });
    const errorIdRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    // Handle error with optional retry capability
    const handleError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorHandling.useCallback[handleError]": (error, context)=>{
            const apiError = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ErrorHandler"].handleApiError(error, {
                logError: true,
                fallbackMessage: `Error in ${context || 'operation'}`
            });
            const errorId = `error_${++errorIdRef.current}_${Date.now()}`;
            setErrorState({
                error: apiError,
                isError: true,
                errorId
            });
            // Determine if error is retryable
            const canRetry = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RetryHandler"].isRetryableError(error) && retryState.retryCount < maxRetries;
            setRetryState({
                "useErrorHandling.useCallback[handleError]": (prev)=>({
                        ...prev,
                        canRetry,
                        isRetrying: false
                    })
            }["useErrorHandling.useCallback[handleError]"]);
            return apiError;
        }
    }["useErrorHandling.useCallback[handleError]"], [
        maxRetries,
        retryState.retryCount
    ]);
    // Clear error state
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorHandling.useCallback[clearError]": ()=>{
            setErrorState({
                error: null,
                isError: false,
                errorId: null
            });
            setRetryState({
                "useErrorHandling.useCallback[clearError]": (prev)=>({
                        ...prev,
                        isRetrying: false,
                        retryCount: 0,
                        canRetry: true
                    })
            }["useErrorHandling.useCallback[clearError]"]);
        }
    }["useErrorHandling.useCallback[clearError]"], []);
    // Retry failed operation
    const retry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorHandling.useCallback[retry]": async (operation)=>{
            if (!retryState.canRetry || retryState.isRetrying) {
                return;
            }
            setRetryState({
                "useErrorHandling.useCallback[retry]": (prev)=>({
                        ...prev,
                        isRetrying: true,
                        retryCount: prev.retryCount + 1
                    })
            }["useErrorHandling.useCallback[retry]"]);
            try {
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RetryHandler"].retryOperation(operation, maxRetries - retryState.retryCount, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].RETRY_DELAY);
                clearError();
                return result;
            } catch (error) {
                handleError(error, 'retry operation');
                throw error;
            }
        }
    }["useErrorHandling.useCallback[retry]"], [
        retryState,
        maxRetries,
        handleError,
        clearError
    ]);
    // Execute operation with error handling
    const executeWithErrorHandling = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorHandling.useCallback[executeWithErrorHandling]": async (operation, context)=>{
            try {
                clearError();
                return await operation();
            } catch (error) {
                handleError(error, context);
                return undefined;
            }
        }
    }["useErrorHandling.useCallback[executeWithErrorHandling]"], [
        handleError,
        clearError
    ]);
    return {
        errorState,
        retryState,
        handleError,
        clearError,
        retry,
        executeWithErrorHandling
    };
}
_s(useErrorHandling, "Dsr4gABvnijWs4thPN6iP0a/HOI=");
function useContentErrorHandling() {
    _s1();
    const { handleError: baseHandleError, ...baseHooks } = useErrorHandling();
    const handleContentError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentErrorHandling.useCallback[handleContentError]": (error, operation)=>{
            const apiError = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleGenerationError(error);
            return baseHandleError(apiError, `content ${operation || 'operation'}`);
        }
    }["useContentErrorHandling.useCallback[handleContentError]"], [
        baseHandleError
    ]);
    const handleValidationError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentErrorHandling.useCallback[handleValidationError]": (errors)=>{
            const apiError = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentErrorHandler"].handleContentValidationError(errors);
            return baseHandleError(apiError, 'content validation');
        }
    }["useContentErrorHandling.useCallback[handleValidationError]"], [
        baseHandleError
    ]);
    return {
        ...baseHooks,
        handleContentError,
        handleValidationError
    };
}
_s1(useContentErrorHandling, "ILEXAZuCAijDAmLvjLATFiV4dPE=", false, function() {
    return [
        useErrorHandling
    ];
});
function useAuthErrorHandling() {
    _s2();
    const { handleError: baseHandleError, ...baseHooks } = useErrorHandling();
    const handleAuthError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthErrorHandling.useCallback[handleAuthError]": (error)=>{
            const apiError = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthErrorHandler"].handleAuthError(error);
            return baseHandleError(apiError, 'authentication');
        }
    }["useAuthErrorHandling.useCallback[handleAuthError]"], [
        baseHandleError
    ]);
    const handleTokenExpiration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAuthErrorHandling.useCallback[handleTokenExpiration]": ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthErrorHandler"].handleTokenExpiration();
            // Clear error state since token expiration is handled
            baseHooks.clearError();
        }
    }["useAuthErrorHandling.useCallback[handleTokenExpiration]"], [
        baseHooks
    ]);
    return {
        ...baseHooks,
        handleAuthError,
        handleTokenExpiration
    };
}
_s2(useAuthErrorHandling, "/ZkOQVFfXGEyZhLNqoPACHoxH/M=", false, function() {
    return [
        useErrorHandling
    ];
});
function useNetworkErrorHandling() {
    _s3();
    const { handleError: baseHandleError, ...baseHooks } = useErrorHandling();
    const handleNetworkError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNetworkErrorHandling.useCallback[handleNetworkError]": (error)=>{
            const apiError = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NetworkErrorHandler"].handleNetworkError(error);
            return baseHandleError(apiError, 'network operation');
        }
    }["useNetworkErrorHandling.useCallback[handleNetworkError]"], [
        baseHandleError
    ]);
    const handleRateLimitError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNetworkErrorHandling.useCallback[handleRateLimitError]": (error)=>{
            const apiError = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NetworkErrorHandler"].handleRateLimitError(error);
            return baseHandleError(apiError, 'rate limited operation');
        }
    }["useNetworkErrorHandling.useCallback[handleRateLimitError]"], [
        baseHandleError
    ]);
    return {
        ...baseHooks,
        handleNetworkError,
        handleRateLimitError
    };
}
_s3(useNetworkErrorHandling, "H3js/8jorYgkBtOWbNRacFEcftA=", false, function() {
    return [
        useErrorHandling
    ];
});
function useAsyncOperation() {
    _s4();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const { errorState, handleError, clearError } = useErrorHandling();
    const execute = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAsyncOperation.useCallback[execute]": async (operation, options)=>{
            setIsLoading(true);
            clearError();
            try {
                const result = await operation();
                setData(result);
                options?.onSuccess?.(result);
                return result;
            } catch (error) {
                const apiError = handleError(error, options?.context);
                options?.onError?.(apiError);
                throw apiError;
            } finally{
                setIsLoading(false);
            }
        }
    }["useAsyncOperation.useCallback[execute]"], [
        handleError,
        clearError
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAsyncOperation.useCallback[reset]": ()=>{
            setData(null);
            setIsLoading(false);
            clearError();
        }
    }["useAsyncOperation.useCallback[reset]"], [
        clearError
    ]);
    return {
        isLoading,
        data,
        errorState,
        execute,
        reset
    };
}
_s4(useAsyncOperation, "0Tr3bHEIT2YZlp55dieV259kalM=", false, function() {
    return [
        useErrorHandling
    ];
});
function useErrorBoundary() {
    _s5();
    const [hasError, setHasError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const resetErrorBoundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorBoundary.useCallback[resetErrorBoundary]": ()=>{
            setHasError(false);
            setError(null);
        }
    }["useErrorBoundary.useCallback[resetErrorBoundary]"], []);
    const captureError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorBoundary.useCallback[captureError]": (error, errorInfo)=>{
            setHasError(true);
            setError(error);
            // Log error for debugging
            console.error('Error Boundary caught an error:', error, errorInfo);
        // Here you could integrate with error reporting service
        // errorReportingService.captureException(error, errorInfo);
        }
    }["useErrorBoundary.useCallback[captureError]"], []);
    return {
        hasError,
        error,
        resetErrorBoundary,
        captureError
    };
}
_s5(useErrorBoundary, "41dEN2qILreKt6kSh/uJH96g37U=");
function useErrorHandler(options = {}) {
    _s6();
    const { maxHistorySize = 10 } = options;
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [errorHistory, setErrorHistory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const errorIdRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const handleError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorHandler.useCallback[handleError]": (error, context)=>{
            const message = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ErrorHandler"].getErrorMessage(error);
            const errorId = `error_${++errorIdRef.current}_${Date.now()}`;
            // Process error through ErrorHandler
            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ErrorHandler"].handleError(error, context);
            setError(message);
            const errorItem = {
                id: errorId,
                message,
                timestamp: Date.now(),
                context
            };
            setErrorHistory({
                "useErrorHandler.useCallback[handleError]": (prev)=>{
                    const newHistory = [
                        errorItem,
                        ...prev
                    ];
                    return newHistory.slice(0, maxHistorySize);
                }
            }["useErrorHandler.useCallback[handleError]"]);
        }
    }["useErrorHandler.useCallback[handleError]"], [
        maxHistorySize
    ]);
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorHandler.useCallback[clearError]": ()=>{
            setError(null);
        }
    }["useErrorHandler.useCallback[clearError]"], []);
    const clearAllErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorHandler.useCallback[clearAllErrors]": ()=>{
            setError(null);
            setErrorHistory([]);
        }
    }["useErrorHandler.useCallback[clearAllErrors]"], []);
    const getErrorById = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useErrorHandler.useCallback[getErrorById]": (id)=>{
            return errorHistory.find({
                "useErrorHandler.useCallback[getErrorById]": (item)=>item.id === id
            }["useErrorHandler.useCallback[getErrorById]"]);
        }
    }["useErrorHandler.useCallback[getErrorById]"], [
        errorHistory
    ]);
    return {
        error,
        hasError: error !== null,
        errorHistory,
        handleError,
        clearError,
        clearAllErrors,
        getErrorById
    };
}
_s6(useErrorHandler, "xPk+0Qi2reWV2gE5Fng/YySuTlQ=");
function useRetryableOperation(operation, options = {}) {
    _s7();
    const { maxRetries = 3, retryDelay = 1000 } = options;
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [retryCount, setRetryCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [canRetry, setCanRetry] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const execute = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRetryableOperation.useCallback[execute]": async (...args)=>{
            setIsLoading(true);
            setError(null);
            let currentRetryCount = 0;
            while(currentRetryCount <= maxRetries){
                try {
                    const result = await operation(...args);
                    setIsLoading(false);
                    setRetryCount(currentRetryCount);
                    return result;
                } catch (err) {
                    const isRetryable = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ErrorHandler"].isRetryableError(err);
                    if (!isRetryable || currentRetryCount >= maxRetries) {
                        const message = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$errorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ErrorHandler"].getErrorMessage(err);
                        setError(message);
                        setIsLoading(false);
                        setRetryCount(currentRetryCount);
                        setCanRetry(false);
                        throw err;
                    }
                    currentRetryCount++;
                    setRetryCount(currentRetryCount);
                    if (currentRetryCount <= maxRetries) {
                        await new Promise({
                            "useRetryableOperation.useCallback[execute]": (resolve)=>setTimeout(resolve, retryDelay)
                        }["useRetryableOperation.useCallback[execute]"]);
                    }
                }
            }
            throw new Error('Max retries exceeded');
        }
    }["useRetryableOperation.useCallback[execute]"], [
        operation,
        maxRetries,
        retryDelay
    ]);
    const retry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRetryableOperation.useCallback[retry]": async (...args)=>{
            setRetryCount({
                "useRetryableOperation.useCallback[retry]": (prev)=>prev + 1
            }["useRetryableOperation.useCallback[retry]"]);
            return execute(...args);
        }
    }["useRetryableOperation.useCallback[retry]"], [
        execute
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRetryableOperation.useCallback[reset]": ()=>{
            setError(null);
            setRetryCount(0);
            setCanRetry(true);
            setIsLoading(false);
        }
    }["useRetryableOperation.useCallback[reset]"], []);
    // Helper methods for testing
    const setError_ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRetryableOperation.useCallback[setError_]": (errorMessage)=>{
            setError(errorMessage);
        }
    }["useRetryableOperation.useCallback[setError_]"], []);
    const setRetryCount_ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRetryableOperation.useCallback[setRetryCount_]": (count)=>{
            setRetryCount(count);
        }
    }["useRetryableOperation.useCallback[setRetryCount_]"], []);
    return {
        isLoading,
        error,
        retryCount,
        canRetry,
        execute,
        retry,
        reset,
        // Expose setters for testing
        setError: setError_,
        setRetryCount: setRetryCount_
    };
}
_s7(useRetryableOperation, "tqtzeILgEE0EfdSzKlau7yZMgEE=");
function useToastNotifications() {
    _s8();
    const [notifications, setNotifications] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const notificationIdRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const removeNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useToastNotifications.useCallback[removeNotification]": (id)=>{
            setNotifications({
                "useToastNotifications.useCallback[removeNotification]": (prev)=>prev.filter({
                        "useToastNotifications.useCallback[removeNotification]": (notification)=>notification.id !== id
                    }["useToastNotifications.useCallback[removeNotification]"])
            }["useToastNotifications.useCallback[removeNotification]"]);
        }
    }["useToastNotifications.useCallback[removeNotification]"], []);
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useToastNotifications.useCallback[addNotification]": (notification)=>{
            const id = `toast_${++notificationIdRef.current}_${Date.now()}`;
            const newNotification = {
                id,
                ...notification
            };
            setNotifications({
                "useToastNotifications.useCallback[addNotification]": (prev)=>[
                        ...prev,
                        newNotification
                    ]
            }["useToastNotifications.useCallback[addNotification]"]);
            // Auto-remove after duration
            if (notification.duration) {
                setTimeout({
                    "useToastNotifications.useCallback[addNotification]": ()=>{
                        removeNotification(id);
                    }
                }["useToastNotifications.useCallback[addNotification]"], notification.duration);
            }
        }
    }["useToastNotifications.useCallback[addNotification]"], [
        removeNotification
    ]);
    const clearAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useToastNotifications.useCallback[clearAll]": ()=>{
            setNotifications([]);
        }
    }["useToastNotifications.useCallback[clearAll]"], []);
    return {
        notifications,
        addNotification,
        removeNotification,
        clearAll
    };
}
_s8(useToastNotifications, "roCdiQ8OC/hI5TVsKBMcXfelV/w=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/useValidation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Custom hooks for validation following SOLID and KISS principles.
 * Provides reusable validation logic across components.
 */ __turbopack_context__.s({
    "useContentValidation": (()=>useContentValidation),
    "useFieldValidation": (()=>useFieldValidation),
    "useFormValidation": (()=>useFormValidation),
    "useValidationSchemas": (()=>useValidationSchemas)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/validation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
;
function useFormValidation(initialData, validationSchema, options = {}) {
    _s();
    const { debounceDelay = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UI_CONSTANTS"].DEBOUNCE_DELAY, validateOnChange = true, validateOnBlur = true } = options;
    const [data, setData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialData);
    const [validationState, setValidationState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        errors: {},
        isValid: true,
        isValidating: false,
        hasValidated: false
    });
    // Debounced validation function
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedValidate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(({
        "useFormValidation.useCallback[debouncedValidate]": ()=>{
            let timeoutId;
            return ({
                "useFormValidation.useCallback[debouncedValidate]": (dataToValidate)=>{
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout({
                        "useFormValidation.useCallback[debouncedValidate]": ()=>{
                            setValidationState({
                                "useFormValidation.useCallback[debouncedValidate]": (prev)=>({
                                        ...prev,
                                        isValidating: true
                                    })
                            }["useFormValidation.useCallback[debouncedValidate]"]);
                            const errors = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormValidation"].validateFormData(dataToValidate, validationSchema);
                            const hasErrors = Object.values(errors).some({
                                "useFormValidation.useCallback[debouncedValidate].hasErrors": (error)=>error !== undefined
                            }["useFormValidation.useCallback[debouncedValidate].hasErrors"]);
                            setValidationState({
                                errors: errors,
                                isValid: !hasErrors,
                                isValidating: false,
                                hasValidated: true
                            });
                        }
                    }["useFormValidation.useCallback[debouncedValidate]"], debounceDelay);
                }
            })["useFormValidation.useCallback[debouncedValidate]"];
        }
    })["useFormValidation.useCallback[debouncedValidate]"](), [
        validationSchema,
        debounceDelay
    ]);
    // Validate specific field
    const validateField = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormValidation.useCallback[validateField]": (fieldName, value)=>{
            const validator = validationSchema[fieldName];
            if (!validator) return;
            const result = validator(value);
            setValidationState({
                "useFormValidation.useCallback[validateField]": (prev)=>({
                        ...prev,
                        errors: {
                            ...prev.errors,
                            [fieldName]: result.isValid ? '' : result.error || ''
                        },
                        hasValidated: true
                    })
            }["useFormValidation.useCallback[validateField]"]);
        }
    }["useFormValidation.useCallback[validateField]"], [
        validationSchema
    ]);
    // Update field value
    const updateField = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormValidation.useCallback[updateField]": (fieldName, value)=>{
            const newData = {
                ...data,
                [fieldName]: value
            };
            setData(newData);
            if (validateOnChange) {
                if (debounceDelay > 0) {
                    debouncedValidate(newData);
                } else {
                    validateField(fieldName, value);
                }
            }
        }
    }["useFormValidation.useCallback[updateField]"], [
        data,
        validateOnChange,
        debounceDelay,
        debouncedValidate,
        validateField
    ]);
    // Handle field blur
    const handleFieldBlur = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormValidation.useCallback[handleFieldBlur]": (fieldName)=>{
            if (validateOnBlur) {
                validateField(fieldName, data[fieldName]);
            }
        }
    }["useFormValidation.useCallback[handleFieldBlur]"], [
        validateOnBlur,
        validateField,
        data
    ]);
    // Validate all fields
    const validateAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormValidation.useCallback[validateAll]": ()=>{
            const errors = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormValidation"].validateFormData(data, validationSchema);
            const hasErrors = Object.values(errors).some({
                "useFormValidation.useCallback[validateAll].hasErrors": (error)=>error !== undefined
            }["useFormValidation.useCallback[validateAll].hasErrors"]);
            setValidationState({
                errors: errors,
                isValid: !hasErrors,
                isValidating: false,
                hasValidated: true
            });
            return !hasErrors;
        }
    }["useFormValidation.useCallback[validateAll]"], [
        data,
        validationSchema
    ]);
    // Reset validation state
    const resetValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormValidation.useCallback[resetValidation]": ()=>{
            setValidationState({
                errors: {},
                isValid: true,
                isValidating: false,
                hasValidated: false
            });
        }
    }["useFormValidation.useCallback[resetValidation]"], []);
    // Reset form data
    const resetForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFormValidation.useCallback[resetForm]": ()=>{
            setData(initialData);
            resetValidation();
        }
    }["useFormValidation.useCallback[resetForm]"], [
        initialData,
        resetValidation
    ]);
    return {
        data,
        validationState,
        updateField,
        handleFieldBlur,
        validateField,
        validateAll,
        resetValidation,
        resetForm,
        setData
    };
}
_s(useFormValidation, "KXo5NF0DSrE58ZkHolUI0yLdSoE=");
function useContentValidation() {
    _s1();
    const [validationResults, setValidationResults] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const validateTopic = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentValidation.useCallback[validateTopic]": (topic)=>{
            const result = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].validateTopic(topic);
            setValidationResults({
                "useContentValidation.useCallback[validateTopic]": (prev)=>({
                        ...prev,
                        topic: result
                    })
            }["useContentValidation.useCallback[validateTopic]"]);
            return result;
        }
    }["useContentValidation.useCallback[validateTopic]"], []);
    const validateContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentValidation.useCallback[validateContent]": (content)=>{
            const result = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].validateContent(content);
            setValidationResults({
                "useContentValidation.useCallback[validateContent]": (prev)=>({
                        ...prev,
                        content: result
                    })
            }["useContentValidation.useCallback[validateContent]"]);
            return result;
        }
    }["useContentValidation.useCallback[validateContent]"], []);
    const getContentStats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentValidation.useCallback[getContentStats]": (content)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentValidation"].getContentStats(content);
        }
    }["useContentValidation.useCallback[getContentStats]"], []);
    const clearValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentValidation.useCallback[clearValidation]": (field)=>{
            if (field) {
                setValidationResults({
                    "useContentValidation.useCallback[clearValidation]": (prev)=>{
                        const newResults = {
                            ...prev
                        };
                        delete newResults[field];
                        return newResults;
                    }
                }["useContentValidation.useCallback[clearValidation]"]);
            } else {
                setValidationResults({});
            }
        }
    }["useContentValidation.useCallback[clearValidation]"], []);
    return {
        validationResults,
        validateTopic,
        validateContent,
        getContentStats,
        clearValidation
    };
}
_s1(useContentValidation, "FXgpevAKtrX2kaJLuWzfoinSHdM=");
function useFieldValidation(validator, debounceDelay = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UI_CONSTANTS"].DEBOUNCE_DELAY) {
    _s2();
    const [result, setResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isValid: true
    });
    const [isValidating, setIsValidating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedValidate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])(({
        "useFieldValidation.useCallback[debouncedValidate]": ()=>{
            let timeoutId;
            return ({
                "useFieldValidation.useCallback[debouncedValidate]": (value)=>{
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout({
                        "useFieldValidation.useCallback[debouncedValidate]": ()=>{
                            setIsValidating(true);
                            const validationResult = validator(value);
                            setResult(validationResult);
                            setIsValidating(false);
                        }
                    }["useFieldValidation.useCallback[debouncedValidate]"], debounceDelay);
                }
            })["useFieldValidation.useCallback[debouncedValidate]"];
        }
    })["useFieldValidation.useCallback[debouncedValidate]"](), [
        validator,
        debounceDelay
    ]);
    const validate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFieldValidation.useCallback[validate]": (value)=>{
            if (debounceDelay > 0) {
                debouncedValidate(value);
            } else {
                const validationResult = validator(value);
                setResult(validationResult);
            }
        }
    }["useFieldValidation.useCallback[validate]"], [
        validator,
        debounceDelay,
        debouncedValidate
    ]);
    const clearValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useFieldValidation.useCallback[clearValidation]": ()=>{
            setResult({
                isValid: true
            });
            setIsValidating(false);
        }
    }["useFieldValidation.useCallback[clearValidation]"], []);
    return {
        result,
        isValidating,
        validate,
        clearValidation
    };
}
_s2(useFieldValidation, "EEnpNqniV5VHi05X8zpf7OwAVO0=");
function useValidationSchemas() {
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useValidationSchemas.useMemo": ()=>({
                contentGeneration: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationSchemas"].contentGeneration,
                userRegistration: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationSchemas"].userRegistration,
                userLogin: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$validation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValidationSchemas"].userLogin
            })
    }["useValidationSchemas.useMemo"], []);
}
_s3(useValidationSchemas, "nwk+m61qLgjDVUp4IGV/072DDN4=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/hooks/useContentGeneration.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Enhanced content generation hook following SOLID and KISS principles.
 * Integrates validation and error handling for better user experience.
 */ __turbopack_context__.s({
    "useContentGeneration": (()=>useContentGeneration)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$contentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/contentService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useErrorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/hooks/useErrorHandling.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/hooks/useValidation.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
function useContentGeneration(options = {}) {
    _s();
    const { validateBeforeGeneration = true, onSuccess, onError } = options;
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isLoading: false,
        content: null,
        isValidating: false,
        validationErrors: {}
    });
    const { errorState, handleContentError, handleValidationError, clearError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useErrorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContentErrorHandling"])();
    const { validateTopic, getContentStats, clearValidation } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContentValidation"])();
    // Validate content request
    const validateRequest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentGeneration.useCallback[validateRequest]": (request)=>{
            setState({
                "useContentGeneration.useCallback[validateRequest]": (prev)=>({
                        ...prev,
                        isValidating: true,
                        validationErrors: {}
                    })
            }["useContentGeneration.useCallback[validateRequest]"]);
            const errors = {};
            // Validate topic
            const topicResult = validateTopic(request.topic);
            if (!topicResult.isValid && topicResult.error) {
                errors.topic = topicResult.error;
            }
            // Validate tone if provided
            if (request.tone && ![
                'professional',
                'casual',
                'humorous',
                'inspirational'
            ].includes(request.tone)) {
                errors.tone = 'Invalid content tone';
            }
            // Validate length if provided
            if (request.length && ![
                'short',
                'medium',
                'long'
            ].includes(request.length)) {
                errors.length = 'Invalid content length';
            }
            const hasErrors = Object.keys(errors).length > 0;
            setState({
                "useContentGeneration.useCallback[validateRequest]": (prev)=>({
                        ...prev,
                        isValidating: false,
                        validationErrors: errors
                    })
            }["useContentGeneration.useCallback[validateRequest]"]);
            if (hasErrors) {
                handleValidationError(errors);
            }
            return !hasErrors;
        }
    }["useContentGeneration.useCallback[validateRequest]"], [
        validateTopic,
        handleValidationError
    ]);
    // Generate content with validation and error handling
    const generateContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentGeneration.useCallback[generateContent]": async (request)=>{
            // Clear previous errors
            clearError();
            clearValidation();
            // Validate request if enabled
            if (validateBeforeGeneration && !validateRequest(request)) {
                return undefined;
            }
            setState({
                "useContentGeneration.useCallback[generateContent]": (prev)=>({
                        ...prev,
                        isLoading: true
                    })
            }["useContentGeneration.useCallback[generateContent]"]);
            try {
                const content = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$contentService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["contentService"].generateContent(request);
                setState({
                    "useContentGeneration.useCallback[generateContent]": (prev)=>({
                            ...prev,
                            content,
                            isLoading: false
                        })
                }["useContentGeneration.useCallback[generateContent]"]);
                onSuccess?.(content);
                return content;
            } catch (error) {
                setState({
                    "useContentGeneration.useCallback[generateContent]": (prev)=>({
                            ...prev,
                            isLoading: false
                        })
                }["useContentGeneration.useCallback[generateContent]"]);
                const apiError = handleContentError(error, 'generation');
                onError?.(apiError);
                return undefined;
            }
        }
    }["useContentGeneration.useCallback[generateContent]"], [
        validateBeforeGeneration,
        validateRequest,
        handleContentError,
        clearError,
        clearValidation,
        onSuccess,
        onError
    ]);
    // Generate tweet specifically
    const generateTweet = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentGeneration.useCallback[generateTweet]": async (topic, tone, length)=>{
            return generateContent({
                topic,
                tone: tone || 'professional',
                length: length || 'medium',
                includeHashtags: true,
                includeEmojis: false
            });
        }
    }["useContentGeneration.useCallback[generateTweet]"], [
        generateContent
    ]);
    // Generate thread specifically
    const generateThread = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentGeneration.useCallback[generateThread]": async (topic, tone)=>{
            return generateContent({
                topic,
                tone: tone || 'professional',
                length: 'long',
                includeHashtags: true,
                includeEmojis: false
            });
        }
    }["useContentGeneration.useCallback[generateThread]"], [
        generateContent
    ]);
    // Generate reply specifically
    const generateReply = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentGeneration.useCallback[generateReply]": async (topic, tone)=>{
            return generateContent({
                topic,
                tone: tone || 'professional',
                length: 'short',
                includeHashtags: false,
                includeEmojis: false
            });
        }
    }["useContentGeneration.useCallback[generateReply]"], [
        generateContent
    ]);
    // Clear all state
    const clearAll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentGeneration.useCallback[clearAll]": ()=>{
            setState({
                isLoading: false,
                content: null,
                isValidating: false,
                validationErrors: {}
            });
            clearError();
            clearValidation();
        }
    }["useContentGeneration.useCallback[clearAll]"], [
        clearError,
        clearValidation
    ]);
    // Get content statistics
    const getContentStatistics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useContentGeneration.useCallback[getContentStatistics]": (content)=>{
            return getContentStats(content);
        }
    }["useContentGeneration.useCallback[getContentStatistics]"], [
        getContentStats
    ]);
    return {
        // State
        ...state,
        errorState,
        // Actions
        generateContent,
        generateTweet,
        generateThread,
        generateReply,
        validateRequest,
        clearAll,
        clearError,
        // Utilities
        getContentStatistics
    };
}
_s(useContentGeneration, "pUfpt7GfScklG66SdjB/Xu9YahY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useErrorHandling$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContentErrorHandling"],
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContentValidation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/ui/Button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Button = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref)=>{
    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    const variants = {
        primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
        secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
        outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500',
        ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
        destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
    };
    const sizes = {
        sm: 'px-3 py-1.5 text-sm',
        md: 'px-4 py-2 text-sm',
        lg: 'px-6 py-3 text-base'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(baseClasses, variants[variant], sizes[size], className),
        disabled: disabled || isLoading,
        ...props,
        children: [
            isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "w-4 h-4 mr-2 animate-spin",
                fill: "none",
                viewBox: "0 0 24 24",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                        className: "opacity-25",
                        cx: "12",
                        cy: "12",
                        r: "10",
                        stroke: "currentColor",
                        strokeWidth: "4"
                    }, void 0, false, {
                        fileName: "[project]/app/components/ui/Button.tsx",
                        lineNumber: 38,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        className: "opacity-75",
                        fill: "currentColor",
                        d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    }, void 0, false, {
                        fileName: "[project]/app/components/ui/Button.tsx",
                        lineNumber: 39,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/ui/Button.tsx",
                lineNumber: 37,
                columnNumber: 11
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/ui/Button.tsx",
        lineNumber: 30,
        columnNumber: 7
    }, this);
});
_c1 = Button;
Button.displayName = 'Button';
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Button$forwardRef");
__turbopack_context__.k.register(_c1, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/ui/Input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Input = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, label, error, helperText, id, ...props }, ref)=>{
    const inputId = id || label?.toLowerCase().replace(/\s+/g, '-');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-2",
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: inputId,
                className: "block text-sm font-medium text-gray-700",
                children: label
            }, void 0, false, {
                fileName: "[project]/app/components/ui/Input.tsx",
                lineNumber: 17,
                columnNumber: 11
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                ref: ref,
                id: inputId,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-offset-0 transition-colors', error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500', className),
                ...props
            }, void 0, false, {
                fileName: "[project]/app/components/ui/Input.tsx",
                lineNumber: 21,
                columnNumber: 9
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-red-600",
                children: error
            }, void 0, false, {
                fileName: "[project]/app/components/ui/Input.tsx",
                lineNumber: 34,
                columnNumber: 11
            }, this),
            helperText && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-gray-500",
                children: helperText
            }, void 0, false, {
                fileName: "[project]/app/components/ui/Input.tsx",
                lineNumber: 37,
                columnNumber: 11
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/ui/Input.tsx",
        lineNumber: 15,
        columnNumber: 7
    }, this);
});
_c1 = Input;
Input.displayName = 'Input';
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Input$forwardRef");
__turbopack_context__.k.register(_c1, "Input");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/content/ContentForm.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/ui/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/ui/Input.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const ContentForm = ({ onSubmit, isLoading, error, initialData })=>{
    _s();
    const [formData, setFormData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        topic: initialData?.topic || '',
        tone: initialData?.tone || 'professional',
        length: initialData?.length || 'medium',
        includeHashtags: initialData?.includeHashtags ?? true,
        includeEmojis: initialData?.includeEmojis ?? false
    });
    const [validationErrors, setValidationErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const validateTopic = (topic)=>{
        if (!topic.trim()) {
            return 'Topic is required';
        }
        if (topic.length < __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_TOPIC_LENGTH) {
            return `Topic must be at least ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MIN_TOPIC_LENGTH} characters`;
        }
        if (topic.length > __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH) {
            return `Topic cannot exceed ${__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH} characters`;
        }
        return null;
    };
    const handleSubmit = async (e)=>{
        e.preventDefault();
        // Validate form
        const topicError = validateTopic(formData.topic);
        if (topicError) {
            setValidationErrors({
                topic: topicError
            });
            return;
        }
        setValidationErrors({});
        await onSubmit(formData);
    };
    const handleInputChange = (field, value)=>{
        setFormData((prev)=>({
                ...prev,
                [field]: value
            }));
        // Clear validation error for this field
        if (validationErrors[field]) {
            setValidationErrors((prev)=>{
                const newErrors = {
                    ...prev
                };
                delete newErrors[field];
                return newErrors;
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
        onSubmit: handleSubmit,
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                label: "Topic",
                value: formData.topic,
                onChange: (e)=>handleInputChange('topic', e.target.value),
                placeholder: "What would you like to tweet about?",
                error: validationErrors.topic,
                required: true,
                maxLength: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH
            }, void 0, false, {
                fileName: "[project]/app/components/content/ContentForm.tsx",
                lineNumber: 73,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-sm text-gray-500 text-right",
                children: [
                    formData.topic.length,
                    "/",
                    __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VALIDATION_RULES"].MAX_TOPIC_LENGTH,
                    " characters"
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/content/ContentForm.tsx",
                lineNumber: 84,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 mb-2",
                        children: "Tone"
                    }, void 0, false, {
                        fileName: "[project]/app/components/content/ContentForm.tsx",
                        lineNumber: 90,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-2 md:grid-cols-4 gap-2",
                        children: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_TONES"].map((tone)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                type: "button",
                                variant: formData.tone === tone.value ? 'primary' : 'outline',
                                size: "sm",
                                onClick: ()=>handleInputChange('tone', tone.value),
                                children: tone.label
                            }, tone.value, false, {
                                fileName: "[project]/app/components/content/ContentForm.tsx",
                                lineNumber: 93,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/components/content/ContentForm.tsx",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/content/ContentForm.tsx",
                lineNumber: 89,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 mb-2",
                        children: "Length"
                    }, void 0, false, {
                        fileName: "[project]/app/components/content/ContentForm.tsx",
                        lineNumber: 108,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-3 gap-2",
                        children: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CONTENT_LENGTHS"].map((length)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                type: "button",
                                variant: formData.length === length.value ? 'primary' : 'outline',
                                size: "sm",
                                onClick: ()=>handleInputChange('length', length.value),
                                children: length.label
                            }, length.value, false, {
                                fileName: "[project]/app/components/content/ContentForm.tsx",
                                lineNumber: 111,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/app/components/content/ContentForm.tsx",
                        lineNumber: 109,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/content/ContentForm.tsx",
                lineNumber: 107,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "checkbox",
                                id: "hashtags",
                                checked: formData.includeHashtags,
                                onChange: (e)=>handleInputChange('includeHashtags', e.target.checked),
                                className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            }, void 0, false, {
                                fileName: "[project]/app/components/content/ContentForm.tsx",
                                lineNumber: 127,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "hashtags",
                                className: "ml-2 text-sm text-gray-700",
                                children: "Include hashtags"
                            }, void 0, false, {
                                fileName: "[project]/app/components/content/ContentForm.tsx",
                                lineNumber: 134,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/content/ContentForm.tsx",
                        lineNumber: 126,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "checkbox",
                                id: "emojis",
                                checked: formData.includeEmojis,
                                onChange: (e)=>handleInputChange('includeEmojis', e.target.checked),
                                className: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            }, void 0, false, {
                                fileName: "[project]/app/components/content/ContentForm.tsx",
                                lineNumber: 139,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "emojis",
                                className: "ml-2 text-sm text-gray-700",
                                children: "Include emojis"
                            }, void 0, false, {
                                fileName: "[project]/app/components/content/ContentForm.tsx",
                                lineNumber: 146,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/content/ContentForm.tsx",
                        lineNumber: 138,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/content/ContentForm.tsx",
                lineNumber: 125,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                type: "submit",
                isLoading: isLoading,
                disabled: !formData.topic.trim(),
                className: "w-full",
                children: isLoading ? 'Generating...' : 'Generate Content'
            }, void 0, false, {
                fileName: "[project]/app/components/content/ContentForm.tsx",
                lineNumber: 152,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-3 bg-red-50 border border-red-200 rounded-md",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-sm text-red-600",
                    children: error
                }, void 0, false, {
                    fileName: "[project]/app/components/content/ContentForm.tsx",
                    lineNumber: 163,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/components/content/ContentForm.tsx",
                lineNumber: 162,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/content/ContentForm.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
};
_s(ContentForm, "c9N2Z4C41T4c8Tyyzr8lczemn5g=");
_c = ContentForm;
const __TURBOPACK__default__export__ = ContentForm;
var _c;
__turbopack_context__.k.register(_c, "ContentForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/ui/Card.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>Card),
    "CardContent": (()=>CardContent),
    "CardHeader": (()=>CardHeader),
    "CardTitle": (()=>CardTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Card = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, variant = 'default', ...props }, ref)=>{
    const variants = {
        default: 'bg-white border border-gray-200 shadow-sm',
        outlined: 'bg-white border border-gray-300',
        elevated: 'bg-white border border-gray-200 shadow-md'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('rounded-lg', variants[variant], className),
        ...props
    }, void 0, false, {
        fileName: "[project]/app/components/ui/Card.tsx",
        lineNumber: 17,
        columnNumber: 7
    }, this);
});
_c1 = Card;
Card.displayName = 'Card';
const CardHeader = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c2 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('p-6 pb-4', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/app/components/ui/Card.tsx",
        lineNumber: 30,
        columnNumber: 5
    }, this));
_c3 = CardHeader;
CardHeader.displayName = 'CardHeader';
const CardContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c4 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('p-6 pt-0', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/app/components/ui/Card.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this));
_c5 = CardContent;
CardContent.displayName = 'CardContent';
const CardTitle = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c6 = ({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-xl font-semibold text-gray-900', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/app/components/ui/Card.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this));
_c7 = CardTitle;
CardTitle.displayName = 'CardTitle';
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;
__turbopack_context__.k.register(_c, "Card$forwardRef");
__turbopack_context__.k.register(_c1, "Card");
__turbopack_context__.k.register(_c2, "CardHeader$forwardRef");
__turbopack_context__.k.register(_c3, "CardHeader");
__turbopack_context__.k.register(_c4, "CardContent$forwardRef");
__turbopack_context__.k.register(_c5, "CardContent");
__turbopack_context__.k.register(_c6, "CardTitle$forwardRef");
__turbopack_context__.k.register(_c7, "CardTitle");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/content/ContentDisplay.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/ui/Button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/ui/Card.tsx [app-client] (ecmascript)");
'use client';
;
;
;
const ContentDisplay = ({ content, onSchedule, onSaveDraft, onEdit, onDelete })=>{
    const formatDate = (dateString)=>{
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };
    const getStatusColor = (status)=>{
        switch(status){
            case 'published':
                return 'bg-green-100 text-green-800';
            case 'scheduled':
                return 'bg-yellow-100 text-yellow-800';
            case 'draft':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between items-start",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                children: "Generated Content"
                            }, void 0, false, {
                                fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                lineNumber: 49,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(content.status)}`,
                                children: content.status.charAt(0).toUpperCase() + content.status.slice(1)
                            }, void 0, false, {
                                fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                lineNumber: 50,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/content/ContentDisplay.tsx",
                        lineNumber: 48,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-500",
                        children: [
                            "Created ",
                            formatDate(content.createdAt)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/components/content/ContentDisplay.tsx",
                        lineNumber: 54,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/content/ContentDisplay.tsx",
                lineNumber: 47,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-4 bg-gray-50 border border-gray-200 rounded-md",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-900 whitespace-pre-wrap",
                                children: content.content
                            }, void 0, false, {
                                fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                lineNumber: 62,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/components/content/ContentDisplay.tsx",
                            lineNumber: 61,
                            columnNumber: 11
                        }, this),
                        content.hashtags && content.hashtags.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "text-sm font-medium text-gray-700 mb-2",
                                    children: "Hashtags:"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                    lineNumber: 67,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-wrap gap-2",
                                    children: content.hashtags.map((hashtag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",
                                            children: [
                                                "#",
                                                hashtag
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                            lineNumber: 70,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                    lineNumber: 68,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/content/ContentDisplay.tsx",
                            lineNumber: 66,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-wrap gap-2",
                            children: [
                                onSchedule && content.status === 'draft' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    size: "sm",
                                    onClick: ()=>onSchedule(content),
                                    children: "Schedule"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                    lineNumber: 83,
                                    columnNumber: 15
                                }, this),
                                onSaveDraft && content.status !== 'draft' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    size: "sm",
                                    variant: "outline",
                                    onClick: ()=>onSaveDraft(content),
                                    children: "Save Draft"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                    lineNumber: 92,
                                    columnNumber: 15
                                }, this),
                                onEdit && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    size: "sm",
                                    variant: "outline",
                                    onClick: ()=>onEdit(content),
                                    children: "Edit"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                    lineNumber: 102,
                                    columnNumber: 15
                                }, this),
                                onDelete && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    size: "sm",
                                    variant: "destructive",
                                    onClick: ()=>onDelete(content.id),
                                    children: "Delete"
                                }, void 0, false, {
                                    fileName: "[project]/app/components/content/ContentDisplay.tsx",
                                    lineNumber: 112,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/components/content/ContentDisplay.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/components/content/ContentDisplay.tsx",
                    lineNumber: 60,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/components/content/ContentDisplay.tsx",
                lineNumber: 59,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/content/ContentDisplay.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
};
_c = ContentDisplay;
const __TURBOPACK__default__export__ = ContentDisplay;
var _c;
__turbopack_context__.k.register(_c, "ContentDisplay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/components/ContentGeneration.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useContentGeneration$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/hooks/useContentGeneration.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$content$2f$ContentForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/content/ContentForm.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$content$2f$ContentDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/content/ContentDisplay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/ui/Card.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const ContentGeneration = ()=>{
    _s();
    const { generateContent, isLoading, errorState } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useContentGeneration$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContentGeneration"])();
    const [generatedContent, setGeneratedContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const handleFormSubmit = async (formData)=>{
        const content = await generateContent(formData);
        if (content) {
            setGeneratedContent(content);
        }
    };
    const handleSchedule = (content)=>{
        // TODO: Implement scheduling logic
        console.log('Schedule content:', content);
    };
    const handleSaveDraft = (content)=>{
        // TODO: Implement save draft logic
        console.log('Save draft:', content);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                            children: "Generate Content"
                        }, void 0, false, {
                            fileName: "[project]/app/components/ContentGeneration.tsx",
                            lineNumber: 35,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/ContentGeneration.tsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$ui$2f$Card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$content$2f$ContentForm$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            onSubmit: handleFormSubmit,
                            isLoading: isLoading,
                            error: errorState.error?.message
                        }, void 0, false, {
                            fileName: "[project]/app/components/ContentGeneration.tsx",
                            lineNumber: 38,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/components/ContentGeneration.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/components/ContentGeneration.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            generatedContent && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$content$2f$ContentDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                content: generatedContent,
                onSchedule: handleSchedule,
                onSaveDraft: handleSaveDraft
            }, void 0, false, {
                fileName: "[project]/app/components/ContentGeneration.tsx",
                lineNumber: 47,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/components/ContentGeneration.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
};
_s(ContentGeneration, "uG1rKEb2YEDB7hdmFoGhu8P+Izo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$hooks$2f$useContentGeneration$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContentGeneration"]
    ];
});
_c = ContentGeneration;
const __TURBOPACK__default__export__ = ContentGeneration;
var _c;
__turbopack_context__.k.register(_c, "ContentGeneration");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=app_66f4c16c._.js.map