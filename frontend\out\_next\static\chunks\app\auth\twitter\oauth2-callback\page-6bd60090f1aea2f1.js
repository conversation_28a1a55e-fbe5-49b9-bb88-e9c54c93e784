(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[999],{2670:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(5155),a=s(2115),o=s(5695),n=s(8794),i=s(3851);function l(){let e=(0,o.useRouter)(),t=(0,o.useSearchParams)(),{refreshUser:s}=(0,n.A)(),[l,c]=(0,a.useState)("loading"),[d,u]=(0,a.useState)(""),[m,h]=(0,a.useState)(!1),[x,g]=(0,a.useState)(null),f=(0,a.useCallback)(async()=>{if(m)return void console.log("⚠️ Callback already processing, skipping...");h(!0);try{var r,a;if(localStorage.getItem("authToken")){console.log("⚠️ User already has a token, redirecting to dashboard..."),c("success"),u("Already authenticated, redirecting..."),setTimeout(()=>e.push("/dashboard"),1e3);return}let o=new URLSearchParams(window.location.search);console.log("=== TWITTER CALLBACK DEBUG ==="),console.log("Full URL:",window.location.href),console.log("All URL params:",Object.fromEntries(o.entries()));let n=t.get("code"),l=t.get("state"),d=t.get("error"),m=t.get("error_description");if(console.log("OAuth params:",{code:n,state:l,error:d,error_description:m}),d){console.error("Twitter OAuth error:",{error:d,error_description:m}),c("error"),u("Twitter authorization failed: ".concat(d," - ").concat(m||"No description")),setTimeout(()=>e.push("/"),3e3);return}if(!n||!l){c("error"),u("Missing OAuth parameters in callback URL"),setTimeout(()=>e.push("/"),3e3);return}let h=sessionStorage.getItem("twitter_oauth2_state"),x=sessionStorage.getItem("twitter_oauth2_code_verifier");if(!h||!x){c("error"),u("OAuth state not found. Please restart the authentication process."),setTimeout(()=>e.push("/"),3e3);return}if(l!==h){c("error"),u("Invalid OAuth state. Possible security issue."),setTimeout(()=>e.push("/"),3e3);return}console.log("\uD83D\uDD04 Calling backend OAuth callback...");let f=await i.y.handleTwitterOAuth2Callback({code:n,state:l,code_verifier:x});console.log("✅ Backend OAuth callback successful:",{hasAccessToken:!!f.access_token,hasUser:!!f.user,userId:null==(r=f.user)?void 0:r.id,username:null==(a=f.user)?void 0:a.username}),sessionStorage.removeItem("twitter_oauth2_state"),sessionStorage.removeItem("twitter_oauth2_code_verifier"),c("success"),u("Successfully logged in with Twitter!"),g(f.user),console.log("\uD83D\uDD04 Refreshing AuthContext..."),await s(),console.log("✅ AuthContext refreshed successfully"),setTimeout(()=>e.push("/dashboard"),1e3)}catch(t){console.error("Callback handler error:",t),sessionStorage.removeItem("twitter_oauth2_state"),sessionStorage.removeItem("twitter_oauth2_code_verifier"),localStorage.removeItem("authToken"),c("error"),u(t instanceof Error?t.message:"Failed to authenticate with Twitter"),t instanceof Error&&console.error("Error details:",{message:t.message,stack:t.stack,name:t.name}),setTimeout(()=>e.push("/login"),3e3)}finally{h(!1)}},[t,e,s,m]);return(0,a.useEffect)(()=>{f()},[f]),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Twitter Authentication"})}),(0,r.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6",children:["loading"===l&&(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Processing Twitter authentication..."})]}),"success"===l&&(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Success!"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:d}),x&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-md p-3 text-left",children:[(0,r.jsx)("p",{className:"text-sm text-gray-700",children:(0,r.jsxs)("strong",{children:["Welcome, ",x.full_name||x.username,"!"]})}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["@",x.twitter_username]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:"Redirecting to dashboard..."})]}),"error"===l&&(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Authentication Failed"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:d}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Redirecting to home page..."})]})]})]})})}function c(){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Twitter Authentication"}),(0,r.jsx)("div",{className:"bg-white shadow-md rounded-lg p-6 mt-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})})]})})})}function d(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(c,{}),children:(0,r.jsx)(l,{})})}},9013:(e,t,s)=>{Promise.resolve().then(s.bind(s,2670))}},e=>{var t=t=>e(e.s=t);e.O(0,[464,794,441,684,358],()=>t(9013)),_N_E=e.O()}]);