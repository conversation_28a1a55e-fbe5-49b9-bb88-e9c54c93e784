(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/lib/constants.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Navigation configuration
__turbopack_context__.s({
    "API_CONFIG": (()=>API_CONFIG),
    "CONTENT_CONSTANTS": (()=>CONTENT_CONSTANTS),
    "CONTENT_LENGTHS": (()=>CONTENT_LENGTHS),
    "CONTENT_MODES": (()=>CONTENT_MODES),
    "CONTENT_TONES": (()=>CONTENT_TONES),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "HTTP_STATUS": (()=>HTTP_STATUS),
    "NAVIGATION_ITEMS": (()=>NAVIGATION_ITEMS),
    "POST_STATUS": (()=>POST_STATUS),
    "REGEX_PATTERNS": (()=>REGEX_PATTERNS),
    "STORAGE_KEYS": (()=>STORAGE_KEYS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TWITTER_CONSTANTS": (()=>TWITTER_CONSTANTS),
    "UI_CONSTANTS": (()=>UI_CONSTANTS),
    "VALIDATION_RULES": (()=>VALIDATION_RULES),
    "getErrorMessageByStatus": (()=>getErrorMessageByStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const NAVIGATION_ITEMS = [
    {
        name: 'Dashboard',
        path: '/dashboard'
    },
    {
        name: 'Content',
        path: '/content'
    },
    {
        name: 'Analytics',
        path: '/analytics'
    },
    {
        name: 'Settings',
        path: '/settings'
    },
    {
        name: 'Auth',
        path: '/auth'
    },
    {
        name: 'Test API',
        path: '/test-connection'
    }
];
const CONTENT_TONES = [
    {
        value: 'engaging',
        label: 'Engaging'
    },
    {
        value: 'professional',
        label: 'Professional'
    },
    {
        value: 'casual',
        label: 'Casual'
    },
    {
        value: 'educational',
        label: 'Educational'
    },
    {
        value: 'humorous',
        label: 'Humorous'
    },
    {
        value: 'informative',
        label: 'Informative'
    },
    {
        value: 'helpful',
        label: 'Helpful'
    }
];
const CONTENT_LENGTHS = [
    {
        value: 'short',
        label: 'Short (1-2 sentences)'
    },
    {
        value: 'medium',
        label: 'Medium (3-5 sentences)'
    },
    {
        value: 'long',
        label: 'Long (6+ sentences)'
    }
];
const API_CONFIG = {
    BASE_URL: ("TURBOPACK compile-time value", "http://localhost:8000/api") || 'http://localhost:8000',
    TIMEOUT: 30000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000
};
const TWITTER_CONSTANTS = {
    MAX_TWEET_LENGTH: 280,
    MAX_THREAD_TWEETS: 25,
    DEFAULT_THREAD_SIZE: 3,
    MAX_HASHTAGS_RECOMMENDED: 3,
    MAX_MENTIONS_RECOMMENDED: 5
};
const CONTENT_CONSTANTS = {
    DEFAULT_STYLE: 'engaging',
    DEFAULT_LANGUAGE: 'en',
    SUPPORTED_STYLES: [
        'engaging',
        'professional',
        'casual',
        'educational',
        'humorous',
        'informative',
        'helpful'
    ],
    SUPPORTED_LANGUAGES: [
        {
            code: 'en',
            name: 'English'
        },
        {
            code: 'es',
            name: 'Spanish'
        },
        {
            code: 'fr',
            name: 'French'
        },
        {
            code: 'de',
            name: 'German'
        },
        {
            code: 'it',
            name: 'Italian'
        },
        {
            code: 'pt',
            name: 'Portuguese'
        }
    ]
};
const VALIDATION_RULES = {
    MIN_TOPIC_LENGTH: 3,
    MAX_TOPIC_LENGTH: 200,
    MIN_CONTENT_LENGTH: 1,
    MAX_CONTENT_LENGTH: 2000,
    MIN_USERNAME_LENGTH: 3,
    MAX_USERNAME_LENGTH: 50,
    MIN_PASSWORD_LENGTH: 8,
    MAX_PASSWORD_LENGTH: 128
};
const UI_CONSTANTS = {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    DEBOUNCE_DELAY: 300,
    TOAST_DURATION: 5000,
    LOADING_SPINNER_DELAY: 200
};
const STORAGE_KEYS = {
    AUTH_TOKEN: 'authToken',
    USER_PREFERENCES: 'userPreferences',
    DRAFT_CONTENT: 'draftContent',
    THEME: 'theme'
};
const CONTENT_MODES = {
    NEW_TWEET: 'new_tweet',
    REPLY: 'reply',
    THREAD: 'thread',
    REWRITE: 'rewrite'
};
const POST_STATUS = {
    PENDING: 'pending',
    POSTED: 'posted',
    FAILED: 'failed',
    CANCELLED: 'cancelled'
};
const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network error. Please check your connection.',
    UNAUTHORIZED: 'Your session has expired. Please log in again.',
    FORBIDDEN: 'Access denied.',
    NOT_FOUND: 'The requested resource was not found.',
    VALIDATION_ERROR: 'Please check your input and try again.',
    GENERATION_FAILED: 'Content generation failed. Please try again.',
    RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
    GENERIC_ERROR: 'Something went wrong. Please try again.'
};
const SUCCESS_MESSAGES = {
    CONTENT_GENERATED: 'Content generated successfully!',
    CONTENT_SAVED: 'Content saved successfully!',
    SETTINGS_UPDATED: 'Settings updated successfully!',
    LOGIN_SUCCESS: 'Login successful!',
    LOGOUT_SUCCESS: 'Logout successful!'
};
const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503
};
const REGEX_PATTERNS = {
    EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    USERNAME: /^[a-zA-Z0-9_]+$/,
    PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,
    HASHTAG: /#\w+/g,
    MENTION: /@\w+/g,
    URL: /https?:\/\/[^\s]+/g
};
const getErrorMessageByStatus = (status)=>{
    switch(status){
        case HTTP_STATUS.UNAUTHORIZED:
            return ERROR_MESSAGES.UNAUTHORIZED;
        case HTTP_STATUS.FORBIDDEN:
            return ERROR_MESSAGES.FORBIDDEN;
        case HTTP_STATUS.NOT_FOUND:
            return ERROR_MESSAGES.NOT_FOUND;
        case HTTP_STATUS.UNPROCESSABLE_ENTITY:
            return ERROR_MESSAGES.VALIDATION_ERROR;
        case HTTP_STATUS.TOO_MANY_REQUESTS:
            return ERROR_MESSAGES.RATE_LIMIT_EXCEEDED;
        case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        case HTTP_STATUS.SERVICE_UNAVAILABLE:
            return ERROR_MESSAGES.GENERATION_FAILED;
        default:
            return ERROR_MESSAGES.GENERIC_ERROR;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/storage.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Storage abstraction following Dependency Inversion Principle
__turbopack_context__.s({
    "storage": (()=>storage)
});
class LocalStorageAdapter {
    getItem(key) {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            return localStorage.getItem(key);
        } catch  {
            return null;
        }
    }
    setItem(key, value) {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.setItem(key, value);
        } catch  {
        // Silently fail if storage is not available
        }
    }
    removeItem(key) {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.removeItem(key);
        } catch  {
        // Silently fail if storage is not available
        }
    }
    clear() {
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        try {
            localStorage.clear();
        } catch  {
        // Silently fail if storage is not available
        }
    }
}
const storage = new LocalStorageAdapter();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/apiClient.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiClient": (()=>ApiClient),
    "apiClient": (()=>apiClient),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/storage.ts [app-client] (ecmascript)");
;
;
;
class ApiClient {
    client;
    storage;
    constructor(storageAdapter = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["storage"]){
        this.storage = storageAdapter;
        this.client = this.createClient();
        this.setupInterceptors();
    }
    createClient() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].BASE_URL,
            timeout: __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_CONFIG"].TIMEOUT,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    setupInterceptors() {
        // Request interceptor
        this.client.interceptors.request.use((config)=>{
            const token = this.storage.getItem(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH_TOKEN);
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            return config;
        }, (error)=>Promise.reject(this.handleError(error)));
        // Response interceptor
        this.client.interceptors.response.use((response)=>response, (error)=>{
            if (error.response?.status === 401) {
                this.handleUnauthorized();
            }
            return Promise.reject(this.handleError(error));
        });
    }
    handleUnauthorized() {
        this.storage.removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH_TOKEN);
        if ("TURBOPACK compile-time truthy", 1) {
            window.location.href = '/login';
        }
    }
    handleError(error) {
        if (!error.response) {
            return new Error(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].NETWORK_ERROR);
        }
        switch(error.response.status){
            case 401:
                return new Error(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].UNAUTHORIZED);
            case 400:
                return new Error(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].VALIDATION_ERROR);
            default:
                const responseData = error.response.data;
                return new Error(responseData?.message || __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ERROR_MESSAGES"].GENERIC_ERROR);
        }
    }
    // Public API methods
    get instance() {
        return this.client;
    }
    async get(url, config) {
        const response = await this.client.get(url, config);
        return response.data;
    }
    async post(url, data, config) {
        const response = await this.client.post(url, data, config);
        return response.data;
    }
    async put(url, data, config) {
        const response = await this.client.put(url, data, config);
        return response.data;
    }
    async delete(url, config) {
        const response = await this.client.delete(url, config);
        return response.data;
    }
}
const apiClient = new ApiClient();
const __TURBOPACK__default__export__ = apiClient;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/lib/authService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthService": (()=>AuthService),
    "authService": (()=>authService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/storage.ts [app-client] (ecmascript)");
// Export singleton instance
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/apiClient.ts [app-client] (ecmascript)");
;
;
class AuthService {
    apiClient;
    constructor(apiClient){
        this.apiClient = apiClient;
    }
    async login(credentials) {
        this.validateLoginRequest(credentials);
        // Backend expects form data for OAuth2PasswordRequestForm
        const formData = new URLSearchParams();
        formData.append('username', credentials.username);
        formData.append('password', credentials.password);
        const response = await this.apiClient.instance.post('/auth/token', formData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });
        // Store the token
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["storage"].setItem(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH_TOKEN, response.data.access_token);
        return response.data;
    }
    async register(userData) {
        this.validateRegisterRequest(userData);
        return this.apiClient.post('/users/', userData);
    }
    async getCurrentUser() {
        return this.apiClient.get('/auth/me');
    }
    async logout() {
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["storage"].removeItem(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH_TOKEN);
        if ("TURBOPACK compile-time truthy", 1) {
            window.location.href = '/login';
        }
    }
    isAuthenticated() {
        return !!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["storage"].getItem(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH_TOKEN);
    }
    getToken() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["storage"].getItem(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH_TOKEN);
    }
    // Twitter OAuth 2.0 Methods
    async initiateTwitterOAuth2() {
        const response = await this.apiClient.instance.post('/auth/oauth2/twitter/init');
        return response.data;
    }
    async handleTwitterOAuth2Callback(callbackData) {
        const response = await this.apiClient.instance.post('/auth/oauth2/twitter/callback', callbackData);
        // Store the JWT token
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$storage$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["storage"].setItem(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["STORAGE_KEYS"].AUTH_TOKEN, response.data.access_token);
        return response.data;
    }
    // Validation methods
    validateLoginRequest(request) {
        if (!request.username?.trim()) {
            throw new Error('Username is required');
        }
        if (!request.password?.trim()) {
            throw new Error('Password is required');
        }
    }
    validateRegisterRequest(request) {
        if (!request.username?.trim()) {
            throw new Error('Username is required');
        }
        if (!request.email?.trim()) {
            throw new Error('Email is required');
        }
        if (!request.password?.trim()) {
            throw new Error('Password is required');
        }
        if (request.password.length < 6) {
            throw new Error('Password must be at least 6 characters');
        }
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(request.email)) {
            throw new Error('Please enter a valid email address');
        }
    }
}
;
const authService = new AuthService(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"]);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth),
    "withAuth": (()=>withAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/lib/authService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Public routes that don't require authentication
const PUBLIC_ROUTES = [
    '/',
    '/login',
    '/register',
    '/auth/twitter/callback',
    '/auth/twitter/oauth2-callback',
    '/test-twitter-oauth2',
    '/terms',
    '/privacy',
    '/about',
    '/contact'
];
// Routes that should redirect to login if not authenticated
const PROTECTED_ROUTES = [
    '/dashboard',
    '/content',
    '/analytics',
    '/settings',
    '/profile'
];
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const isAuthenticated = !!user && !!__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getToken();
    // Check if current route is public
    const isPublicRoute = PUBLIC_ROUTES.some((route)=>{
        if (route === '/') return pathname === '/';
        return pathname.startsWith(route);
    });
    // Check if current route is protected
    const isProtectedRoute = PROTECTED_ROUTES.some((route)=>pathname.startsWith(route));
    const refreshUser = async ()=>{
        try {
            const token = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getToken();
            if (!token) {
                setUser(null);
                return;
            }
            const currentUser = await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
            setUser(currentUser);
        } catch (error) {
            console.error('Failed to refresh user:', error);
            setUser(null);
            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].logout();
        }
    };
    const login = async (credentials)=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].login(credentials);
            // After successful login, fetch user data
            await refreshUser();
        } catch (error) {
            throw error;
        }
    };
    const logout = ()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].logout();
        setUser(null);
        router.push('/');
    };
    // Handle authentication state and routing
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const initAuth = {
                "AuthProvider.useEffect.initAuth": async ()=>{
                    setIsLoading(true);
                    try {
                        const token = __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getToken();
                        if (token) {
                            // Try to get current user
                            await refreshUser();
                        }
                    } catch (error) {
                        console.error('Auth initialization error:', error);
                        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$lib$2f$authService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].logout();
                        setUser(null);
                    } finally{
                        setIsLoading(false);
                    }
                }
            }["AuthProvider.useEffect.initAuth"];
            initAuth();
        }
    }["AuthProvider.useEffect"], []);
    // Handle route protection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            if (isLoading) return;
            // If user is not authenticated and trying to access protected route
            if (!isAuthenticated && isProtectedRoute) {
                console.log('Redirecting to login - protected route accessed without auth');
                router.push('/login');
                return;
            }
            // If user is authenticated and trying to access login page
            if (isAuthenticated && pathname === '/login') {
                console.log('Redirecting to dashboard - already authenticated');
                router.push('/dashboard');
                return;
            }
            // For any other route that's not explicitly public, redirect to login
            if (!isAuthenticated && !isPublicRoute) {
                console.log('Redirecting to login - non-public route accessed without auth');
                router.push('/login');
                return;
            }
        }
    }["AuthProvider.useEffect"], [
        isAuthenticated,
        isLoading,
        pathname,
        isProtectedRoute,
        isPublicRoute,
        router
    ]);
    const value = {
        user,
        isLoading,
        isAuthenticated,
        login,
        logout,
        refreshUser
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/app/contexts/AuthContext.tsx",
        lineNumber: 164,
        columnNumber: 5
    }, this);
}
_s(AuthProvider, "SWJuhWhWQgul5IsZEWEhril+390=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function withAuth(Component) {
    var _s = __turbopack_context__.k.signature();
    return _s(function AuthenticatedComponent(props) {
        _s();
        const { isAuthenticated, isLoading } = useAuth();
        const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
            "withAuth.AuthenticatedComponent.useEffect": ()=>{
                if (!isLoading && !isAuthenticated) {
                    router.push('/login');
                }
            }
        }["withAuth.AuthenticatedComponent.useEffect"], [
            isAuthenticated,
            isLoading,
            router
        ]);
        if (isLoading) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "min-h-screen flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"
                }, void 0, false, {
                    fileName: "[project]/app/contexts/AuthContext.tsx",
                    lineNumber: 193,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/contexts/AuthContext.tsx",
                lineNumber: 192,
                columnNumber: 9
            }, this);
        }
        if (!isAuthenticated) {
            return null;
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Component, {
            ...props
        }, void 0, false, {
            fileName: "[project]/app/contexts/AuthContext.tsx",
            lineNumber: 202,
            columnNumber: 12
        }, this);
    }, "mEH+GTDGNx6l1kiic8iucxoBZHI=", false, function() {
        return [
            useAuth,
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
        ];
    });
}
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=app_60c57231._.js.map