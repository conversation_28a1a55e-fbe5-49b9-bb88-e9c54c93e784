# Render Database (Get these from your Render dashboard)
DATABASE_URL=postgresql://username:password@hostname:port/database_name
# Example: postgresql://autoreach_user:<EMAIL>:5432/autoreach_db

# Render Redis (Get this from your Render dashboard)
REDIS_URL=redis://username:password@hostname:port
# Example: redis://red-xxxxxxxxx:<EMAIL>:6379

# Security (REQUIRED - Generate secure values!)
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=CHANGE-THIS-TO-A-SECURE-32-CHARACTER-SECRET-KEY
DEBUG=true

# Twitter API Credentials (OAuth 2.0 - Get from developer.twitter.com)
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_OAUTH_REDIRECT_URI=http://localhost:3000/auth/callback

# OpenAI API (Get from platform.openai.com)
OPENAI_API_KEY=your_openai_api_key

# Email Settings (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
