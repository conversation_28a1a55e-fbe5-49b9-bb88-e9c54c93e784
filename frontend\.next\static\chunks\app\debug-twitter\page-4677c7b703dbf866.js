(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[248],{3503:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(5155),l=t(2115);function r(){let[e,s]=(0,l.useState)(null),[t,r]=(0,l.useState)(null),[d,n]=(0,l.useState)("");return(0,l.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/auth/oauth2/twitter/debug"),t=await e.json();s(t)}catch(e){r("Failed to fetch debug info")}};n(window.location.href),e()},[]),(0,a.jsxs)("div",{className:"p-8 max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Twitter OAuth Debug"}),t&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4",children:t}),e&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h2",{className:"font-semibold mb-2",children:"Configuration:"}),(0,a.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(e,null,2)})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h2",{className:"font-semibold mb-2",children:"Current URL:"}),(0,a.jsx)("p",{className:"text-sm bg-gray-100 p-2 rounded",children:d||"Loading..."})]})]})}},7437:(e,s,t)=>{Promise.resolve().then(t.bind(t,3503))}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(7437)),_N_E=e.O()}]);