from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, timezone

from app.core.database import get_db
from app.models.user import User
from app.models.tweet import Tweet, TweetStatus
from app.api.auth import get_current_user
from app.core.dependencies import get_twitter_service

router = APIRouter()


class TweetCreate(BaseModel):
    content: str
    scheduled_at: Optional[datetime] = None


class TweetResponse(BaseModel):
    model_config = {"from_attributes": True}

    id: int
    content: str
    status: str
    scheduled_at: Optional[datetime]
    posted_at: Optional[datetime]
    likes_count: int
    retweets_count: int
    replies_count: int
    created_at: datetime


class TweetUpdate(BaseModel):
    content: Optional[str] = None
    scheduled_at: Optional[datetime] = None


@router.post("/", response_model=TweetResponse, status_code=status.HTTP_201_CREATED)
async def create_tweet(
    tweet: TweetCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if tweet.scheduled_at and tweet.scheduled_at <= datetime.now(timezone.utc):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Scheduled time must be in the future"
        )

    db_tweet = Tweet(
        user_id=current_user.id,
        content=tweet.content,
        scheduled_at=tweet.scheduled_at,
        status=TweetStatus.SCHEDULED if tweet.scheduled_at else TweetStatus.DRAFT
    )
    db.add(db_tweet)
    db.commit()
    db.refresh(db_tweet)
    return db_tweet


@router.get("/", response_model=List[TweetResponse])
async def read_tweets(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    query = db.query(Tweet).filter(Tweet.user_id == current_user.id)

    if status_filter:
        try:
            status_enum = TweetStatus(status_filter)
            query = query.filter(Tweet.status == status_enum)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid status filter: {status_filter}"
            )

    tweets = query.order_by(Tweet.created_at.desc()).offset(skip).limit(limit).all()
    return tweets


@router.get("/{tweet_id}", response_model=TweetResponse)
async def read_tweet(
    tweet_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    tweet = db.query(Tweet).filter(
        Tweet.id == tweet_id,
        Tweet.user_id == current_user.id
    ).first()
    if tweet is None:
        raise HTTPException(status_code=404, detail="Tweet not found")
    return tweet


@router.put("/{tweet_id}", response_model=TweetResponse)
async def update_tweet(
    tweet_id: int,
    tweet_update: TweetUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    tweet = db.query(Tweet).filter(
        Tweet.id == tweet_id,
        Tweet.user_id == current_user.id
    ).first()
    if tweet is None:
        raise HTTPException(status_code=404, detail="Tweet not found")

    if tweet.status == TweetStatus.POSTED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot update a posted tweet"
        )

    for field, value in tweet_update.dict(exclude_unset=True).items():
        setattr(tweet, field, value)

    if tweet_update.scheduled_at:
        if tweet_update.scheduled_at <= datetime.now(timezone.utc):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Scheduled time must be in the future"
            )
        tweet.status = TweetStatus.SCHEDULED
    
    db.commit()
    db.refresh(tweet)
    return tweet


@router.delete("/{tweet_id}")
async def delete_tweet(
    tweet_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    tweet = db.query(Tweet).filter(
        Tweet.id == tweet_id,
        Tweet.user_id == current_user.id
    ).first()
    if tweet is None:
        raise HTTPException(status_code=404, detail="Tweet not found")

    if tweet.status == TweetStatus.POSTED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete a posted tweet"
        )

    db.delete(tweet)
    db.commit()
    return {"message": "Tweet deleted successfully"}


@router.post("/{tweet_id}/post-now")
async def post_now(
    tweet_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    twitter_service=Depends(get_twitter_service)
):
    tweet = db.query(Tweet).filter(
        Tweet.id == tweet_id,
        Tweet.user_id == current_user.id
    ).first()

    if not tweet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tweet not found"
        )

    if tweet.status not in [TweetStatus.DRAFT, TweetStatus.SCHEDULED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only draft or scheduled tweets can be posted"
        )

    result = twitter_service.post_tweet(
        text=tweet.content,
        user_access_token=current_user.twitter_access_token,
        user_access_token_secret=current_user.twitter_refresh_token
    )

    if result["success"]:
        tweet.status = TweetStatus.POSTED
        tweet.tweet_id = result["id"]
        tweet.posted_at = datetime.now(timezone.utc)
        db.commit()

        return {
            "message": "Tweet posted successfully",
            "tweet_id": result["id"]
        }
    else:
        tweet.status = TweetStatus.FAILED
        db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to post tweet: {result['error']}"
        )
