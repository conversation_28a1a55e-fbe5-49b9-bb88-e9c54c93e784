(()=>{var e={};e.id=745,e.ids=[745],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5636:(e,s,t)=>{Promise.resolve().then(t.bind(t,95188))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14890:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\Header.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31398:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(37413),a=t(14890);function n(){return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Analytics"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"Track your Twitter growth and engagement metrics."})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md",children:"Last 7 days"}),(0,r.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Last 30 days"}),(0,r.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Last 90 days"})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Followers"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:"2,847"}),(0,r.jsxs)("p",{className:"text-sm text-green-600 flex items-center mt-1",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18"})}),"+12% (342 new)"]})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Engagement Rate"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:"4.2%"}),(0,r.jsxs)("p",{className:"text-sm text-green-600 flex items-center mt-1",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18"})}),"+0.5%"]})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Impressions"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:"45.2K"}),(0,r.jsxs)("p",{className:"text-sm text-green-600 flex items-center mt-1",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18"})}),"+18% (6.8K)"]})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]})}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Posts Published"}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:"28"}),(0,r.jsxs)("p",{className:"text-sm text-green-600 flex items-center mt-1",children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18"})}),"+8% (2 more)"]})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-twitter-light-blue rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-twitter-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Follower Growth"}),(0,r.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Chart placeholder - Follower growth over time"})})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Engagement Metrics"}),(0,r.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,r.jsx)("p",{className:"text-gray-500",children:"Chart placeholder - Likes, retweets, comments"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Top Performing Posts"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"3 days ago"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsx)("span",{children:"❤️ 156"}),(0,r.jsx)("span",{children:"\uD83D\uDD04 42"}),(0,r.jsx)("span",{children:"\uD83D\uDCAC 18"}),(0,r.jsx)("span",{children:"\uD83D\uDC41️ 2.3K"})]})]}),(0,r.jsx)("p",{className:"text-gray-900",children:"Just discovered an amazing new AI tool that's revolutionizing content creation! \uD83D\uDE80 The future of marketing is here. #AI #Marketing #Innovation"})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"5 days ago"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsx)("span",{children:"❤️ 89"}),(0,r.jsx)("span",{children:"\uD83D\uDD04 23"}),(0,r.jsx)("span",{children:"\uD83D\uDCAC 12"}),(0,r.jsx)("span",{children:"\uD83D\uDC41️ 1.8K"})]})]}),(0,r.jsx)("p",{className:"text-gray-900",children:"Building a personal brand takes time, but the results are worth it. Focus on providing value and being genuine. #PersonalBrand #Growth"})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)("span",{className:"text-sm text-gray-500",children:"1 week ago"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsx)("span",{children:"❤️ 67"}),(0,r.jsx)("span",{children:"\uD83D\uDD04 19"}),(0,r.jsx)("span",{children:"\uD83D\uDCAC 8"}),(0,r.jsx)("span",{children:"\uD83D\uDC41️ 1.5K"})]})]}),(0,r.jsx)("p",{className:"text-gray-900",children:"Monday motivation: Success isn't just about what you accomplish in your life, it's about what you inspire others to do. \uD83D\uDCAA #MondayMotivation #Success"})]})]})]})]})]})}},33873:e=>{"use strict";e.exports=require("path")},51159:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(85814),n=t.n(a);let i=({href:e="/dashboard",size:s="md",showText:t=!0})=>{let a={sm:{icon:"w-6 h-6",text:"text-lg"},md:{icon:"w-8 h-8",text:"text-xl"},lg:{icon:"w-10 h-10",text:"text-2xl"}},i=(0,r.jsxs)("div",{className:"flex items-center space-x-2 select-none",children:[(0,r.jsx)("div",{className:`${a[s].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`,children:(0,r.jsx)("span",{className:`text-white font-bold ${"sm"===s?"text-sm":"text-lg"}`,children:"A"})}),t&&(0,r.jsx)("span",{className:`${a[s].text} font-bold text-gray-900 whitespace-nowrap`,children:"AutoReach"})]});return e?(0,r.jsx)(n(),{href:e,children:i}):i}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64950:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d={children:["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,31398)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\analytics\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\analytics\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},75780:(e,s,t)=>{Promise.resolve().then(t.bind(t,14890))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86364:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,r.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},94735:e=>{"use strict";e.exports=require("events")},95188:(e,s,t)=>{"use strict";t.d(s,{default:()=>x});var r=t(60687),a=t(85814),n=t.n(a),i=t(16189),o=t(43210),l=t(51785),d=t(51159),c=t(63772);let x=({showNavigation:e=!0})=>{let s=(0,i.usePathname)(),[t,a]=(0,o.useState)(!1),[x,m]=(0,o.useState)(!1),{user:h,logout:u,isAuthenticated:p}=(0,c.A)();return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 relative z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(d.A,{})}),e&&(0,r.jsx)("nav",{className:"hidden md:flex space-x-6",children:l.Ij.map(e=>(0,r.jsx)(n(),{href:e.path,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${s===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"} ${"Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100":""}`,children:e.name},e.path))}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e&&(0,r.jsx)("button",{onClick:()=>a(!t),className:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle mobile menu",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),p?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>m(!x),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-purple-600",children:h?.full_name?.[0]||h?.username?.[0]||"U"})}),(0,r.jsx)("span",{className:"hidden sm:block text-sm font-medium",children:h?.full_name||h?.username}),(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),x&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,r.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 border-b border-gray-100",children:[(0,r.jsx)("p",{className:"font-medium",children:h?.full_name||h?.username}),h?.twitter_username&&(0,r.jsxs)("p",{className:"text-gray-500",children:["@",h.twitter_username]})]}),(0,r.jsx)(n(),{href:"/settings",onClick:()=>m(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Settings"}),(0,r.jsx)("button",{onClick:()=>{m(!1),u()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})]}):(0,r.jsx)(n(),{href:"/login",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})]})]}),e&&t&&(0,r.jsx)("div",{className:"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,r.jsx)("nav",{className:"px-4 py-4 space-y-2",children:l.Ij.map(e=>(0,r.jsx)(n(),{href:e.path,onClick:()=>a(!1),className:`block px-4 py-3 rounded-lg text-base font-medium transition-colors ${s===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"} ${"Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100 border border-green-200":""}`,children:(0,r.jsxs)("span",{className:"flex items-center gap-3",children:[(0,r.jsxs)("span",{className:"text-lg",children:["Dashboard"===e.name&&"\uD83D\uDCCA","Content"===e.name&&"✍️","Analytics"===e.name&&"\uD83D\uDCC8","Settings"===e.name&&"⚙️","Auth"===e.name&&"\uD83D\uDD10","Test API"===e.name&&"\uD83E\uDDEA"]}),e.name]})},e.path))})})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,786,658,814,70],()=>t(64950));module.exports=r})();