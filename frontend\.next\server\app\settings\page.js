(()=>{var e={};e.id=662,e.ids=[662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14890:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\Header.tsx","default")},18684:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,98245)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\settings\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,86364))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\settings\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27262:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AutoReach\\\\frontend\\\\app\\\\components\\\\TwitterAuth.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\AutoReach\\frontend\\app\\components\\TwitterAuth.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51159:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(60687),a=r(85814),n=r.n(a);let o=({href:e="/dashboard",size:t="md",showText:r=!0})=>{let a={sm:{icon:"w-6 h-6",text:"text-lg"},md:{icon:"w-8 h-8",text:"text-xl"},lg:{icon:"w-10 h-10",text:"text-2xl"}},o=(0,s.jsxs)("div",{className:"flex items-center space-x-2 select-none",children:[(0,s.jsx)("div",{className:`${a[t].icon} bg-primary-600 rounded-lg flex items-center justify-center flex-shrink-0`,children:(0,s.jsx)("span",{className:`text-white font-bold ${"sm"===t?"text-sm":"text-lg"}`,children:"A"})}),r&&(0,s.jsx)("span",{className:`${a[t].text} font-bold text-gray-900 whitespace-nowrap`,children:"AutoReach"})]});return e?(0,s.jsx)(n(),{href:e,children:o}):o}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68989:(e,t,r)=>{Promise.resolve().then(r.bind(r,14890)),Promise.resolve().then(r.bind(r,27262))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84720:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),a=r(43210),n=r(88606);function o({onStatusChange:e}){let[t,r]=(0,a.useState)({connected:!1}),[o,i]=(0,a.useState)(!1),[l,c]=(0,a.useState)(null),[d,m]=(0,a.useState)(!1);(0,a.useCallback)(async()=>{try{c(null);let t=await n.b.getTwitterStatus();r(t),e?.(t)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";c(`Failed to load Twitter status: ${e}`)}},[e]);let u=async()=>{if(!d)return void c("Please log in first to connect your Twitter account");i(!0),c(null);try{let e=await n.b.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret),sessionStorage.setItem("twitter_oauth_token",e.oauth_token),window.location.href=e.authorization_url}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";c(`Failed to connect Twitter: ${e}`),i(!1)}},x=async()=>{i(!0),c(null);try{await n.b.disconnectTwitter();let t={connected:!1};r(t),e?.(t)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";c(`Failed to disconnect Twitter: ${e}`)}finally{i(!1)}};return d?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-twitter-blue rounded-lg flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-white",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900",children:"Twitter"}),t.connected?(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["@",t.twitter_username]}):(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Not connected"})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-3",children:t.connected?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Connected"}),(0,s.jsx)("button",{onClick:x,disabled:o,className:"px-3 py-1 text-sm text-red-600 border border-red-300 rounded hover:bg-red-50 disabled:opacity-50",children:o?"Disconnecting...":"Disconnect"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:"Not connected"}),(0,s.jsx)("button",{onClick:u,disabled:o,className:"px-3 py-1 text-sm text-white bg-twitter-blue rounded hover:bg-blue-600 disabled:opacity-50",children:o?"Connecting...":"Connect"})]})})]}),l&&(0,s.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("span",{className:"text-sm text-red-800",children:l})]})}),t.connected&&(0,s.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("span",{className:"text-sm text-green-800",children:"Twitter account connected successfully! You can now create and schedule tweets."})]})})]}):(0,s.jsx)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})}),(0,s.jsx)("span",{className:"text-yellow-800",children:"Please log in to connect your Twitter account"})]})})}r(74265)},86364:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/svg+xml",sizes:"any",url:(0,s.fillMetadataSegment)(".",await e.params,"icon.svg")+"?16a4b67dca6d7882"}]},88606:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var s=r(24731);class a{async initiateTwitterAuth(){try{return await s.uE.get("/auth/twitter/login")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to initiate Twitter auth: ${e}`)}}async handleTwitterCallback(e){try{return await s.uE.post("/auth/twitter/callback",e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to handle Twitter callback: ${e}`)}}async getTwitterStatus(){try{return await s.uE.get("/auth/twitter/status")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to get Twitter status: ${e}`)}}async disconnectTwitter(){try{return await s.uE.delete("/auth/twitter/disconnect")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error(`Failed to disconnect Twitter: ${e}`)}}async startTwitterOAuth(){let e=await this.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret);let t=window.open(e.authorization_url,"twitter_auth","width=600,height=600,scrollbars=yes,resizable=yes");return new Promise((r,s)=>{let a=setInterval(()=>{t?.closed&&(clearInterval(a),s(Error("Twitter authorization was cancelled")))},1e3),n=o=>{o.origin===window.location.origin&&("TWITTER_AUTH_SUCCESS"===o.data.type?(clearInterval(a),window.removeEventListener("message",n),t?.close(),r({oauth_token:e.oauth_token,oauth_token_secret:e.oauth_token_secret})):"TWITTER_AUTH_ERROR"===o.data.type&&(clearInterval(a),window.removeEventListener("message",n),t?.close(),s(Error(o.data.error||"Twitter authorization failed"))))};window.addEventListener("message",n)})}async completeTwitterOAuth(e){let t=sessionStorage.getItem("twitter_oauth_token_secret");if(!t)throw Error("OAuth token secret not found. Please restart the authentication process.");let r=new URLSearchParams(window.location.search).get("oauth_token");if(!r)throw Error("OAuth token not found in callback URL");let s=await this.handleTwitterCallback({oauth_token:r,oauth_verifier:e,oauth_token_secret:t});return sessionStorage.removeItem("twitter_oauth_token_secret"),s}}let n=new a},94735:e=>{"use strict";e.exports=require("events")},95188:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(60687),a=r(85814),n=r.n(a),o=r(16189),i=r(43210),l=r(51785),c=r(51159),d=r(63772);let m=({showNavigation:e=!0})=>{let t=(0,o.usePathname)(),[r,a]=(0,i.useState)(!1),[m,u]=(0,i.useState)(!1),{user:x,logout:h,isAuthenticated:p}=(0,d.A)();return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 relative z-50",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(c.A,{})}),e&&(0,s.jsx)("nav",{className:"hidden md:flex space-x-6",children:l.Ij.map(e=>(0,s.jsx)(n(),{href:e.path,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${t===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"} ${"Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100":""}`,children:e.name},e.path))}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[e&&(0,s.jsx)("button",{onClick:()=>a(!r),className:"md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Toggle mobile menu",children:(0,s.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r?(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),p?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("button",{onClick:()=>u(!m),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 p-2 rounded-md",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm font-medium text-purple-600",children:x?.full_name?.[0]||x?.username?.[0]||"U"})}),(0,s.jsx)("span",{className:"hidden sm:block text-sm font-medium",children:x?.full_name||x?.username}),(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),m&&(0,s.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200",children:[(0,s.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-700 border-b border-gray-100",children:[(0,s.jsx)("p",{className:"font-medium",children:x?.full_name||x?.username}),x?.twitter_username&&(0,s.jsxs)("p",{className:"text-gray-500",children:["@",x.twitter_username]})]}),(0,s.jsx)(n(),{href:"/settings",onClick:()=>u(!1),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Settings"}),(0,s.jsx)("button",{onClick:()=>{u(!1),h()},className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Sign out"})]})]}):(0,s.jsx)(n(),{href:"/login",className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",children:"Sign In"})]})]}),e&&r&&(0,s.jsx)("div",{className:"md:hidden absolute top-16 left-0 right-0 bg-white border-b border-gray-200 shadow-lg",children:(0,s.jsx)("nav",{className:"px-4 py-4 space-y-2",children:l.Ij.map(e=>(0,s.jsx)(n(),{href:e.path,onClick:()=>a(!1),className:`block px-4 py-3 rounded-lg text-base font-medium transition-colors ${t===e.path?"text-primary-600 bg-primary-50":"text-gray-600 hover:text-gray-900 hover:bg-gray-50"} ${"Test API"===e.name?"bg-green-50 text-green-700 hover:bg-green-100 border border-green-200":""}`,children:(0,s.jsxs)("span",{className:"flex items-center gap-3",children:[(0,s.jsxs)("span",{className:"text-lg",children:["Dashboard"===e.name&&"\uD83D\uDCCA","Content"===e.name&&"✍️","Analytics"===e.name&&"\uD83D\uDCC8","Settings"===e.name&&"⚙️","Auth"===e.name&&"\uD83D\uDD10","Test API"===e.name&&"\uD83E\uDDEA"]}),e.name]})},e.path))})})]})})}},96605:(e,t,r)=>{Promise.resolve().then(r.bind(r,95188)),Promise.resolve().then(r.bind(r,84720))},98245:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(37413),a=r(14890),n=r(27262);function o(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(a.default,{}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Settings"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your account and application preferences."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)("nav",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-4",children:(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsx)("li",{children:(0,s.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm font-medium text-primary-600 bg-primary-50 rounded-md",children:"Account"})}),(0,s.jsx)("li",{children:(0,s.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md",children:"Twitter Integration"})}),(0,s.jsx)("li",{children:(0,s.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md",children:"Content Preferences"})}),(0,s.jsx)("li",{children:(0,s.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md",children:"Notifications"})}),(0,s.jsx)("li",{children:(0,s.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md",children:"Billing"})})]})})}),(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:"Account Settings"}),(0,s.jsxs)("form",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Profile Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First Name"}),(0,s.jsx)("input",{type:"text",id:"firstName",defaultValue:"John",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last Name"}),(0,s.jsx)("input",{type:"text",id:"lastName",defaultValue:"Doe",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,s.jsx)("input",{type:"email",id:"email",defaultValue:"<EMAIL>",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Connected Accounts"}),(0,s.jsx)(n.default,{})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Content Preferences"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"defaultTone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Tone"}),(0,s.jsxs)("select",{id:"defaultTone",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"professional",children:"Professional"}),(0,s.jsx)("option",{value:"casual",children:"Casual"}),(0,s.jsx)("option",{value:"humorous",children:"Humorous"}),(0,s.jsx)("option",{value:"inspirational",children:"Inspirational"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"defaultLength",className:"block text-sm font-medium text-gray-700 mb-2",children:"Default Length"}),(0,s.jsxs)("select",{id:"defaultLength",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"short",children:"Short"}),(0,s.jsx)("option",{value:"medium",children:"Medium"}),(0,s.jsx)("option",{value:"long",children:"Long"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"autoHashtags",defaultChecked:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"autoHashtags",className:"ml-2 text-sm text-gray-700",children:"Automatically include hashtags"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"autoEmojis",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"autoEmojis",className:"ml-2 text-sm text-gray-700",children:"Automatically include emojis"})]})]})]})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Notifications"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"emailNotifications",defaultChecked:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"emailNotifications",className:"ml-2 text-sm text-gray-700",children:"Email notifications for published posts"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"weeklyReports",defaultChecked:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"weeklyReports",className:"ml-2 text-sm text-gray-700",children:"Weekly analytics reports"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"failureAlerts",defaultChecked:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"failureAlerts",className:"ml-2 text-sm text-gray-700",children:"Alerts for failed posts"})]})]})]}),(0,s.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)("button",{type:"button",className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",children:"Save Changes"})]})})]})]})})]})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,786,658,814,70],()=>r(18684));module.exports=s})();