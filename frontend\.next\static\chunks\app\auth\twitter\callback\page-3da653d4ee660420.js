(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[769],{2178:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var s=r(4205);class a{async initiateTwitterAuth(){try{return await s.uE.get("/auth/twitter/login")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to initiate Twitter auth: ".concat(e))}}async handleTwitterCallback(e){try{return await s.uE.post("/auth/twitter/callback",e)}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to handle Twitter callback: ".concat(e))}}async getTwitterStatus(){try{return await s.uE.get("/auth/twitter/status")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to get Twitter status: ".concat(e))}}async disconnectTwitter(){try{return await s.uE.delete("/auth/twitter/disconnect")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";throw Error("Failed to disconnect Twitter: ".concat(e))}}async startTwitterOAuth(){let e=await this.initiateTwitterAuth();sessionStorage.setItem("twitter_oauth_token_secret",e.oauth_token_secret);let t=window.open(e.authorization_url,"twitter_auth","width=600,height=600,scrollbars=yes,resizable=yes");return new Promise((r,s)=>{let a=setInterval(()=>{(null==t?void 0:t.closed)&&(clearInterval(a),s(Error("Twitter authorization was cancelled")))},1e3),n=i=>{i.origin===window.location.origin&&("TWITTER_AUTH_SUCCESS"===i.data.type?(clearInterval(a),window.removeEventListener("message",n),null==t||t.close(),r({oauth_token:e.oauth_token,oauth_token_secret:e.oauth_token_secret})):"TWITTER_AUTH_ERROR"===i.data.type&&(clearInterval(a),window.removeEventListener("message",n),null==t||t.close(),s(Error(i.data.error||"Twitter authorization failed"))))};window.addEventListener("message",n)})}async completeTwitterOAuth(e){let t=sessionStorage.getItem("twitter_oauth_token_secret");if(!t)throw Error("OAuth token secret not found. Please restart the authentication process.");let r=new URLSearchParams(window.location.search).get("oauth_token");if(!r)throw Error("OAuth token not found in callback URL");let s=await this.handleTwitterCallback({oauth_token:r,oauth_verifier:e,oauth_token_secret:t});return sessionStorage.removeItem("twitter_oauth_token_secret"),s}}let n=new a},2182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(5155),a=r(2115),n=r(5695),i=r(2178),o=r(3851);function c(){let e=(0,n.useRouter)(),t=(0,n.useSearchParams)(),[r,c]=(0,a.useState)("loading"),[l,u]=(0,a.useState)(""),[d,h]=(0,a.useState)(null),E=(0,a.useCallback)(async()=>{try{if(!o.y.isAuthenticated()){c("error"),u("You must be logged in to connect your Twitter account"),setTimeout(()=>e.push("/"),3e3);return}let r=t.get("oauth_token"),s=t.get("oauth_verifier");if(t.get("denied")){c("error"),u("Twitter authorization was denied"),setTimeout(()=>e.push("/settings"),3e3);return}if(!r||!s){c("error"),u("Missing OAuth parameters in callback URL"),setTimeout(()=>e.push("/settings"),3e3);return}let a=sessionStorage.getItem("twitter_oauth_token_secret");if(!a){c("error"),u("OAuth token secret not found. Please restart the authentication process."),setTimeout(()=>e.push("/settings"),3e3);return}let n=await i.b.handleTwitterCallback({oauth_token:r,oauth_verifier:s,oauth_token_secret:a});sessionStorage.removeItem("twitter_oauth_token_secret"),sessionStorage.removeItem("twitter_oauth_token"),c("success"),u(n.message),h(n.twitter_username||null),setTimeout(()=>e.push("/settings"),2e3)}catch(t){c("error"),u(t instanceof Error?t.message:"Failed to connect Twitter account"),setTimeout(()=>e.push("/settings"),3e3)}},[t,e]);return(0,a.useEffect)(()=>{E()},[E]),(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(()=>{switch(r){case"loading":return(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"});case"success":return(0,s.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})});case"error":return(0,s.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}})()}),(0,s.jsx)("h2",{className:"text-2xl font-bold ".concat((()=>{switch(r){case"loading":return"text-blue-600";case"success":return"text-green-600";case"error":return"text-red-600"}})()),children:(()=>{switch(r){case"loading":return"Connecting Twitter Account...";case"success":return"Twitter Connected Successfully!";case"error":return"Connection Failed"}})()}),(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsx)("p",{className:"text-gray-600",children:l}),"success"===r&&d&&(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Connected as ",(0,s.jsxs)("span",{className:"font-medium text-twitter-blue",children:["@",d]})]})]}),"loading"===r&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full animate-bounce"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Please wait while we connect your account..."})]}),"success"===r&&(0,s.jsx)("div",{className:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),(0,s.jsx)("span",{className:"text-sm text-green-800",children:"You can now create and schedule tweets from Reachly!"})]})}),"error"===r&&(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)("button",{onClick:()=>e.push("/settings"),className:"w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Go to Settings"})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["loading"===r&&"This may take a few seconds...","success"===r&&"Redirecting to settings...","error"===r&&"Redirecting in a few seconds..."]})})]})})})}function l(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-blue-600",children:"Loading..."}),(0,s.jsx)("p",{className:"text-gray-600 mt-4",children:"Processing Twitter authentication..."})]})})})}function u(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(l,{}),children:(0,s.jsx)(c,{})})}},2703:(e,t,r)=>{"use strict";r.d(t,{I:()=>a});class s{getItem(e){try{return localStorage.getItem(e)}catch(e){return null}}setItem(e,t){try{localStorage.setItem(e,t)}catch(e){}}removeItem(e){try{localStorage.removeItem(e)}catch(e){}}clear(){try{localStorage.clear()}catch(e){}}}let a=new s},3057:(e,t,r)=>{Promise.resolve().then(r.bind(r,2182))},3851:(e,t,r)=>{"use strict";r.d(t,{y:()=>o});var s=r(4611),a=r(2703),n=r(4205);class i{async login(e){this.validateLoginRequest(e);let t=new URLSearchParams;t.append("username",e.username),t.append("password",e.password);let r=await this.apiClient.instance.post("/auth/token",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}});return a.I.setItem(s.d5.AUTH_TOKEN,r.data.access_token),r.data}async register(e){return this.validateRegisterRequest(e),this.apiClient.post("/users/",e)}async getCurrentUser(){return this.apiClient.get("/auth/me")}async logout(){a.I.removeItem(s.d5.AUTH_TOKEN),window.location.href="/login"}isAuthenticated(){return!!a.I.getItem(s.d5.AUTH_TOKEN)}getToken(){return a.I.getItem(s.d5.AUTH_TOKEN)}async initiateTwitterOAuth2(){return(await this.apiClient.instance.post("/auth/oauth2/twitter/init")).data}async handleTwitterOAuth2Callback(e){let t=await this.apiClient.instance.post("/auth/oauth2/twitter/callback",e);return a.I.setItem(s.d5.AUTH_TOKEN,t.data.access_token),t.data}validateLoginRequest(e){var t,r;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(r=e.password)?void 0:r.trim()))throw Error("Password is required")}validateRegisterRequest(e){var t,r,s;if(!(null==(t=e.username)?void 0:t.trim()))throw Error("Username is required");if(!(null==(r=e.email)?void 0:r.trim()))throw Error("Email is required");if(!(null==(s=e.password)?void 0:s.trim()))throw Error("Password is required");if(e.password.length<6)throw Error("Password must be at least 6 characters");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email))throw Error("Please enter a valid email address")}constructor(e){this.apiClient=e}}let o=new i(n.uE)},4205:(e,t,r)=>{"use strict";r.d(t,{uE:()=>o});var s=r(3464),a=r(4611),n=r(2703);class i{createClient(){return s.A.create({baseURL:a.i3.BASE_URL,timeout:a.i3.TIMEOUT,headers:{"Content-Type":"application/json"}})}setupInterceptors(){this.client.interceptors.request.use(e=>{let t=this.storage.getItem(a.d5.AUTH_TOKEN);return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(this.handleError(e))),this.client.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&this.handleUnauthorized(),Promise.reject(this.handleError(e))})}handleUnauthorized(){this.storage.removeItem(a.d5.AUTH_TOKEN),window.location.href="/login"}handleError(e){if(!e.response)return Error(a.UU.NETWORK_ERROR);switch(e.response.status){case 401:return Error(a.UU.UNAUTHORIZED);case 400:return Error(a.UU.VALIDATION_ERROR);default:let t=e.response.data;return Error((null==t?void 0:t.message)||a.UU.GENERIC_ERROR)}}get instance(){return this.client}async get(e,t){return(await this.client.get(e,t)).data}async post(e,t,r){return(await this.client.post(e,t,r)).data}async put(e,t,r){return(await this.client.put(e,t,r)).data}async delete(e,t){return(await this.client.delete(e,t)).data}constructor(e=n.I){this.storage=e,this.client=this.createClient(),this.setupInterceptors()}}let o=new i},4611:(e,t,r)=>{"use strict";r.d(t,{ID:()=>a,Ij:()=>s,KA:()=>c,Ot:()=>o,RW:()=>m,UU:()=>d,WF:()=>E,d5:()=>u,gY:()=>l,gx:()=>h,i3:()=>i,m0:()=>n});let s=[{name:"Dashboard",path:"/dashboard"},{name:"Content",path:"/content"},{name:"Analytics",path:"/analytics"},{name:"Settings",path:"/settings"},{name:"Auth",path:"/auth"},{name:"Test API",path:"/test-connection"}],a=[{value:"engaging",label:"Engaging"},{value:"professional",label:"Professional"},{value:"casual",label:"Casual"},{value:"educational",label:"Educational"},{value:"humorous",label:"Humorous"},{value:"informative",label:"Informative"},{value:"helpful",label:"Helpful"}],n=[{value:"short",label:"Short (1-2 sentences)"},{value:"medium",label:"Medium (3-5 sentences)"},{value:"long",label:"Long (6+ sentences)"}],i={BASE_URL:"http://localhost:8000/api",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},o={MAX_TWEET_LENGTH:280,MAX_THREAD_TWEETS:25,DEFAULT_THREAD_SIZE:3,MAX_HASHTAGS_RECOMMENDED:3,MAX_MENTIONS_RECOMMENDED:5},c={DEFAULT_STYLE:"engaging",DEFAULT_LANGUAGE:"en",SUPPORTED_STYLES:["engaging","professional","casual","educational","humorous","informative","helpful"],SUPPORTED_LANGUAGES:[{code:"en",name:"English"},{code:"es",name:"Spanish"},{code:"fr",name:"French"},{code:"de",name:"German"},{code:"it",name:"Italian"},{code:"pt",name:"Portuguese"}]},l={MIN_TOPIC_LENGTH:3,MAX_TOPIC_LENGTH:200,MIN_CONTENT_LENGTH:1,MAX_CONTENT_LENGTH:2e3,MIN_USERNAME_LENGTH:3,MAX_USERNAME_LENGTH:50,MIN_PASSWORD_LENGTH:8,MAX_PASSWORD_LENGTH:128},u={AUTH_TOKEN:"authToken",USER_PREFERENCES:"userPreferences",DRAFT_CONTENT:"draftContent",THEME:"theme"},d={NETWORK_ERROR:"Network error. Please check your connection.",UNAUTHORIZED:"Your session has expired. Please log in again.",FORBIDDEN:"Access denied.",NOT_FOUND:"The requested resource was not found.",VALIDATION_ERROR:"Please check your input and try again.",GENERATION_FAILED:"Content generation failed. Please try again.",RATE_LIMIT_EXCEEDED:"Rate limit exceeded. Please try again later.",GENERIC_ERROR:"Something went wrong. Please try again."},h={OK:200,CREATED:201,NO_CONTENT:204,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,CONFLICT:409,UNPROCESSABLE_ENTITY:422,TOO_MANY_REQUESTS:429,INTERNAL_SERVER_ERROR:500,SERVICE_UNAVAILABLE:503},E={EMAIL:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,USERNAME:/^[a-zA-Z0-9_]+$/,PASSWORD:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/,HASHTAG:/#\w+/g,MENTION:/@\w+/g,URL:/https?:\/\/[^\s]+/g},m=e=>{switch(e){case h.UNAUTHORIZED:return d.UNAUTHORIZED;case h.FORBIDDEN:return d.FORBIDDEN;case h.NOT_FOUND:return d.NOT_FOUND;case h.UNPROCESSABLE_ENTITY:return d.VALIDATION_ERROR;case h.TOO_MANY_REQUESTS:return d.RATE_LIMIT_EXCEEDED;case h.INTERNAL_SERVER_ERROR:case h.SERVICE_UNAVAILABLE:return d.GENERATION_FAILED;default:return d.GENERIC_ERROR}}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})}},e=>{var t=t=>e(e.s=t);e.O(0,[464,441,684,358],()=>t(3057)),_N_E=e.O()}]);