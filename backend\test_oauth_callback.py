#!/usr/bin/env python3
"""
Test script to simulate Twitter OAuth callback
"""
import requests
import json

def test_oauth_callback():
    """Test the OAuth callback endpoint with sample data"""
    
    # First, get the init data
    print("Getting OAuth init data...")
    init_response = requests.post("http://localhost:8000/api/auth/oauth2/twitter/init")
    
    if init_response.status_code != 200:
        print(f"Init failed: {init_response.status_code} - {init_response.text}")
        return
    
    init_data = init_response.json()
    print(f"Init successful. State: {init_data['state']}")
    print(f"Code verifier: {init_data['code_verifier']}")
    
    # Simulate callback data (this would normally come from Twitter)
    # Note: This will fail because we don't have a real authorization code from Twitter
    # But it will help us see if the database operations work
    callback_data = {
        "code": "fake_authorization_code_for_testing",
        "state": init_data['state'],
        "code_verifier": init_data['code_verifier']
    }
    
    print("\nTesting callback endpoint...")
    callback_response = requests.post(
        "http://localhost:8000/api/auth/oauth2/twitter/callback",
        json=callback_data
    )
    
    print(f"Callback response status: {callback_response.status_code}")
    print(f"Callback response: {callback_response.text}")

if __name__ == "__main__":
    test_oauth_callback()
